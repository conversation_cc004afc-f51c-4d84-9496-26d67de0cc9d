/* Global styles */
:root {
  --bg-main: #f3f3f3;
  --sidebar-width: 260px;
  --nav-item-height: 44px;
  --nav-item-padding: 0 24px;
  --nav-item-font-size: 15.38px;
  --primary-color: #199A8E;
  --primary-color-80: rgba(25, 154, 142, 0.80);
  --text-primary: #111827;
  --text-secondary: #6B7280;
  --bg-white: #ffffff;
  --bg-light: #F9FAFB;
  --bg-icon-light: #F0F9FF;
  --border-color: #E5E7EB;
  --shadow-sm: 0px 1px 2px rgba(0, 0, 0, 0.05);
  --border-radius-lg: 12px;
  --border-radius-md: 8px;
  --border-radius-full: 9999px;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--bg-main);
}

/* Angular specific */
app-root {
  display: block;
  min-height: 100vh;
  background-color: var(--bg-main);
}

/* Font settings */
* {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Common component styles */
.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  color: var(--text-secondary);
}

.active .nav-icon {
  color: var(--primary-color);
}

.nav-item {
  display: flex;
  align-items: center;
  height: 44px;
  padding: 0 24px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
}

.nav-item.active {
  background: var(--bg-icon-light);
  color: var(--primary-color);
}

.sidebar {
  width: 256px;
  height: 100%;
  background: var(--bg-white);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.logo .primary {
  color: var(--primary-color);
  font-weight: 700;
}

.logo .secondary {
  color: var(--primary-color-80);
  font-weight: 400;
}

/* Prevent FOUC (Flash of Unstyled Content) */
body {
  opacity: 1;
  transition: none !important;
}

.dashboard-container {
  opacity: 1;
  transition: none !important;
}

/* Scroll to top button styles */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: var(--primary-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1000;
  border: none;
}

.scroll-to-top i {
  color: white;
  font-size: 20px;
  transition: transform 0.3s ease;
}

.scroll-to-top:hover {
  background-color: var(--primary-color-80);
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.scroll-to-top:hover i {
  transform: translateY(-2px);
}
