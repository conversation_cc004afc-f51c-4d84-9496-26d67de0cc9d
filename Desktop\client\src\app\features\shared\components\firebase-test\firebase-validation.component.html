<div class="container mt-5">
  <div class="card">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
      <h2>Firebase Rules Validation</h2>
      <div>
        <a routerLink="/firebase-test" class="btn btn-outline-light me-2">Basic Test</a>
        <a routerLink="/" class="btn btn-outline-light">Home</a>
      </div>
    </div>
    <div class="card-body">
      <div *ngIf="!currentUser" class="alert alert-warning">
        <h4>Not Authenticated</h4>
        <p>Please log in first to test Firebase rules.</p>
      </div>

      <div *ngIf="currentUser" class="alert alert-info">
        <h4>Authenticated as:</h4>
        <p>Email: {{ currentUser.email }}</p>
        <p>UID: {{ currentUser.uid }}</p>
      </div>

      <div class="card mt-3">
        <div class="card-header">
          <h4>Troubleshooting Guide</h4>
        </div>
        <div class="card-body">
          <p>If you're having permission issues, try these steps:</p>
          <ol>
            <li>Make sure your user has a document in the <code>users</code> collection with your UID</li>
            <li>Try accessing the <code>public_test</code> collection which has looser permissions</li>
            <li>Check if your security rules are deployed correctly</li>
          </ol>
          <div class="alert alert-secondary">
            <p><strong>Currently using Firebase project:</strong> medsecura-5abb2</p>
            <p class="mb-0"><strong>Authentication status:</strong> {{currentUser ? 'Authenticated' : 'Not authenticated'}}</p>
          </div>
          <button class="btn btn-primary" (click)="createPublicTestDoc()">
            Create Public Test Document
          </button>
        </div>
      </div>

      <div class="card mt-3">
        <div class="card-body">
          <h4>Test Specific Permissions</h4>

          <div class="mb-3">
            <label class="form-label">Operation</label>
            <select class="form-select" [(ngModel)]="testOperation">
              <option value="read">Read Document</option>
              <option value="write">Write Document</option>
              <option value="update">Update Document</option>
              <option value="delete">Delete Document</option>
            </select>
          </div>

          <div class="mb-3">
            <label class="form-label">Collection</label>
            <select class="form-select" [(ngModel)]="testCollection">
              <option value="users">Users</option>
              <option value="public_test">Public Test</option>
              <option value="appointments">Appointments</option>
              <option value="doctorPatients">Doctor-Patient Relationships</option>
              <option value="medicalRecords">Medical Records</option>
              <option value="firebase_test">Test Collection</option>
            </select>
          </div>

          <div class="mb-3">
            <label class="form-label">Document ID (leave empty for new doc)</label>
            <input type="text" class="form-control" [(ngModel)]="testDocId">
          </div>

          <button class="btn btn-primary" (click)="runValidationTest()" [disabled]="!currentUser">
            Run Test
          </button>
        </div>
      </div>

      <div *ngIf="results.length > 0" class="mt-4">
        <h4>Test Results:</h4>
        <div class="list-group">
          <div *ngFor="let result of results"
               class="list-group-item"
               [ngClass]="{'list-group-item-success': result.success, 'list-group-item-danger': !result.success}">
            <div class="d-flex justify-content-between">
              <strong>{{ result.operation }}</strong>
              <span>{{ result.path }}</span>
              <span>{{ result.success ? 'Success' : 'Failed' }}</span>
            </div>
            <div *ngIf="result.error" class="text-danger mt-2">
              Error: {{ result.error }}
            </div>
            <div *ngIf="result.data" class="mt-2">
              <pre>{{ result.data | json }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
