import{c as Q,d as I,f as E,g as G,k as pt,l as ct}from"./chunk-WOUCJQGE.js";import{a as mt}from"./chunk-B62BIBRO.js";import{$ as P,A as k,Bc as lt,D as u,E as b,Ha as Y,Ic as st,Ja as U,K as M,Lc as dt,O as p,Oa as q,P as T,R as j,S as f,U as g,W as O,Z as x,_ as $,a as z,aa as w,ab as H,b as L,ba as i,bb as K,ca as a,cb as J,da as c,ea as D,fa as F,g as W,ga as v,ha as d,ia as m,ic as X,kc as Z,lc as tt,nc as et,oa as r,oc as nt,p as N,pa as A,pc as it,qa as V,r as B,ra as R,s as S,sa as h,ta as C,ua as y,uc as ot,vc as at,wc as rt}from"./chunk-YV65XDJO.js";var _t=(s,n)=>n.date,bt=(s,n)=>n.id;function ft(s,n){s&1&&(i(0,"div",48)(1,"div",68),c(2,"i",69),a(),i(3,"h3"),r(4,"No Appointments Found"),a(),i(5,"p"),r(6,"There are no appointments matching your current filters."),a()())}function vt(s,n){if(s&1){let t=v();i(0,"a",81),d("click",function(){u(t);let l=m().$implicit,o=m(4);return b(o.updateAppointmentStatus(l.id,"Approved"))}),c(1,"i",82),r(2," Accept "),a(),i(3,"a",83),d("click",function(){u(t);let l=m().$implicit,o=m(4);return b(o.updateAppointmentStatus(l.id,"Cancelled"))}),c(4,"i",84),r(5," Reject "),a()}}function ht(s,n){if(s&1){let t=v();i(0,"a",81),d("click",function(){u(t);let l=m().$implicit,o=m(4);return b(o.updateAppointmentStatus(l.id,"Completed"))}),c(1,"i",85),r(2," Complete "),a(),i(3,"a",83),d("click",function(){u(t);let l=m().$implicit,o=m(4);return b(o.updateAppointmentStatus(l.id,"Cancelled"))}),c(4,"i",84),r(5," Cancel "),a()}}function Ct(s,n){if(s&1){let t=v();i(0,"a",83),d("click",function(){u(t);let l=m().$implicit,o=m(4);return b(o.deleteAppointment(l.id))}),c(1,"i",86),r(2," Delete "),a()}}function yt(s,n){if(s&1&&(i(0,"div",77)(1,"div",76),r(2),a(),i(3,"div",76),r(4),a(),i(5,"div",76)(6,"span",78),r(7),a()(),i(8,"div",76)(9,"div",79),f(10,vt,6,0)(11,ht,6,0)(12,Ct,3,0,"a",80),a()()()),s&2){let t=n.$implicit;p(2),A(t.time),p(2),A(t.patientName),p(2),g("ngClass",t.status.toLowerCase()),p(),A(t.status),p(3),x(t.status==="Pending"?10:-1),p(),x(t.status==="Approved"?11:-1),p(),x(t.status==="Completed"||t.status==="Cancelled"?12:-1)}}function xt(s,n){if(s&1&&(i(0,"div",74)(1,"div",75)(2,"div",76),r(3,"Time"),a(),i(4,"div",76),r(5,"Patient"),a(),i(6,"div",76),r(7,"Status"),a(),i(8,"div",76),r(9,"Actions"),a()(),P(10,yt,13,7,"div",77,$),a()),s&2){let t=m().$implicit;g("@expandCollapse",void 0),p(10),w(t.appointments)}}function At(s,n){if(s&1){let t=v();i(0,"div",70)(1,"div",71),d("click",function(){let l=u(t).$implicit,o=m(2);return b(o.toggleAppointmentDateGroup(l.date))}),c(2,"i",72),i(3,"h3"),r(4),a(),i(5,"span",73),r(6),a()(),f(7,xt,12,1,"div",74),a()}if(s&2){let t=n.$implicit,e=m(2);p(2),O("bi-chevron-down",e.isAppointmentDateGroupOpen(t.date))("bi-chevron-right",!e.isAppointmentDateGroupOpen(t.date)),p(2),A(t.date),p(2),V("",t.appointments.length," appointments"),p(),x(e.isAppointmentDateGroupOpen(t.date)?7:-1)}}function St(s,n){if(s&1&&P(0,At,8,7,"div",70,_t),s&2){let t=m();w(t.groupedAppointments())}}function Mt(s,n){s&1&&(i(0,"span"),c(1,"i",87),r(2," Saving... "),a())}function Ot(s,n){s&1&&(i(0,"span"),r(1,"Set Availability"),a())}function Pt(s,n){s&1&&(i(0,"div",88),c(1,"div",89),i(2,"p"),r(3,"Loading availability..."),a()())}function wt(s,n){if(s&1){let t=v();i(0,"div",48)(1,"div",68),c(2,"i",69),a(),i(3,"h3"),r(4,"No Availability Set"),a(),i(5,"p"),r(6,"You haven't set any availability slots yet."),a(),i(7,"div",90)(8,"button",28),d("click",function(){u(t);let l=m();return l.closeViewAvailabilityModal(),b(l.openAvailabilityModal())}),c(9,"i",91),r(10," Set Availability Now "),a()()()}}function kt(s,n){s&1&&(D(0),c(1,"i",87),r(2," Converting... "),F())}function Tt(s,n){s&1&&(D(0),r(1," Convert to Appointments "),F())}function Et(s,n){if(s&1){let t=v();i(0,"div",97)(1,"div",98),c(2,"i",99),i(3,"span"),r(4),a()(),i(5,"button",100),d("click",function(){let l=u(t).$implicit,o=m(4);return b(o.removeAvailability(l.id))}),c(6,"i",86),a()()}if(s&2){let t=n.$implicit;p(4),R("",t.start_time," - ",t.end_time,"")}}function Dt(s,n){if(s&1&&(i(0,"div",95)(1,"div",96),P(2,Et,7,2,"div",97,bt),a()()),s&2){let t=m().$implicit;p(2),w(t.slots)}}function Ft(s,n){if(s&1){let t=v();i(0,"div",70)(1,"div",71),d("click",function(){let l=u(t).$implicit,o=m(2);return b(o.toggleDateGroup(l.date))}),c(2,"i",72),i(3,"h3"),r(4),a(),i(5,"span",92),r(6),a(),i(7,"div",23)(8,"button",93),d("click",function(l){let o=u(t).$implicit,_=m(2);return l.stopPropagation(),b(_.convertAvailabilityToAppointments(o.date))}),f(9,kt,3,0,"ng-container",66)(10,Tt,2,0,"ng-container",66),a()()(),f(11,Dt,4,0,"div",94),a()}if(s&2){let t=n.$implicit,e=m(2);p(2),O("bi-chevron-down",e.isDateGroupOpen(t.date))("bi-chevron-right",!e.isDateGroupOpen(t.date)),p(2),A(t.date),p(2),V("",t.slots.length," slots"),p(2),g("disabled",e.convertingDates.has(t.date)),p(),g("ngIf",e.convertingDates.has(t.date)),p(),g("ngIf",!e.convertingDates.has(t.date)),p(),g("ngIf",e.isDateGroupOpen(t.date))}}function Vt(s,n){if(s&1&&P(0,Ft,12,10,"div",70,_t),s&2){let t=m();w(t.groupedAvailability())}}var gt=class s{constructor(n,t,e){this.appointmentsService=n;this.router=t;this.authService=e;this.searchSub=this.searchSubject.pipe(N(300),B()).subscribe(l=>{this.searchTerm=l,this.applyFilters()})}appointments=[];filteredAppointments=M([]);availableSlots=[];groupedAvailableSlots={};groupedAppointments=M([]);groupedAvailability=M([]);openDateGroups=new Set;openAppointmentDateGroups=new Set;db=k(st);securaService=k(mt);appointmentsData=M([]);selectedAppointment=null;isEditingAppointment=!1;isCreatingAppointment=!1;showAvailableSlotsModal=!1;isLoading=!0;error=null;searchTerm="";filter="all";slotFilterDate="";appointmentViewFilter="all";appointmentSortOrder="newest";startTime="";endTime="";availabilityDate="";showSetAvailabilityModal=!1;showViewAvailabilityModal=!1;searchSubject=new W;appointmentsSub=null;searchSub=null;firebaseService=k(dt);savingAvailability=!1;loadingAvailability=!1;convertingDates=new Set;ngOnInit(){this.loadAppointments(),this.loadAvailabilityFromFirebase(),setTimeout(()=>{let n=this.groupedAppointments();n.length>0&&this.openAppointmentDateGroups.add(n[0].date);let t=this.groupedAvailability();t.length>0&&this.openDateGroups.add(t[0].date)},100),this.firebaseService.availability$.subscribe(n=>{n&&n.length>0&&this.groupAvailabilityByDate(n)})}ngOnDestroy(){this.appointmentsSub&&this.appointmentsSub.unsubscribe(),this.searchSub&&this.searchSub.unsubscribe()}loadAppointments(){let n=this.db.current_doctor()?.id+"";this.appointmentsData.set(this.securaService.doctorAppoitments(n)),this.filterAppointments()}loadAvailabilityFromFirebase(){this.loadingAvailability=!0;let n=this.db.current_doctor()?.id||"";if(!n){console.error("No doctor ID available"),this.loadingAvailability=!1;return}console.log(`Loading availability for doctor ID: ${n}`),this.firebaseService.syncAvailabilityData(n).pipe(S(()=>{console.log("Synchronization complete, now loading availability")})).subscribe({next:()=>{this.firebaseService.loadDoctorAvailability(n).pipe(S(()=>this.loadingAvailability=!1)).subscribe({next:t=>{console.log(`Loaded ${t.length} availability slots from Firebase after sync`),t.length>0?(console.log("Sample availability data:",t[0]),this.groupAvailabilityByDate(t)):(console.warn("No availability slots found in Firebase after sync"),this.groupedAvailability.set([]))},error:t=>{console.error("Error loading availability from Firebase after sync:",t),this.groupedAvailability.set([])}})},error:t=>{console.error("Error during availability synchronization:",t),this.loadingAvailability=!1,console.log("Falling back to direct loading without sync"),this.firebaseService.loadDoctorAvailability(n).subscribe({next:e=>{this.groupAvailabilityByDate(e)},error:e=>{console.error("Error loading availability directly:",e),this.groupedAvailability.set([])}})}})}synchronizeFirebaseWithLocalStorage(n){let t=this.db.availabilityTable().filter(e=>e.doctor_id===n);if(t.length===0){console.log("No local availability slots to synchronize to Firebase");return}console.log(`Found ${t.length} local availability slots to check for Firebase`);for(let e of t){if(e.id&&e.id.length>10)continue;let l={doctor_id:e.doctor_id,date:e.date,start_time:e.start_time,end_time:e.end_time};this.firebaseService.addAvailability(l).subscribe({next:o=>{console.log(`Added local slot to Firebase with ID: ${o}`),this.db.removeAvailability(e.id);let _=L(z({},e),{id:o});this.db.addAvailability(_)},error:o=>{console.error("Error adding local slot to Firebase:",o)}})}}groupAvailabilityByDate(n){let t=n||this.db.availabilityTable(),e={};t.forEach(o=>{e[o.date]||(e[o.date]=[]),e[o.date].push(o)});let l=Object.keys(e).sort((o,_)=>new Date(o).getTime()-new Date(_).getTime()).map(o=>({date:o,slots:e[o].sort((_,ut)=>_.start_time.localeCompare(ut.start_time))}));this.groupedAvailability.set(l)}loadAvailableSlots(){this.availableSlots=this.appointments.filter(n=>n.status==="Available"&&!n.patientId),this.groupAvailableSlotsByDate()}groupAvailableSlotsByDate(){this.groupedAvailableSlots={},this.availableSlots.forEach(n=>{let t=new Date(n.appointmentDate).toLocaleDateString();this.groupedAvailableSlots[t]||(this.groupedAvailableSlots[t]=[]),this.groupedAvailableSlots[t].push(n)}),Object.keys(this.groupedAvailableSlots).forEach(n=>{this.groupedAvailableSlots[n].sort((t,e)=>t.appointmentTime.localeCompare(e.appointmentTime))})}filterAvailableSlots(){if(!this.slotFilterDate){this.loadAvailableSlots();return}let n=new Date(this.slotFilterDate).toLocaleDateString(),t={};this.groupedAvailableSlots[n]&&(t[n]=this.groupedAvailableSlots[n]),this.groupedAvailableSlots=t}onSearch(n){this.searchSubject.next(n)}applyFilters(){if(!this.appointments||this.appointments.length===0){this.filteredAppointments.set([]),this.groupAppointmentsByDate();return}let n=[...this.appointments];if(this.filter!=="all"&&(n=n.filter(t=>t.status===this.filter)),this.searchTerm.trim()){let t=this.searchTerm.toLowerCase();n=n.filter(e=>e.patientName?.toLowerCase().includes(t)||!1||e.reasonForVisit?.toLowerCase().includes(t)||!1)}this.groupAppointmentsByDate()}filterAppointments(){let t=[...this.appointmentsData()],e=new Date().toISOString().split("T")[0];if(this.appointmentViewFilter==="today"?t=t.filter(l=>l.date===e):this.appointmentViewFilter==="upcoming"?t=t.filter(l=>l.date>=e):this.appointmentViewFilter==="past"&&(t=t.filter(l=>l.date<e)),this.filter!=="all"&&(t=t.filter(l=>l.status===this.filter)),this.searchTerm.trim()){let l=this.searchTerm.toLowerCase();t=t.filter(o=>o.patientName&&o.patientName.toLowerCase().includes(l)||o.date&&o.date.includes(l))}this.filteredAppointments.set(t),this.groupAppointmentsByDate()}sortAppointments(){this.groupAppointmentsByDate()}groupAppointmentsByDate(){let n=this.filteredAppointments(),t={};n.forEach(o=>{t[o.date]||(t[o.date]=[]),t[o.date].push(o)});let e=Object.keys(t);this.appointmentSortOrder==="newest"?e.sort((o,_)=>new Date(_).getTime()-new Date(o).getTime()):e.sort((o,_)=>new Date(o).getTime()-new Date(_).getTime());let l=e.map(o=>({date:o,appointments:t[o]}));this.groupedAppointments.set(l)}getCurrentDate(){return new Date().toISOString().split("T")[0]}openAvailabilityModal(){this.showSetAvailabilityModal=!0}closeSetAvailabilityModal(){this.showSetAvailabilityModal=!1,this.availabilityDate="",this.startTime="",this.endTime=""}openViewAvailabilityModal(){this.showViewAvailabilityModal=!0}closeViewAvailabilityModal(){this.showViewAvailabilityModal=!1}onSetSchedule(){if(!this.availabilityDate||!this.startTime||!this.endTime){alert("Please fill in all fields");return}if(this.startTime>=this.endTime){alert("End time must be after start time");return}let n=this.db.current_doctor()?.id||"";if(!n){alert("No doctor ID available");return}this.savingAvailability=!0;let t={doctor_id:n,date:this.availabilityDate,start_time:this.startTime,end_time:this.endTime};this.firebaseService.addAvailability(t).pipe(S(()=>this.savingAvailability=!1)).subscribe({next:e=>{console.log("Availability added to Firebase with ID:",e),this.closeSetAvailabilityModal(),this.loadAvailabilityFromFirebase()},error:e=>{console.error("Error adding availability to Firebase:",e),alert("Failed to set availability. Please try again.")}})}removeAvailability(n){this.firebaseService.removeAvailability(n).subscribe({next:()=>{console.log("Availability removed from Firebase"),this.db.removeAvailability(n)},error:t=>{console.error("Error removing availability from Firebase:",t),this.db.removeAvailability(n)}}),this.loadAvailabilityFromFirebase()}convertAvailabilityToAppointments(n){let t=this.db.current_doctor()?.id||"";if(!t){alert("No doctor ID available");return}this.convertingDates.add(n),this.firebaseService.convertAvailabilityToAppointments(t,n).pipe(S(()=>{this.convertingDates.delete(n)})).subscribe({next:()=>{console.log("Availability converted to appointments"),this.loadAvailabilityFromFirebase(),setTimeout(()=>{this.loadAppointments()},500)},error:e=>{console.error("Error converting availability:",e),alert("Failed to convert availability to appointments. Please try again.")}})}updateAppointmentStatus(n,t){this.firebaseService.updateAppointmentStatus(n,t).subscribe({next:()=>{console.log("Appointment status updated in Firebase"),this.db.updateAppointmentStatus(n,t),this.loadAppointments()},error:e=>{console.error("Error updating appointment in Firebase:",e),this.db.updateAppointmentStatus(n,t),this.loadAppointments()}})}deleteAppointment(n){let t=this.appointmentsData().find(e=>e.id===n);t&&(t.status==="Cancelled"||t.status==="Completed")&&(this.appointmentsData.update(e=>e.filter(l=>l.id!==n)),this.db.deleteAppointment(n),this.filterAppointments())}toggleDateGroup(n){this.openDateGroups.has(n)?this.openDateGroups.delete(n):this.openDateGroups.add(n)}isDateGroupOpen(n){return this.openDateGroups.has(n)}toggleAppointmentDateGroup(n){this.openAppointmentDateGroups.has(n)?this.openAppointmentDateGroups.delete(n):this.openAppointmentDateGroups.add(n)}isAppointmentDateGroupOpen(n){return this.openAppointmentDateGroups.has(n)}setFilter(n){this.filter=n,this.filterAppointments()}logout(){this.authService.logout(),this.router.navigate(["/login"])}handleSearchInput(n){let t=n.target;this.onSearch(t.value)}static \u0275fac=function(t){return new(t||s)(T(ct),T(H),T(pt))};static \u0275cmp=j({type:s,selectors:[["app-appointments"]],decls:132,vars:19,consts:[[1,"page-container"],[1,"sidebar"],[1,"logo-section"],[1,"logo"],[1,"primary"],[1,"secondary"],[1,"nav-menu"],["routerLink","/dashboard","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-grid-1x2-fill","nav-icon"],["routerLink","/doctors-patient","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-people-fill","nav-icon"],["routerLink","/doctors","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-person-badge-fill","nav-icon"],["routerLink","/appointments","routerLinkActive","active",1,"nav-item"],[1,"bi","bi-calendar2-week-fill","nav-icon"],["routerLink","/settings","routerLinkActive","active",1,"nav-item","active"],[1,"bi","bi-gear-fill","nav-icon"],[1,"logout-section"],[1,"logout-button",3,"click"],[1,"bi","bi-box-arrow-right","nav-icon"],[1,"main-content"],[1,"content-card"],[1,"card-header"],[1,"header-actions"],[1,"search-bar"],[1,"bi","bi-search"],["type","text","placeholder","Search appointments...",3,"ngModelChange","input","ngModel"],[1,"action-buttons"],[1,"btn","primary",3,"click"],[1,"bi","bi-calendar-plus"],[1,"btn","secondary",3,"click"],[1,"bi","bi-calendar-week"],[1,"table-section"],[1,"section-header"],[1,"section-header-content"],[1,"filter-group"],[3,"ngModelChange","change","ngModel"],["value","all"],["value","today"],["value","upcoming"],["value","past"],["value","Pending"],["value","Approved"],["value","Completed"],["value","Cancelled"],["value","newest"],["value","oldest"],[1,"date-groups"],[1,"empty-state"],[1,"modal"],[1,"modal-content"],[1,"modal-header"],[1,"close-btn",3,"click"],[1,"bi","bi-x-lg"],[1,"modal-body"],[1,"availability-form"],[1,"form-group"],["for","date"],["type","date","id","date","name","date",1,"form-control",3,"ngModelChange","ngModel","min"],["for","startTime"],["type","time","id","startTime","name","startTime",1,"form-control",3,"ngModelChange","ngModel"],["for","endTime"],["type","time","id","endTime","name","endTime",1,"form-control",3,"ngModelChange","ngModel"],[1,"form-actions"],["type","button",1,"btn","secondary",3,"click","disabled"],["type","submit",1,"btn","primary",3,"click","disabled"],[4,"ngIf"],["class","loading-container",4,"ngIf"],[1,"empty-icon"],[1,"bi","bi-calendar-x"],[1,"date-group"],[1,"date-header",3,"click"],[1,"bi"],[1,"appointment-count"],[1,"table-container"],[1,"table-header"],[1,"table-cell"],[1,"table-row"],[1,"status-badge",3,"ngClass"],[1,"action-links"],[1,"action-link","danger"],[1,"action-link","primary",3,"click"],[1,"bi","bi-check-circle"],[1,"action-link","danger",3,"click"],[1,"bi","bi-x-circle"],[1,"bi","bi-check-square"],[1,"bi","bi-trash"],[1,"bi","bi-arrow-repeat","spinning"],[1,"loading-container"],[1,"spinner"],[1,"empty-state-action"],[1,"bi","bi-plus-circle"],[1,"slot-count"],[1,"btn-sm","convert-btn",3,"click","disabled"],["class","date-content",4,"ngIf"],[1,"date-content"],[1,"time-slots"],[1,"time-slot"],[1,"time-info"],[1,"bi","bi-clock"],[1,"remove-btn",3,"click"]],template:function(t,e){t&1&&(i(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"span",4),r(5,"Med"),a(),i(6,"span",5),r(7,"Secura"),a()()(),i(8,"nav",6)(9,"a",7),c(10,"i",8),i(11,"span"),r(12,"Dashboard"),a()(),i(13,"a",9),c(14,"i",10),i(15,"span"),r(16,"Patients"),a()(),i(17,"a",11),c(18,"i",12),i(19,"span"),r(20,"Doctors"),a()(),i(21,"a",13),c(22,"i",14),i(23,"span"),r(24,"Appointments"),a()(),i(25,"a",15),c(26,"i",16),i(27,"span"),r(28,"Settings"),a()()(),i(29,"div",17)(30,"a",18),d("click",function(){return e.logout()}),c(31,"i",19),i(32,"span"),r(33,"Logout"),a()()()(),i(34,"div",20)(35,"div",21)(36,"div",22)(37,"h1"),r(38,"Appointments Management"),a(),i(39,"div",23)(40,"div",24),c(41,"i",25),i(42,"input",26),y("ngModelChange",function(o){return C(e.searchTerm,o)||(e.searchTerm=o),o}),d("input",function(o){return e.handleSearchInput(o)}),a()(),i(43,"div",27)(44,"button",28),d("click",function(){return e.openAvailabilityModal()}),c(45,"i",29),r(46," Set Availability "),a(),i(47,"button",30),d("click",function(){return e.openViewAvailabilityModal()}),c(48,"i",31),r(49," View Availability "),a()()()(),i(50,"div",32)(51,"div",33)(52,"div",34)(53,"h2"),r(54,"My Appointments"),a(),i(55,"div",23)(56,"div",35)(57,"label"),r(58,"View:"),a(),i(59,"select",36),y("ngModelChange",function(o){return C(e.appointmentViewFilter,o)||(e.appointmentViewFilter=o),o}),d("change",function(){return e.filterAppointments()}),i(60,"option",37),r(61,"All"),a(),i(62,"option",38),r(63,"Today"),a(),i(64,"option",39),r(65,"Upcoming"),a(),i(66,"option",40),r(67,"Past"),a()()(),i(68,"div",35)(69,"label"),r(70,"Status:"),a(),i(71,"select",36),y("ngModelChange",function(o){return C(e.filter,o)||(e.filter=o),o}),d("change",function(){return e.filterAppointments()}),i(72,"option",37),r(73,"All"),a(),i(74,"option",41),r(75,"Pending"),a(),i(76,"option",42),r(77,"Approved"),a(),i(78,"option",43),r(79,"Completed"),a(),i(80,"option",44),r(81,"Cancelled"),a()()(),i(82,"div",35)(83,"label"),r(84,"Sort:"),a(),i(85,"select",36),y("ngModelChange",function(o){return C(e.appointmentSortOrder,o)||(e.appointmentSortOrder=o),o}),d("change",function(){return e.sortAppointments()}),i(86,"option",45),r(87,"Newest First"),a(),i(88,"option",46),r(89,"Oldest First"),a()()()()()(),i(90,"div",47),f(91,ft,7,0,"div",48)(92,St,2,0),a()()()()(),i(93,"div",49)(94,"div",50)(95,"div",51)(96,"h2"),r(97,"Set Availability"),a(),i(98,"button",52),d("click",function(){return e.closeSetAvailabilityModal()}),c(99,"i",53),a()(),i(100,"div",54)(101,"form",55)(102,"div",56)(103,"label",57),r(104,"Date"),a(),i(105,"input",58),y("ngModelChange",function(o){return C(e.availabilityDate,o)||(e.availabilityDate=o),o}),a()(),i(106,"div",56)(107,"label",59),r(108,"Start Time"),a(),i(109,"input",60),y("ngModelChange",function(o){return C(e.startTime,o)||(e.startTime=o),o}),a()(),i(110,"div",56)(111,"label",61),r(112,"End Time"),a(),i(113,"input",62),y("ngModelChange",function(o){return C(e.endTime,o)||(e.endTime=o),o}),a()(),i(114,"div",63)(115,"button",64),d("click",function(){return e.closeSetAvailabilityModal()}),r(116,"Cancel"),a(),i(117,"button",65),d("click",function(){return e.onSetSchedule()}),f(118,Mt,3,0,"span",66)(119,Ot,2,0,"span",66),a()()()()()(),i(120,"div",49)(121,"div",50)(122,"div",51)(123,"h2"),r(124,"My Availability"),a(),i(125,"button",52),d("click",function(){return e.closeViewAvailabilityModal()}),c(126,"i",53),a()(),i(127,"div",54)(128,"div",47),f(129,Pt,4,0,"div",67)(130,wt,11,0,"div",48)(131,Vt,2,0),a()()()()),t&2&&(p(42),h("ngModel",e.searchTerm),p(17),h("ngModel",e.appointmentViewFilter),p(12),h("ngModel",e.filter),p(14),h("ngModel",e.appointmentSortOrder),p(6),x(e.filteredAppointments().length===0?91:92),p(2),O("show",e.showSetAvailabilityModal),p(12),h("ngModel",e.availabilityDate),g("min",e.getCurrentDate()),p(4),h("ngModel",e.startTime),p(4),h("ngModel",e.endTime),p(2),g("disabled",e.savingAvailability),p(2),g("disabled",e.savingAvailability),p(),g("ngIf",e.savingAvailability),p(),g("ngIf",!e.savingAvailability),p(),O("show",e.showViewAvailabilityModal),p(9),g("ngIf",e.loadingAvailability),p(),x(e.groupedAvailability().length===0&&!e.loadingAvailability?130:131))},dependencies:[q,Y,U,lt,it,at,rt,X,ot,Z,tt,nt,et,K,J],styles:["[_nghost-%COMP%]{--primary-color: #199A8E;--primary-color-80: rgba(25, 154, 142, .8);--primary-hover: #168176;--primary-bg-hover: rgba(25, 154, 142, .08);--text-primary: #111827;--text-secondary: #6B7280;--text-success: #166534;--bg-white: #ffffff;--bg-light: #F9FAFB;--bg-hover: #F3F4F6;--border-color: #E5E7EB;--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / .05);--shadow-md: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--border-radius-lg: 16px;--border-radius-md: 12px;--danger-color: #DC2626;--danger-hover: #B91C1C;--danger-bg: #FEF2F2;--danger-bg-hover: #FEE2E2;--transition-fast: .15s ease;--transition-normal: .25s cubic-bezier(.4, 0, .2, 1);--transition-bounce: .5s cubic-bezier(.68, -.55, .265, 1.55)}[_nghost-%COMP%]{display:flex;width:100vw;height:100vh;overflow:hidden}.page-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%}.sidebar[_ngcontent-%COMP%]{width:256px;height:100vh;background:var(--bg-white);border-right:1px solid var(--border-color);display:flex;flex-direction:column;position:fixed;left:0;top:0;z-index:100}.logo-section[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logo[_ngcontent-%COMP%]{font-size:22.88px;line-height:32px}.logo[_ngcontent-%COMP%]   .primary[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700}.logo[_ngcontent-%COMP%]   .secondary[_ngcontent-%COMP%]{color:var(--primary-color-80);font-weight:400}.nav-menu[_ngcontent-%COMP%]{padding:16px 0;flex:1}.nav-item[_ngcontent-%COMP%]{display:flex;align-items:center;height:44px;padding:0 24px;color:var(--text-primary);font-size:15.38px;cursor:pointer;text-decoration:none}.nav-item[_ngcontent-%COMP%]:hover{background:var(--bg-light)}.nav-item.active[_ngcontent-%COMP%]{background:var(--bg-icon-light);color:var(--primary-color)}.nav-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:12px;color:var(--text-secondary)}.active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%]{color:var(--primary-color)}.logout-section[_ngcontent-%COMP%]{height:44px;border-top:1px solid var(--border-color);display:flex;align-items:center;padding:0 24px}.logout-button[_ngcontent-%COMP%]{display:flex;align-items:center;color:var(--text-primary);font-size:15.25px;cursor:pointer;text-decoration:none}.main-content[_ngcontent-%COMP%]{margin-left:256px;flex:1;height:100vh;overflow-y:auto;background:var(--bg-light);transition:var(--transition-all)}.content-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;box-shadow:0 2px 4px #0000000d;padding:24px}.card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:24px}.header-actions[_ngcontent-%COMP%]{display:flex;gap:16px;align-items:center}.search-bar[_ngcontent-%COMP%]{position:relative;width:300px}.search-bar[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:12px;top:50%;transform:translateY(-50%);color:#666}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:10px 12px 10px 35px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px}.action-buttons[_ngcontent-%COMP%]{display:flex;gap:12px}.btn[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:8px;padding:10px 16px;border:none;border-radius:8px;font-size:14px;font-weight:500;cursor:pointer;transition:all .3s ease}.btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1em}.btn.primary[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff}.btn.primary[_ngcontent-%COMP%]:hover{background-color:#199a8e}.btn.secondary[_ngcontent-%COMP%]{background-color:#e9ecef;color:#495057}.btn.secondary[_ngcontent-%COMP%]:hover{background-color:#dee2e6}.table-section[_ngcontent-%COMP%]{margin-top:24px}.section-header[_ngcontent-%COMP%]{margin-bottom:16px}.section-header-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.filter-group[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.filter-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px;color:#666}.filter-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{padding:8px 12px;border:1px solid #e0e0e0;border-radius:6px;font-size:14px}.table-container[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:8px;overflow:hidden}.table-header[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 2fr 1fr 1fr;background-color:#f8f9fa;padding:12px 16px;font-weight:600;color:#495057}.table-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 2fr 1fr 1fr;padding:12px 16px;border-top:1px solid #e0e0e0}.table-cell[_ngcontent-%COMP%]{display:flex;align-items:center}.status-badge[_ngcontent-%COMP%]{padding:6px 12px;border-radius:20px;font-size:12px;font-weight:500}.status-badge.pending[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404}.status-badge.approved[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724}.status-badge.completed[_ngcontent-%COMP%]{background-color:#cce5ff;color:#004085}.status-badge.cancelled[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24}.action-links[_ngcontent-%COMP%]{display:flex;gap:12px}.action-link[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:4px;padding:6px 12px;border-radius:6px;font-size:13px;cursor:pointer;transition:all .2s ease}.action-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.1em}.action-link.primary[_ngcontent-%COMP%]{color:#199a8e;background-color:#e6f0ff}.action-link.primary[_ngcontent-%COMP%]:hover{background-color:#cce0ff}.action-link.danger[_ngcontent-%COMP%]{color:#dc3545;background-color:#fff5f5}.action-link.danger[_ngcontent-%COMP%]:hover{background-color:#ffe0e0}.date-group[_ngcontent-%COMP%]{margin-bottom:16px}.date-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px;background-color:#f8f9fa;border-radius:8px;cursor:pointer;transition:background-color .2s ease}.date-header[_ngcontent-%COMP%]:hover{background-color:#e9ecef}.date-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;color:#343a40}.appointment-count[_ngcontent-%COMP%], .slot-count[_ngcontent-%COMP%]{font-size:14px;color:#6c757d}.empty-state[_ngcontent-%COMP%]{padding:3rem 1rem;text-align:center;background-color:#f8f9fa;border-radius:.5rem;margin:1rem 0}.empty-icon[_ngcontent-%COMP%]{font-size:3rem;color:#adb5bd;margin-bottom:1rem}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin-bottom:.5rem;font-size:1.25rem;color:#343a40}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:0}.empty-state-action[_ngcontent-%COMP%]{margin-top:1.5rem}.empty-state-action[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.5rem 1rem;font-size:.9rem;border-radius:.25rem;transition:all .2s ease}.empty-state-action[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem}.convert-btn[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed;background-color:#6c757d}.loading-message[_ngcontent-%COMP%]{margin-top:.5rem;color:#6c757d;font-size:.9rem}.modal[_ngcontent-%COMP%]{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;z-index:1000;opacity:0;transition:opacity .3s ease}.modal.show[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;opacity:1}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;width:90%;max-width:600px;max-height:90vh;overflow-y:auto;box-shadow:0 4px 6px #0000001a;transform:scale(.9);transition:transform .3s ease}.modal.show[_ngcontent-%COMP%]   .modal-content[_ngcontent-%COMP%]{transform:scale(1)}.modal-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:20px 24px;border-bottom:1px solid #e0e0e0}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:20px;color:#343a40}.close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:20px;color:#6c757d;cursor:pointer;padding:4px;border-radius:4px;transition:all .2s ease}.close-btn[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;color:#343a40}.modal-body[_ngcontent-%COMP%]{padding:24px}.availability-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#495057}.form-control[_ngcontent-%COMP%]{padding:10px 12px;border:1px solid #e0e0e0;border-radius:8px;font-size:14px;transition:border-color .2s ease}.form-control[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:12px;margin-top:24px}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideIn{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@media (max-width: 1024px){.main-content[_ngcontent-%COMP%], .card-header[_ngcontent-%COMP%], .section-header[_ngcontent-%COMP%], .availability-form[_ngcontent-%COMP%]{padding:16px 24px}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{padding:12px 24px}}@media (max-width: 768px){.sidebar[_ngcontent-%COMP%]{width:0;transform:translate(-100%)}.main-content[_ngcontent-%COMP%]{margin-left:0;padding:16px}.card-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.header-actions[_ngcontent-%COMP%], .search-bar[_ngcontent-%COMP%]{width:100%}.availability-form[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.form-actions[_ngcontent-%COMP%]{justify-content:flex-start}.btn-primary[_ngcontent-%COMP%]{width:100%;justify-content:center}}@media (max-width: 576px){.main-content[_ngcontent-%COMP%]{padding:12px}.card-header[_ngcontent-%COMP%], .section-header[_ngcontent-%COMP%], .availability-form[_ngcontent-%COMP%]{padding:12px 16px}.table-header[_ngcontent-%COMP%], .table-row[_ngcontent-%COMP%]{padding:10px 16px;gap:8px}.header-cell[_ngcontent-%COMP%], .table-cell[_ngcontent-%COMP%]{font-size:12px}.action-link[_ngcontent-%COMP%]{padding:4px 8px;font-size:12px}.action-links[_ngcontent-%COMP%]{flex-direction:column}}@supports (-webkit-touch-callout: none){[_nghost-%COMP%], .sidebar[_ngcontent-%COMP%], .main-content[_ngcontent-%COMP%]{height:-webkit-fill-available}}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:2rem}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid var(--primary-color);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:1rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block;margin-right:.5rem}.time-slots[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;padding:1rem;background-color:#f8f9fa;border-radius:.5rem}.time-slot[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:.75rem 1rem;background-color:#fff;border-radius:.5rem;box-shadow:0 1px 3px #0000001a}.time-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem}.time-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color)}.remove-btn[_ngcontent-%COMP%]{background:none;border:none;color:var(--danger-color);cursor:pointer;padding:.25rem;border-radius:.25rem;transition:background-color .2s}.remove-btn[_ngcontent-%COMP%]:hover{background-color:#dc35451a}.header-actions[_ngcontent-%COMP%]{margin-left:auto;display:flex;align-items:center}.btn-sm[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.75rem;border-radius:.25rem}.convert-btn[_ngcontent-%COMP%]{background-color:var(--accent-color);color:#fff;border:none;cursor:pointer;transition:background-color .2s}.convert-btn[_ngcontent-%COMP%]:hover{background-color:var(--accent-color-dark)}.date-content[_ngcontent-%COMP%]{padding:0 1rem 1rem 2rem}.date-header[_ngcontent-%COMP%]{display:flex;align-items:center;padding:1rem;background-color:#fff;border-radius:.5rem;margin-bottom:.25rem;cursor:pointer;transition:background-color .2s}.date-header[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}.date-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1rem;margin-right:.5rem}.slot-count[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d;background-color:#f0f0f0;padding:.2rem .5rem;border-radius:1rem}@media (max-width: 768px){.date-header[_ngcontent-%COMP%]{flex-wrap:wrap}.header-actions[_ngcontent-%COMP%]{margin-left:0;margin-top:.5rem;width:100%}}"],data:{animation:[Q("expandCollapse",[G(":enter",[E({height:0,opacity:0}),I("300ms ease-out",E({height:"*",opacity:1}))]),G(":leave",[I("300ms ease-in",E({height:0,opacity:0}))])])]}})};export{gt as AppointmentsComponent};
