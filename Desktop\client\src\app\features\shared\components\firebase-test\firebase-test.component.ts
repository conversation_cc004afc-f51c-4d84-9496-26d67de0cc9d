import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  Firestore,
  collection,
  addDoc,
  getDocs,
  query,
  limit,
  DocumentData,
  doc,
  setDoc,
  getDoc
} from '@angular/fire/firestore';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, authState, onAuthStateChanged } from '@angular/fire/auth';
import { Subscription } from 'rxjs';
import { AuthErrorMapperService } from '../../../../core/services/auth-error-mapper.service';

@Component({
  selector: 'app-firebase-test',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  template: `
    <div class="container mt-5">
      <div class="card mb-4">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h2>Firebase Authentication Test</h2>
          <div>
            <a routerLink="/firebase-validation" class="btn btn-outline-light me-2">Advanced Validation</a>
            <a routerLink="/firebase-availability-debug" class="btn btn-outline-light">Availability Debug</a>
          </div>
        </div>
        <div class="card-body">
          <div *ngIf="authLoading" class="alert alert-info">
            Processing authentication...
          </div>

          <div *ngIf="!authLoading">
            <div *ngIf="currentUser" class="alert alert-success">
              <h4>Currently Logged In</h4>
              <p>Email: {{ currentUser.email }}</p>
              <p>UID: {{ currentUser.uid }}</p>
              <div class="d-flex gap-2 mt-3">
                <button class="btn btn-warning" (click)="logout()">Logout</button>
                <button class="btn btn-info" (click)="createUserDocument()">Create User Document</button>
              </div>
            </div>

            <div *ngIf="!currentUser" class="card">
              <div class="card-body">
                <h4>Authentication Test</h4>
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <input type="email" class="form-control" id="email" [(ngModel)]="testEmail">
                </div>
                <div class="mb-3">
                  <label for="password" class="form-label">Password</label>
                  <input type="password" class="form-control" id="password" [(ngModel)]="testPassword">
                </div>
                <div class="d-flex gap-2">
                  <button class="btn btn-primary" (click)="login()">Test Login</button>
                  <button class="btn btn-secondary" (click)="register()">Test Register</button>
                </div>
              </div>
            </div>

            <div *ngIf="authError" class="alert alert-danger mt-3">
              <h4>Authentication Error</h4>
              <p>{{ authError }}</p>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
          <h2>Firebase Firestore Test</h2>
          <a routerLink="/firebase-debug" class="btn btn-outline-light">Advanced Debug</a>
        </div>
        <div class="card-body">
          <div *ngIf="loading" class="alert alert-info">
            Testing Firebase connection...
          </div>

          <div *ngIf="!loading">
            <div *ngIf="error" class="alert alert-danger">
              <h4>Connection Error</h4>
              <p>{{ error }}</p>
              <p>Check the console for more details.</p>
            </div>

            <div *ngIf="!error" class="alert alert-success">
              <h4>Connection Successful!</h4>
              <p>Firebase is properly configured and connected.</p>
            </div>

            <div *ngIf="testData.length > 0" class="mt-4">
              <h4>Test Collection Data:</h4>
              <ul class="list-group">
                <li *ngFor="let item of testData" class="list-group-item">
                  {{ item.message }} (created: {{ item.timestamp | date:'medium' }})
                </li>
              </ul>
            </div>
          </div>

          <div class="mt-4">
            <button class="btn btn-primary me-2" (click)="runTest()" [disabled]="loading">
              {{ loading ? 'Testing...' : 'Run Test Again' }}
            </button>
            <button class="btn btn-success" (click)="createTestDocument()" [disabled]="loading || error">
              Add Test Document
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: []
})
export class FirebaseTestComponent implements OnInit, OnDestroy {
  loading = true;
  error: string | null = null;
  testData: any[] = [];

  // Auth test properties
  authLoading = false;
  authError: string | null = null;
  currentUser: any = null;
  testEmail = '';
  testPassword = '';

  // Subscriptions
  private authSubscription: Subscription | null = null;

  private authErrorMapper = inject(AuthErrorMapperService);

  constructor(
    private firestore: Firestore,
    private auth: Auth,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.runTest();
    this.checkAuthState();
  }

  ngOnDestroy(): void {
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  checkAuthState(): void {
    this.ngZone.runOutsideAngular(() => {
      this.authSubscription = new Subscription();

      const unsubscribe = onAuthStateChanged(this.auth, (user) => {
        this.ngZone.run(() => {
          this.currentUser = user;
        });
      });

      this.authSubscription.add(() => unsubscribe());
    });
  }

  async runTest(): Promise<void> {
    this.loading = true;
    this.error = null;
    this.testData = [];

    try {
      // Run Firestore operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        const testCollection = collection(this.firestore, 'public_test');
        const testQuery = query(testCollection, limit(10));

        const querySnapshot = await getDocs(testQuery);

        // Process results inside Angular zone
        this.ngZone.run(() => {
          this.testData = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));
          this.loading = false;
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        this.error = `Firebase test failed: ${err.message}`;
        this.loading = false;
      });
    }
  }

  async createTestDocument(): Promise<void> {
    this.loading = true;
    this.error = null;

    try {
      // Run Firestore operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        const testCollection = collection(this.firestore, 'public_test');
        const newDoc = {
          timestamp: new Date(),
          message: 'Test document for Firebase validation',
          createdBy: this.currentUser?.uid || 'anonymous',
          email: this.currentUser?.email || 'anonymous'
        };

        await addDoc(testCollection, newDoc);

        // Refresh data inside Angular zone
        this.ngZone.run(() => {
          this.runTest();
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        this.error = `Failed to create test document: ${err.message}`;
        this.loading = false;
        console.error('Error adding document:', err);

        // Try adding to the user's own document which should be accessible
        if (err.message.includes('Missing or insufficient permissions') && this.currentUser) {
          this.updateUserDocument();
        }
      });
    }
  }

  async createUserDocument(): Promise<void> {
    if (!this.currentUser) {
      this.error = 'You must be logged in to create a user document';
      return;
    }

    this.loading = true;
    this.error = null;

    try {
      // Run Firestore operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        const userDocRef = doc(this.firestore, 'users', this.currentUser.uid);

        // First check if the user document already exists
        const docSnap = await getDoc(userDocRef);

        if (!docSnap.exists()) {
          await setDoc(userDocRef, {
            email: this.currentUser.email,
            displayName: this.currentUser.displayName || this.currentUser.email,
            photoURL: this.currentUser.photoURL || null,
            createdAt: new Date(),
            role: 'user'
          });
        }

        // Refresh data inside Angular zone
        this.ngZone.run(() => {
          this.error = null;
          this.loading = false;
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        this.error = `Failed to create user document: ${err.message}`;
        this.loading = false;
      });
    }
  }

  async login(): Promise<void> {
    if (!this.testEmail || !this.testPassword) {
      this.authError = 'Email and password are required';
      return;
    }

    this.authLoading = true;
    this.authError = null;

    try {
      // Run auth operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        await signInWithEmailAndPassword(this.auth, this.testEmail, this.testPassword);

        // Handle UI updates inside Angular zone
        this.ngZone.run(() => {
          this.authLoading = false;
          this.testEmail = '';
          this.testPassword = '';
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        const userFriendlyMessage = this.authErrorMapper.mapFirebaseError(err);
        this.authError = userFriendlyMessage;
        this.authLoading = false;
      });
    }
  }

  async register(): Promise<void> {
    if (!this.testEmail || !this.testPassword) {
      this.authError = 'Email and password are required';
      return;
    }

    this.authLoading = true;
    this.authError = null;

    try {
      // Run auth operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        await createUserWithEmailAndPassword(this.auth, this.testEmail, this.testPassword);

        // Handle UI updates inside Angular zone
        this.ngZone.run(() => {
          this.authLoading = false;
          this.testEmail = '';
          this.testPassword = '';
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        const userFriendlyMessage = this.authErrorMapper.mapRegistrationError(err);
        this.authError = userFriendlyMessage;
        this.authLoading = false;
      });
    }
  }

  async logout(): Promise<void> {
    this.authLoading = true;
    this.authError = null;

    try {
      // Run auth operations outside Angular zone
      await this.ngZone.runOutsideAngular(async () => {
        await signOut(this.auth);

        // Handle UI updates inside Angular zone
        this.ngZone.run(() => {
          this.authLoading = false;
        });
      });
    } catch (err: any) {
      this.ngZone.run(() => {
        this.authError = `Logout failed: ${err.message}`;
        this.authLoading = false;
      });
    }
  }

  updateUserDocument(): void {
    if (!this.currentUser) {
      this.error = 'You must be logged in to update your user document';
      this.loading = false;
      return;
    }

    this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, `users/${this.currentUser.uid}`);

      const updateData = {
        lastTestTimestamp: new Date().toISOString(),
        testMessage: 'Document updated successfully',
        updatedAt: new Date().toISOString()
      };

      setDoc(userDocRef, updateData, { merge: true })
        .then(() => {
          this.ngZone.run(() => {
            console.log('User document updated successfully');
            this.tryFallbackCollection(); // Check the user document instead
          });
        })
        .catch(error => {
          this.ngZone.run(() => {
            this.error = `Error updating user document: ${error.message}`;
            this.loading = false;
            console.error('Error updating user document:', error);
          });
        });
    });
  }

  tryFallbackCollection(): void {
    this.loading = true;

    this.ngZone.runOutsideAngular(() => {
      // Try with the user's own document which should be accessible
      if (this.currentUser) {
        const userDocRef = doc(this.firestore, `users/${this.currentUser.uid}`);

        getDoc(userDocRef)
          .then(docSnap => {
            this.ngZone.run(() => {
              if (docSnap.exists()) {
                this.testData = [{
                  id: this.currentUser.uid,
                  message: 'Successfully accessed your user document',
                  timestamp: new Date().toISOString(),
                  ...docSnap.data()
                }];
                this.loading = false;
                this.error = null;
              } else {
                throw new Error('User document does not exist - please create it first');
              }
            });
          })
          .catch(error => {
            this.ngZone.run(() => {
              this.error = `Error accessing user document: ${error.message}`;
              this.loading = false;
              console.error('Error accessing user document:', error);
            });
          });
      } else {
        this.ngZone.run(() => {
          this.error = 'You must be logged in to test Firestore access';
          this.loading = false;
        });
      }
    });
  }
}
