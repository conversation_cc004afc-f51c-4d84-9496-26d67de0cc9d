# Firebase Reset Tool - Usage Guide

## Overview

The Firebase Reset Tool is a **development-only** utility that allows you to completely reset your Firebase database by deleting all data from all collections. This is useful for testing, development, and starting fresh with clean data.

## ⚠️ **IMPORTANT WARNING**

**This tool will permanently delete ALL data from your Firebase database. This action cannot be undone!**

- Use only in development/testing environments
- Never use in production
- All user data, appointments, medical records, and other information will be lost
- Make sure you have backups if needed

## Where to Find the Reset Tool

The Firebase Reset Tool has been added to the following dashboards:

### Doctor Dashboard
- Navigate to `/dashboard` (Doctor Dashboard)
- Scroll to the bottom of the page
- Look for the "🚨 DEVELOPMENT TOOL - DANGER ZONE" section

### Patient Dashboard  
- Navigate to `/mobile/patient-dashboard` (Patient Dashboard)
- Scroll to the bottom of the page
- Look for the "🚨 DEVELOPMENT TOOL - DANGER ZONE" section

## How to Use the Reset Tool

### Step 1: Initial View
When you first see the tool, it will show:
- A warning banner explaining this is a development tool
- Information about what data will be deleted
- A red button "I Want to Reset Firebase Database"

### Step 2: Confirmation
After clicking the initial button:
- A final warning dialog appears
- Two checkboxes must be checked:
  - ✅ "I understand this will delete ALL data permanently"
  - ✅ "I confirm this is for development/testing only"
- "DELETE ALL DATA" button becomes enabled only when both boxes are checked

### Step 3: Execution
When you click "DELETE ALL DATA":
- Progress bar shows deletion progress
- Real-time status messages display current operation
- Process typically takes 5-15 seconds depending on data volume

### Step 4: Results
After completion:
- Success/failure message is displayed
- Count of deleted items from each collection
- Option to refresh the application

## What Gets Deleted

The reset tool deletes ALL documents from these Firebase collections:

- **users** - All user accounts and profiles
- **doctors** - All doctor profile information  
- **patients** - All patient profile data
- **appointments** - All appointment records
- **medicalRecords** - All medical record entries
- **doctorAvailability** - All doctor availability slots
- **doctorPatientRelations** - All doctor-patient relationships

## Additional Features

### Clear Current User State
- Clears the current logged-in user session
- Forces user to log in again
- Useful for testing authentication flows

### Refresh Application
- Reloads the entire application
- Ensures all cached data is cleared
- Recommended after successful reset

## Best Practices

### Before Using the Reset Tool

1. **Backup Important Data**: If you have test data you want to keep, export it first
2. **Inform Team Members**: Let other developers know you're resetting the database
3. **Close Other Browser Tabs**: Prevent conflicts with other active sessions
4. **Check Environment**: Ensure you're working with development Firebase project

### After Using the Reset Tool

1. **Refresh Application**: Click the "Refresh Application" button or reload manually
2. **Clear Browser Cache**: Clear localStorage and session storage if needed
3. **Re-authenticate**: Log in again with test accounts
4. **Verify Reset**: Check Firebase Console to confirm data deletion

## Troubleshooting

### Reset Fails
- Check internet connectivity
- Verify Firebase configuration
- Check browser console for errors
- Ensure proper Firebase permissions

### Data Still Appears
- Refresh the application completely
- Clear browser cache and localStorage
- Check if you're looking at cached data
- Verify in Firebase Console

### Permission Errors
- Ensure Firebase security rules allow deletion
- Check if user has proper authentication
- Verify Firebase project configuration

## Development Notes

### Technical Details
- Uses Firebase batch operations for efficient deletion
- Processes all collections simultaneously
- Includes proper error handling and rollback
- Provides detailed logging for debugging

### Security Considerations
- Only available in development builds
- Requires explicit user confirmation
- Logs all operations for audit trail
- Cannot be triggered accidentally

### Performance
- Optimized for large datasets
- Uses Firebase batch operations (max 500 operations per batch)
- Progress tracking for user feedback
- Graceful handling of network issues

## Removing the Reset Tool

For production deployment, you should remove the Firebase Reset Tool:

### Option 1: Environment-Based Hiding
Add environment check in templates:
```html
<div *ngIf="!environment.production">
  <app-firebase-reset></app-firebase-reset>
</div>
```

### Option 2: Complete Removal
Remove the component imports and template references:
1. Remove `FirebaseResetComponent` from component imports
2. Remove `<app-firebase-reset></app-firebase-reset>` from templates
3. Remove the development tools section

## Support

If you encounter issues with the Firebase Reset Tool:

1. Check the browser console for error messages
2. Verify Firebase configuration and permissions
3. Ensure you're using the development environment
4. Contact the development team for assistance

## Related Documentation

- [Firebase Migration Guide](./FIREBASE_MIGRATION_GUIDE.md)
- [Firebase Console](https://console.firebase.google.com/)
- [Angular Firebase Documentation](https://github.com/angular/angularfire)

---

**Remember: This tool is for development only. Always double-check before using it, and never use it in production!**
