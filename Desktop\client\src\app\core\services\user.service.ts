import { Injectable } from '@angular/core';
import { Observable, from, of, throwError } from 'rxjs';
import { ApiService } from './api.service';
import { User } from './auth.service';
import {
  Firestore,
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  collectionData,
  orderBy
} from '@angular/fire/firestore';
import { Auth, updatePassword, EmailAuthProvider, reauthenticateWithCredential } from '@angular/fire/auth';
import { catchError, map } from 'rxjs/operators';

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  profilePicture?: string;
  specialization?: string;
  phoneNumber?: string;
  address?: string;
  affiliatedInstitution?: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {
  constructor(
    private apiService: ApiService,
    private firestore: Firestore,
    private auth: Auth
  ) {}

  getUserProfile(userId: string): Observable<User> {
    const userDocRef = doc(this.firestore, `users/${userId}`);
    return from(getDoc(userDocRef)).pipe(
      map(docSnap => {
        if (docSnap.exists()) {
          return { uid: userId, ...docSnap.data() } as User;
        } else {
          throw new Error('User not found');
        }
      }),
      catchError(error => {
        console.error('Error fetching user profile:', error);
        return throwError(() => error);
      })
    );
  }

  updateUserProfile(userId: string, userData: UpdateUserRequest): Observable<User> {
    const userDocRef = doc(this.firestore, `users/${userId}`);
    return from(updateDoc(userDocRef, { ...userData })).pipe(
      map(() => ({ uid: userId, ...userData } as User)),
      catchError(error => {
        console.error('Error updating user profile:', error);
        return throwError(() => error);
      })
    );
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      const user = this.auth.currentUser;
      if (!user || !user.email) {
        throw new Error('No authenticated user found');
      }

      // Re-authenticate the user
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Update the password
      await updatePassword(user, newPassword);
    } catch (error) {
      console.error('Error changing password:', error);
      throw error;
    }
  }

  getDoctors(): Observable<User[]> {
    const usersRef = collection(this.firestore, 'users');
    const doctorsQuery = query(
      usersRef,
      where('role', '==', 'doctor'),
      orderBy('lastName')
    );

    return collectionData(doctorsQuery).pipe(
      map(data => data as User[]),
      catchError(error => {
        console.error('Error fetching doctors:', error);
        return throwError(() => error);
      })
    );
  }

  getPatients(): Observable<User[]> {
    const usersRef = collection(this.firestore, 'users');
    const patientsQuery = query(
      usersRef,
      where('role', '==', 'patient'),
      orderBy('lastName')
    );

    return collectionData(patientsQuery).pipe(
      map(data => data as User[]),
      catchError(error => {
        console.error('Error fetching patients:', error);
        return throwError(() => error);
      })
    );
  }

  getReceptionists(): Observable<User[]> {
    const usersRef = collection(this.firestore, 'users');
    const receptionistsQuery = query(
      usersRef,
      where('role', '==', 'receptionist'),
      orderBy('lastName')
    );

    return collectionData(receptionistsQuery).pipe(
      map(data => data as User[]),
      catchError(error => {
        console.error('Error fetching receptionists:', error);
        return throwError(() => error);
      })
    );
  }
}
