import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'app-payment-info',
  templateUrl: './payment-info.component.html',
  styleUrls: ['./payment-info.component.css'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class PaymentInfoComponent implements OnInit {
  paymentForm: FormGroup;
  subscriptionType: string = 'trial';
  subscriptionPrice: number = 0;
  userCount: number = 5;
  expiryDateError: string = '';

  // Payment processing states
  isProcessing: boolean = false;
  paymentStep: 'idle' | 'processing' | 'verifying' | 'success' = 'idle';
  processingMessage: string = '';

  // Processing messages
  private processingMessages = [
    'Connecting to payment gateway...',
    'Processing your payment...',
    'Securing transaction...'
  ];

  private verifyingMessages = [
    'Verifying card details...',
    'Confirming payment...',
    'Finalizing transaction...'
  ];

  private successMessages = [
    'Payment successful!',
    'Transaction complete!',
    'Payment confirmed!'
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router
  ) {
    this.paymentForm = this.fb.group({
      cardNumber: ['', [
        Validators.required,
        Validators.pattern(/^[\d\s]{19}$/)
      ]],
      expiryDate: ['', [
        Validators.required,
        Validators.pattern(/^(0[1-9]|1[0-2])\/([0-9]{2})$/),
        this.expiryDateValidator
      ]],
      cvc: ['', [
        Validators.required,
        Validators.pattern(/^[0-9]{3}$/)
      ]]
    });
  }

  ngOnInit(): void {
    const practiceData = localStorage.getItem('practiceData');
    if (!practiceData) {
      this.router.navigate(['/payment/practice-info']);
      return;
    }

    this.subscriptionType = localStorage.getItem('subscriptionType') || 'trial';
    if (this.subscriptionType === 'premium') {
      this.subscriptionPrice = Number(localStorage.getItem('subscriptionPrice')) || 0;
      this.userCount = Number(localStorage.getItem('userCount')) || 5;
    }
  }

  // Custom validator for expiry date to ensure it's not expired
  expiryDateValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;
    if (!value) {
      return null;
    }

    // Check if the format is valid
    if (!/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(value)) {
      return { invalidFormat: true };
    }

    const [month, yearStr] = value.split('/');
    const year = 2000 + parseInt(yearStr, 10); // Convert YY to YYYY

    // Create date objects for validation
    const expiryDate = new Date(year, parseInt(month, 10) - 1, 1); // First day of the month
    const today = new Date();
    const lastDayOfMonth = new Date(year, parseInt(month, 10), 0).getDate();

    // Set expiry to last day of the month at 23:59:59
    expiryDate.setDate(lastDayOfMonth);
    expiryDate.setHours(23, 59, 59, 999);

    // Check if the card is expired
    if (expiryDate < today) {
      return { expired: true };
    }

    return null;
  }

  formatCardNumber(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, '');
    if (value.length > 16) value = value.substr(0, 16);


    const parts = value.match(/.{1,4}/g) || [];
    input.value = parts.join(' ');

    // Update form control
    this.paymentForm.get('cardNumber')?.setValue(input.value, { emitEvent: false });
  }

  formatExpiryDate(event: Event): void {
    const input = event.target as HTMLInputElement;
    let value = input.value.replace(/\D/g, '');

    if (value.length >= 2) {
      value = value.substr(0, 2) + '/' + value.substr(2, 2);
    }

    input.value = value;
    this.paymentForm.get('expiryDate')?.setValue(input.value, { emitEvent: false });

    // Check for validation errors
    const expiryControl = this.paymentForm.get('expiryDate');
    if (expiryControl?.errors?.['expired']) {
      this.expiryDateError = 'Card has expired. Please use a valid expiry date.';
    } else if (expiryControl?.errors?.['invalidFormat']) {
      this.expiryDateError = 'Invalid format. Use MM/YY format.';
    } else {
      this.expiryDateError = '';
    }
  }

  // Get a random message from an array
  private getRandomMessage(messages: string[]): string {
    const index = Math.floor(Math.random() * messages.length);
    return messages[index];
  }

  // Get a random time between min and max seconds
  private getRandomTime(min: number, max: number): number {
    return Math.floor(Math.random() * (max * 10 - min * 10 + 1) + min * 10) * 100;
  }

  // Update processing message with animation
  private updateMessage(message: string): void {
    // Fade out current message
    this.processingMessage = '';

    // Short delay before showing new message
    setTimeout(() => {
      this.processingMessage = message;
    }, 150);
  }

  // Simulate payment processing with multiple steps and dynamic messages
  private simulatePaymentProcess(): void {
    this.isProcessing = true;
    this.paymentStep = 'processing';

    // Initial processing message
    this.updateMessage(this.getRandomMessage(this.processingMessages));

    // First stage - processing (2-3 seconds with message updates)
    const initialProcessingTime = this.getRandomTime(1.0, 1.5);
    const secondaryProcessingTime = this.getRandomTime(1.0, 1.5);

    setTimeout(() => {
      // Update message during processing
      this.updateMessage(this.getRandomMessage(this.processingMessages));

      setTimeout(() => {
        // Second stage - verification (1.5-2.5 seconds)
        this.paymentStep = 'verifying';
        this.updateMessage(this.getRandomMessage(this.verifyingMessages));

        const initialVerifyingTime = this.getRandomTime(0.8, 1.2);
        const secondaryVerifyingTime = this.getRandomTime(0.7, 1.3);

        setTimeout(() => {
          // Update verifying message
          this.updateMessage(this.getRandomMessage(this.verifyingMessages));

          setTimeout(() => {
            // Final stage - success (2-3 seconds)
            this.paymentStep = 'success';
            this.updateMessage(this.getRandomMessage(this.successMessages));

            const successTime = this.getRandomTime(2.0, 3.0);

            setTimeout(() => {
              // Save data and redirect
              this.savePaymentData();
              this.router.navigate(['/dashboard']);
            }, successTime);
          }, secondaryVerifyingTime);
        }, initialVerifyingTime);
      }, secondaryProcessingTime);
    }, initialProcessingTime);
  }

  // Save payment data to localStorage
  private savePaymentData(): void {
    const practiceData = JSON.parse(localStorage.getItem('practiceData') || '{}');

    // Combine with payment data and subscription details
    const paymentData = {
      ...practiceData,
      payment: this.paymentForm.value,
      subscription: {
        type: this.subscriptionType,
        price: this.subscriptionPrice,
        userCount: this.userCount
      }
    };

    console.log('Payment processed successfully:', paymentData);

    // Store subscription data
    localStorage.setItem('subscriptionData', JSON.stringify({
      type: this.subscriptionType,
      userCount: this.userCount
    }));

    // Store user email for future reference
    const userEmail = localStorage.getItem('userEmail');
    if (userEmail) {
      localStorage.setItem('userEmail', userEmail);
    }
  }

  onSubmit(): void {
    if (this.paymentForm.valid && !this.isProcessing) {
      // Start the payment simulation process
      this.simulatePaymentProcess();
    } else if (!this.isProcessing) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.paymentForm.controls).forEach(key => {
        this.paymentForm.get(key)?.markAsTouched();
      });

      // Check for expiry date errors specifically
      const expiryControl = this.paymentForm.get('expiryDate');
      if (expiryControl?.errors?.['expired']) {
        this.expiryDateError = 'Card has expired. Please use a valid expiry date.';
      }
    }
  }
}
