import { Injectable, inject, NgZone } from '@angular/core';
import { Observable, from } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import {
  Storage,
  ref,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  UploadTask,
  UploadTaskSnapshot
} from '@angular/fire/storage';
import { Auth } from '@angular/fire/auth';

export interface UploadProgress {
  progress: number;
  bytesTransferred: number;
  totalBytes: number;
  state: 'running' | 'paused' | 'success' | 'canceled' | 'error';
}

export interface UploadResult {
  downloadURL: string;
  fullPath: string;
  name: string;
}

/**
 * Firebase Storage Service for file uploads
 * Handles profile pictures, medical records, and other file uploads
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseStorageService {
  private storage = inject(Storage);
  private auth = inject(Auth);
  private ngZone = inject(NgZone);

  constructor() { }

  /**
   * Upload profile picture for a user
   */
  uploadProfilePicture(file: File, userId: string): Observable<UploadResult> {
    return this.uploadFile(file, `profilePictures/${userId}`, {
      userId: userId,
      type: 'profile'
    });
  }

  /**
   * Upload medical record attachment
   */
  uploadMedicalRecordFile(file: File, recordId: string, patientId: string, doctorId: string): Observable<UploadResult> {
    return this.uploadFile(file, `medicalRecords/${recordId}`, {
      recordId: recordId,
      patientId: patientId,
      doctorId: doctorId,
      type: 'medical-record'
    });
  }

  /**
   * Upload general document
   */
  uploadDocument(file: File, category: string, userId: string): Observable<UploadResult> {
    return this.uploadFile(file, `documents/${category}/${userId}`, {
      userId: userId,
      category: category,
      type: 'document'
    });
  }

  /**
   * Generic file upload method
   */
  private uploadFile(file: File, basePath: string, metadata: any): Observable<UploadResult> {
    return this.ngZone.runOutsideAngular(() => {
      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}_${file.name}`;
      const filePath = `${basePath}/${fileName}`;
      
      // Create storage reference
      const storageRef = ref(this.storage, filePath);

      // Add custom metadata
      const uploadMetadata = {
        customMetadata: {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          uploadedBy: this.auth.currentUser?.uid || 'anonymous'
        }
      };

      // Start upload
      const uploadTask = uploadBytesResumable(storageRef, file, uploadMetadata);

      return new Observable<UploadResult>(observer => {
        uploadTask.on('state_changed',
          (snapshot: UploadTaskSnapshot) => {
            // Optional: Emit progress updates
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            console.log(`Upload is ${progress}% done`);
          },
          (error) => {
            // Handle unsuccessful uploads
            this.ngZone.run(() => {
              console.error('Upload failed:', error);
              observer.error(error);
            });
          },
          () => {
            // Handle successful uploads
            getDownloadURL(uploadTask.snapshot.ref).then(downloadURL => {
              this.ngZone.run(() => {
                const result: UploadResult = {
                  downloadURL: downloadURL,
                  fullPath: filePath,
                  name: fileName
                };
                observer.next(result);
                observer.complete();
              });
            }).catch(error => {
              this.ngZone.run(() => {
                observer.error(error);
              });
            });
          }
        );
      });
    });
  }

  /**
   * Upload file with progress tracking
   */
  uploadFileWithProgress(file: File, basePath: string, metadata: any): Observable<UploadProgress | UploadResult> {
    return this.ngZone.runOutsideAngular(() => {
      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${timestamp}_${file.name}`;
      const filePath = `${basePath}/${fileName}`;
      
      // Create storage reference
      const storageRef = ref(this.storage, filePath);

      // Add custom metadata
      const uploadMetadata = {
        customMetadata: {
          ...metadata,
          uploadedAt: new Date().toISOString(),
          uploadedBy: this.auth.currentUser?.uid || 'anonymous'
        }
      };

      // Start upload
      const uploadTask = uploadBytesResumable(storageRef, file, uploadMetadata);

      return new Observable<UploadProgress | UploadResult>(observer => {
        uploadTask.on('state_changed',
          (snapshot: UploadTaskSnapshot) => {
            // Emit progress updates
            this.ngZone.run(() => {
              const progress: UploadProgress = {
                progress: (snapshot.bytesTransferred / snapshot.totalBytes) * 100,
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
                state: snapshot.state as any
              };
              observer.next(progress);
            });
          },
          (error) => {
            // Handle unsuccessful uploads
            this.ngZone.run(() => {
              console.error('Upload failed:', error);
              observer.error(error);
            });
          },
          () => {
            // Handle successful uploads
            getDownloadURL(uploadTask.snapshot.ref).then(downloadURL => {
              this.ngZone.run(() => {
                const result: UploadResult = {
                  downloadURL: downloadURL,
                  fullPath: filePath,
                  name: fileName
                };
                observer.next(result);
                observer.complete();
              });
            }).catch(error => {
              this.ngZone.run(() => {
                observer.error(error);
              });
            });
          }
        );
      });
    });
  }

  /**
   * Delete file from Firebase Storage
   */
  deleteFile(filePath: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const storageRef = ref(this.storage, filePath);
      return from(deleteObject(storageRef)).pipe(
        catchError(error => {
          console.error('Error deleting file:', error);
          throw error;
        })
      );
    });
  }

  /**
   * Delete profile picture
   */
  deleteProfilePicture(userId: string, fileName: string): Observable<void> {
    const filePath = `profilePictures/${userId}/${fileName}`;
    return this.deleteFile(filePath);
  }

  /**
   * Delete medical record file
   */
  deleteMedicalRecordFile(recordId: string, fileName: string): Observable<void> {
    const filePath = `medicalRecords/${recordId}/${fileName}`;
    return this.deleteFile(filePath);
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File, maxSizeMB: number = 10, allowedTypes: string[] = []): { valid: boolean; error?: string } {
    // Check file size
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: `File size exceeds ${maxSizeMB}MB limit`
      };
    }

    // Check file type if specified
    if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Get common image file types
   */
  getImageFileTypes(): string[] {
    return ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  }

  /**
   * Get common document file types
   */
  getDocumentFileTypes(): string[] {
    return [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
  }

  /**
   * Get all allowed file types for medical records
   */
  getMedicalRecordFileTypes(): string[] {
    return [
      ...this.getImageFileTypes(),
      ...this.getDocumentFileTypes()
    ];
  }
}
