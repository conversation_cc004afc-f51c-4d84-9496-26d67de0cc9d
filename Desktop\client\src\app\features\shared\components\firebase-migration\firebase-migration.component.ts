import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FirebaseMigrationService, MigrationResult } from '../../../../core/services/firebase-migration.service';

@Component({
  selector: 'app-firebase-migration',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="migration-container p-4 max-w-2xl mx-auto">
      <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">
          <i class="bi bi-cloud-upload text-blue-600 mr-2"></i>
          Firebase Migration Tool
        </h2>
        
        <div class="mb-6">
          <p class="text-gray-600 mb-4">
            This tool helps migrate your data from localStorage to Firebase. 
            This is a one-time process that will move all your existing data to the cloud.
          </p>
          
          <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="bi bi-info-circle text-blue-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-blue-700">
                  <strong>Note:</strong> After successful migration, your localStorage data will be cleared 
                  and all future data will be stored in Firebase.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Migration Summary -->
        <div class="mb-6" *ngIf="migrationSummary">
          <h3 class="text-lg font-semibold text-gray-800 mb-3">Data to Migrate:</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Users</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.users }}</div>
            </div>
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Doctors</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.doctors }}</div>
            </div>
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Patients</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.patients }}</div>
            </div>
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Appointments</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.appointments }}</div>
            </div>
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Medical Records</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.medicalRecords }}</div>
            </div>
            <div class="bg-gray-50 p-3 rounded">
              <div class="text-sm text-gray-600">Availability</div>
              <div class="text-xl font-bold text-gray-800">{{ migrationSummary.availability }}</div>
            </div>
          </div>
        </div>

        <!-- No Data Message -->
        <div class="mb-6" *ngIf="!hasDataToMigrate && !loading">
          <div class="bg-green-50 border-l-4 border-green-400 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="bi bi-check-circle text-green-400"></i>
              </div>
              <div class="ml-3">
                <p class="text-sm text-green-700">
                  No localStorage data found to migrate. Your application is already using Firebase!
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Migration Button -->
        <div class="mb-6" *ngIf="hasDataToMigrate">
          <button 
            (click)="startMigration()"
            [disabled]="loading"
            class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
            <span *ngIf="!loading">
              <i class="bi bi-cloud-upload mr-2"></i>
              Start Migration
            </span>
            <span *ngIf="loading">
              <i class="bi bi-arrow-repeat animate-spin mr-2"></i>
              Migrating...
            </span>
          </button>
        </div>

        <!-- Loading Progress -->
        <div class="mb-6" *ngIf="loading">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                 [style.width.%]="progress"></div>
          </div>
          <p class="text-sm text-gray-600 mt-2 text-center">{{ progressMessage }}</p>
        </div>

        <!-- Migration Result -->
        <div class="mb-6" *ngIf="migrationResult">
          <div class="p-4 rounded-lg" 
               [ngClass]="migrationResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <i class="text-lg" 
                   [ngClass]="migrationResult.success ? 'bi bi-check-circle text-green-500' : 'bi bi-x-circle text-red-500'"></i>
              </div>
              <div class="ml-3 flex-1">
                <h4 class="font-semibold" 
                    [ngClass]="migrationResult.success ? 'text-green-800' : 'text-red-800'">
                  {{ migrationResult.success ? 'Migration Successful!' : 'Migration Failed' }}
                </h4>
                <p class="text-sm mt-1" 
                   [ngClass]="migrationResult.success ? 'text-green-700' : 'text-red-700'">
                  {{ migrationResult.message }}
                </p>
                
                <!-- Success Details -->
                <div *ngIf="migrationResult.success" class="mt-3">
                  <h5 class="font-medium text-green-800 mb-2">Migrated Data:</h5>
                  <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
                    <div>Users: {{ migrationResult.migratedCounts.users }}</div>
                    <div>Doctors: {{ migrationResult.migratedCounts.doctors }}</div>
                    <div>Patients: {{ migrationResult.migratedCounts.patients }}</div>
                    <div>Appointments: {{ migrationResult.migratedCounts.appointments }}</div>
                    <div>Medical Records: {{ migrationResult.migratedCounts.medicalRecords }}</div>
                    <div>Availability: {{ migrationResult.migratedCounts.availability }}</div>
                  </div>
                </div>

                <!-- Error Details -->
                <div *ngIf="!migrationResult.success && migrationResult.errors.length > 0" class="mt-3">
                  <h5 class="font-medium text-red-800 mb-2">Errors:</h5>
                  <ul class="text-sm text-red-700 list-disc list-inside">
                    <li *ngFor="let error of migrationResult.errors">{{ error }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Refresh Button -->
        <div class="text-center" *ngIf="migrationResult?.success">
          <button 
            (click)="refreshPage()"
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200">
            <i class="bi bi-arrow-clockwise mr-2"></i>
            Refresh Application
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-spin {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `]
})
export class FirebaseMigrationComponent implements OnInit {
  private migrationService = inject(FirebaseMigrationService);

  hasDataToMigrate = false;
  migrationSummary: { [key: string]: number } | null = null;
  loading = false;
  progress = 0;
  progressMessage = '';
  migrationResult: MigrationResult | null = null;

  ngOnInit(): void {
    this.checkForData();
  }

  checkForData(): void {
    this.hasDataToMigrate = this.migrationService.hasDataToMigrate();
    if (this.hasDataToMigrate) {
      this.migrationSummary = this.migrationService.getMigrationSummary();
    }
  }

  startMigration(): void {
    this.loading = true;
    this.progress = 0;
    this.progressMessage = 'Starting migration...';
    this.migrationResult = null;

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      if (this.progress < 90) {
        this.progress += 10;
        this.progressMessage = `Migrating data... ${this.progress}%`;
      }
    }, 500);

    this.migrationService.migrateAllData().subscribe({
      next: (result) => {
        clearInterval(progressInterval);
        this.progress = 100;
        this.progressMessage = 'Migration complete!';
        this.migrationResult = result;
        this.loading = false;
        
        if (result.success) {
          this.hasDataToMigrate = false;
          this.migrationSummary = null;
        }
      },
      error: (error) => {
        clearInterval(progressInterval);
        this.loading = false;
        this.migrationResult = {
          success: false,
          message: `Migration failed: ${error.message}`,
          migratedCounts: {
            users: 0,
            doctors: 0,
            patients: 0,
            appointments: 0,
            medicalRecords: 0,
            availability: 0,
            doctorPatientRelations: 0
          },
          errors: [error.message]
        };
      }
    });
  }

  refreshPage(): void {
    window.location.reload();
  }
}
