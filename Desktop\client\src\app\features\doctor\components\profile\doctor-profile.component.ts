import { Component, OnInit, Ng<PERSON>one } from "@angular/core";
import { CommonModule } from "@angular/common";
import { FormsModule } from "@angular/forms";
import { Router } from "@angular/router";
import { AuthService, ProfileUpdateResponse } from "../../../../features/auth/services/auth.service";
import { ProfileUpdateService } from "../../../../features/shared/services/profile-update.service";
import { HttpClient } from "@angular/common/http";
import { environment } from "../../../../../environments/environment";
import { Db } from "../../../../db";
import { DoctorService } from "../../../doctor/services/doctor.service";

@Component({
  selector: "app-doctor-profile",
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: "./doctor-profile.component.html",
  styleUrls: ["./doctor-profile.component.css"],
})
export class DoctorProfileComponent implements OnInit {
  fullName = "";
  email = "";
  phoneNumber = "";
  specialization = "";
  hospitalAffiliations: string[] = [];
  bio = "";
  selectedImage: File | null = null;
  imagePreview: string | null = null;
  qualifications: string[] = [];
  newQualification: string = '';
  newHospitalAffiliation: string = '';
  services: string[] = [];
  newService: string = '';
  showSuccessModal = false;
  isSubmitting = false; // Track form submission attempt
  private baseUrl = environment.apiUrl + '/api';

  constructor(
    private authService: AuthService,
    private router: Router,
    private profileUpdateService: ProfileUpdateService,
    private http: HttpClient,
    private db: Db,
    private doctorService: DoctorService,
    private ngZone: NgZone
  ) {}

  ngOnInit() {
    // Get user info from auth service
    let userInfo = this.authService.getUserInfo();
    console.log('User info retrieved in doctor profile:', userInfo);

    // Check if we have user info
    if (userInfo) {
      this.initializeFromUserInfo(userInfo);
    } else {
      console.warn('No user info available for doctor profile, will try to get it from localStorage');

      // Try to get it from localStorage directly as a backup
      const userInfoStr = localStorage.getItem('med_secure_user_info');
      if (userInfoStr) {
        try {
          userInfo = JSON.parse(userInfoStr);
          console.log('User info retrieved from localStorage directly:', userInfo);
          this.initializeFromUserInfo(userInfo);
        } catch (error) {
          console.error('Error parsing user info from localStorage:', error);
        }
      }

      // If still no user info, set up a timer to check again in a moment
      // This handles the case where the component loads before auth service has finished
      if (!userInfo) {
        console.log('Will retry getting user info in 500ms');
        setTimeout(() => {
          userInfo = this.authService.getUserInfo();
          console.log('User info after retry:', userInfo);
          if (userInfo) {
            this.initializeFromUserInfo(userInfo);
          }
        }, 500);
      }
    }

    // Get the doctor ID from local storage or user info
    const doctorId = this.authService.getDoctorId() || (userInfo?.doctorId || userInfo?.id);
    console.log('Doctor ID from local storage:', doctorId);

    // We'll try to fetch details in the background, but already have the UI initialized
    if (doctorId) {
      this.ngZone.runOutsideAngular(() => {
        this.fetchDoctorDetailsInBackground(doctorId);
      });
    }
  }

  private fetchDoctorDetailsInBackground(doctorId: string) {
    this.ngZone.run(() => {
      console.log('Attempting to fetch doctor details in background for ID:', doctorId);
    });

    this.doctorService.getDoctorById(doctorId).subscribe({
      next: (doctor) => {
        this.ngZone.run(() => {
          console.log('Doctor details fetched successfully from Firestore:', doctor);
          // Update the profile with the fetched doctor details
          // No need to reinitialize the UI as we've already done that from localStorage
        });
      },
      error: (error) => {
        this.ngZone.run(() => {
          console.error('Error fetching doctor details from Firestore - this is expected until security rules are fixed:', error);
          // Already initialized from localStorage, so no fallback needed
        });
      }
    });
  }

  private initializeFromUserInfo(userInfo: any) {
    // Handle full name - ensure it's properly formatted
    this.fullName = userInfo.name || '';

    // If name is empty but we have firstName and lastName in the response, use those
    if (!this.fullName && userInfo.firstName && userInfo.lastName) {
      this.fullName = `${userInfo.firstName} ${userInfo.lastName}`.trim();
    }

    this.email = userInfo.email || '';
    this.phoneNumber = userInfo.phoneNumber || '';
    this.specialization = userInfo.specialization || '';

    // Ensure arrays are properly initialized
    if (Array.isArray(userInfo.hospitalAffiliations)) {
      this.hospitalAffiliations = userInfo.hospitalAffiliations;
    } else if (typeof userInfo.hospitalAffiliations === 'string') {
      this.hospitalAffiliations = userInfo.hospitalAffiliations.split(',').map((s: string) => s.trim());
    } else {
      this.hospitalAffiliations = [];
    }

    if (Array.isArray(userInfo.qualifications)) {
      this.qualifications = userInfo.qualifications;
    } else if (typeof userInfo.qualifications === 'string') {
      this.qualifications = userInfo.qualifications.split(',').map((s: string) => s.trim());
    } else {
      this.qualifications = [];
    }

    if (Array.isArray(userInfo.services)) {
      this.services = userInfo.services;
    } else if (typeof userInfo.services === 'string') {
      this.services = userInfo.services.split(',').map((s: string) => s.trim());
    } else {
      this.services = [];
    }

    this.bio = userInfo.bio || '';

    // Set profile picture if available
    if (userInfo.profilePicture) {
      this.imagePreview = userInfo.profilePicture;
    }

    console.log('Doctor profile initialized with user info data:', {
      fullName: this.fullName,
      email: this.email,
      phoneNumber: this.phoneNumber,
      specialization: this.specialization,
      hospitalAffiliations: this.hospitalAffiliations,
      qualifications: this.qualifications,
      services: this.services,
      bio: this.bio,
      hasProfilePicture: !!this.imagePreview
    });
  }

  onImageSelected(event: any) {
    const file = event.target.files[0];
    if (file) {
      // Check file size (limit to 2MB)
      if (file.size > 2 * 1024 * 1024) {
        alert('Image is too large. Maximum size is 2MB.');
        return;
      }

      // Check file type
      if (!file.type.match(/image\/(jpeg|jpg|png|gif)/)) {
        alert('Only image files (JPEG, PNG, GIF) are allowed.');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.imagePreview = e.target.result;
        console.log('Image loaded as base64');

        // Automatically update profile picture when selected
        this.updateProfilePicture();
      };
      reader.readAsDataURL(file);
    }
  }

  private convertToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const base64String = reader.result as string;
        resolve(base64String);
      };
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      reader.readAsDataURL(file);
    });
  }

  addQualification() {
    if (this.newQualification.trim()) {
      this.qualifications.push(this.newQualification.trim());
      this.newQualification = '';
      this.updateUserInfo();
    }
  }

  removeQualification(index: number) {
    this.qualifications.splice(index, 1);
    this.updateUserInfo();
  }

  addHospitalAffiliation() {
    if (this.newHospitalAffiliation.trim()) {
      this.hospitalAffiliations.push(this.newHospitalAffiliation.trim());
      this.newHospitalAffiliation = '';
      this.updateUserInfo();
    }
  }

  removeHospitalAffiliation(index: number) {
    this.hospitalAffiliations.splice(index, 1);
    this.updateUserInfo();
  }

  addService() {
    if (this.newService.trim()) {
      this.services.push(this.newService.trim());
      this.newService = '';
      this.updateUserInfo();
    }
  }

  removeService(index: number) {
    this.services.splice(index, 1);
    this.updateUserInfo();
  }

  private updateUserInfo() {
    const currentUserInfo = this.authService.getUserInfo();
    if (!currentUserInfo) {
      console.error('No existing user info found');
      return;
    }

    const userInfo = {
      ...currentUserInfo, // Preserve all existing fields
      name: this.fullName,
      email: this.email,
      phoneNumber: this.phoneNumber,
      specialization: this.specialization,
      profilePicture: this.imagePreview,
      hospitalAffiliations: this.hospitalAffiliations,
      qualifications: this.qualifications,
      bio: this.bio,
      services: this.services
    };

    console.log('Updating local user info:', userInfo);
    this.authService.saveUserInfo(userInfo);
  }

  updateProfilePicture() {
    if (!this.imagePreview) {
      console.error('No image selected');
      return;
    }

    console.log('Updating profile picture with base64 data');

    // First, save to the doctor table in the database
    this.authService.updateProfilePicture(this.imagePreview).subscribe({
      next: (response) => {
        console.log('Profile picture updated successfully:', response);

        // Update local user info with the new profile picture
        const currentUserInfo = this.authService.getUserInfo();
        if (currentUserInfo) {
          const updatedUserInfo = {
            ...currentUserInfo,
            profilePicture: this.imagePreview
          };
          this.authService.saveUserInfo(updatedUserInfo);
        }

        // Notify other components about the profile picture update
        this.profileUpdateService.notifyProfileUpdate({
          profilePicture: this.imagePreview,
          name: this.fullName,
          specialization: this.specialization,
          bio: this.bio
        });
      },
      error: (error) => {
        console.error('Error updating profile picture:', error);
        alert(`Error updating profile picture: ${error.message}`);
      }
    });
  }

  proceedToSubscription() {
    this.showSuccessModal = true;
  }

  navigateToAvailability() {
    this.router.navigate(['/doctor-availability']);
  }

  closeModalAndProceed() {
    this.showSuccessModal = false;
    this.router.navigate(['/subscription']);
  }

  // Reset validation state when input changes
  onInputChange() {
    if (this.isSubmitting) {
      this.isSubmitting = false;
    }
  }
}
