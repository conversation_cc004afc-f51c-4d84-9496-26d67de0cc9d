import { Injectable, inject, NgZone } from '@angular/core';
import { Observable, from, BehaviorSubject, throwError, of } from 'rxjs';
import { map, catchError, tap, switchMap, take } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  addDoc,
  updateDoc,
  deleteDoc,
  setDoc,
  Timestamp,
  collectionData,
  writeBatch,
  onSnapshot,
  QuerySnapshot,
  DocumentData
} from '@angular/fire/firestore';
import { Auth } from '@angular/fire/auth';
import {
  UserType,
  DoctorType,
  PatientType,
  AppointmentType,
  MedicalRecordType,
  AvailabilityType,
  DoctorPatient,
  AppointmentStatusType
} from '../../type';

/**
 * Centralized Firebase Data Service
 * Replaces localStorage with Firebase Firestore operations
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseDataService {
  private firestore = inject(Firestore);
  private auth = inject(Auth);
  private ngZone = inject(NgZone);

  // Collection names
  private readonly COLLECTIONS = {
    USERS: 'users',
    DOCTORS: 'doctors',
    PATIENTS: 'patients',
    APPOINTMENTS: 'appointments',
    MEDICAL_RECORDS: 'medicalRecords',
    AVAILABILITY: 'doctorAvailability',
    DOCTOR_PATIENT: 'doctorPatientRelations'
  };

  // BehaviorSubjects for real-time data
  private usersSubject = new BehaviorSubject<UserType[]>([]);
  private doctorsSubject = new BehaviorSubject<DoctorType[]>([]);
  private patientsSubject = new BehaviorSubject<PatientType[]>([]);
  private appointmentsSubject = new BehaviorSubject<AppointmentType[]>([]);
  private medicalRecordsSubject = new BehaviorSubject<MedicalRecordType[]>([]);
  private availabilitySubject = new BehaviorSubject<AvailabilityType[]>([]);
  private doctorPatientSubject = new BehaviorSubject<DoctorPatient[]>([]);

  // Public observables
  public users$ = this.usersSubject.asObservable();
  public doctors$ = this.doctorsSubject.asObservable();
  public patients$ = this.patientsSubject.asObservable();
  public appointments$ = this.appointmentsSubject.asObservable();
  public medicalRecords$ = this.medicalRecordsSubject.asObservable();
  public availability$ = this.availabilitySubject.asObservable();
  public doctorPatient$ = this.doctorPatientSubject.asObservable();

  // Current user state
  private currentDoctorSubject = new BehaviorSubject<UserType | null>(null);
  private currentPatientSubject = new BehaviorSubject<UserType | null>(null);
  public currentDoctor$ = this.currentDoctorSubject.asObservable();
  public currentPatient$ = this.currentPatientSubject.asObservable();

  constructor() {
    this.initializeRealtimeListeners();
  }

  // ==================== INITIALIZATION ====================

  /**
   * Initialize real-time listeners for all collections
   */
  private initializeRealtimeListeners(): void {
    // Only initialize if user is authenticated
    this.auth.onAuthStateChanged(user => {
      if (user) {
        this.setupCollectionListeners();
      } else {
        this.clearAllData();
      }
    });
  }

  /**
   * Setup real-time listeners for all collections
   */
  private setupCollectionListeners(): void {
    this.ngZone.runOutsideAngular(() => {
      // Users collection listener
      const usersRef = collection(this.firestore, this.COLLECTIONS.USERS);
      onSnapshot(usersRef, (snapshot) => {
        this.ngZone.run(() => {
          const users = this.processSnapshot<UserType>(snapshot);
          this.usersSubject.next(users);
        });
      });

      // Doctors collection listener
      const doctorsRef = collection(this.firestore, this.COLLECTIONS.DOCTORS);
      onSnapshot(doctorsRef, (snapshot) => {
        this.ngZone.run(() => {
          const doctors = this.processSnapshot<DoctorType>(snapshot);
          this.doctorsSubject.next(doctors);
        });
      });

      // Patients collection listener
      const patientsRef = collection(this.firestore, this.COLLECTIONS.PATIENTS);
      onSnapshot(patientsRef, (snapshot) => {
        this.ngZone.run(() => {
          const patients = this.processSnapshot<PatientType>(snapshot);
          this.patientsSubject.next(patients);
        });
      });

      // Appointments collection listener
      const appointmentsRef = collection(this.firestore, this.COLLECTIONS.APPOINTMENTS);
      onSnapshot(appointmentsRef, (snapshot) => {
        this.ngZone.run(() => {
          const appointments = this.processSnapshot<AppointmentType>(snapshot);
          this.appointmentsSubject.next(appointments);
        });
      });

      // Medical records collection listener
      const medicalRecordsRef = collection(this.firestore, this.COLLECTIONS.MEDICAL_RECORDS);
      onSnapshot(medicalRecordsRef, (snapshot) => {
        this.ngZone.run(() => {
          const records = this.processSnapshot<MedicalRecordType>(snapshot);
          this.medicalRecordsSubject.next(records);
        });
      });

      // Availability collection listener
      const availabilityRef = collection(this.firestore, this.COLLECTIONS.AVAILABILITY);
      onSnapshot(availabilityRef, (snapshot) => {
        this.ngZone.run(() => {
          const availability = this.processSnapshot<AvailabilityType>(snapshot);
          this.availabilitySubject.next(availability);
        });
      });

      // Doctor-Patient relations listener
      const doctorPatientRef = collection(this.firestore, this.COLLECTIONS.DOCTOR_PATIENT);
      onSnapshot(doctorPatientRef, (snapshot) => {
        this.ngZone.run(() => {
          const relations = this.processSnapshot<DoctorPatient>(snapshot);
          this.doctorPatientSubject.next(relations);
        });
      });
    });
  }

  /**
   * Process Firestore snapshot into typed array
   */
  private processSnapshot<T>(snapshot: QuerySnapshot<DocumentData>): T[] {
    return snapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as T));
  }

  /**
   * Clear all data when user logs out
   */
  private clearAllData(): void {
    this.usersSubject.next([]);
    this.doctorsSubject.next([]);
    this.patientsSubject.next([]);
    this.appointmentsSubject.next([]);
    this.medicalRecordsSubject.next([]);
    this.availabilitySubject.next([]);
    this.doctorPatientSubject.next([]);
    this.currentDoctorSubject.next(null);
    this.currentPatientSubject.next(null);
  }

  // ==================== CURRENT USER MANAGEMENT ====================

  /**
   * Set current doctor
   */
  setCurrentDoctor(doctor: UserType): void {
    this.currentDoctorSubject.next(doctor);
  }

  /**
   * Set current patient
   */
  setCurrentPatient(patient: UserType): void {
    this.currentPatientSubject.next(patient);
  }

  /**
   * Get current doctor
   */
  getCurrentDoctor(): UserType | null {
    return this.currentDoctorSubject.value;
  }

  /**
   * Get current patient
   */
  getCurrentPatient(): UserType | null {
    return this.currentPatientSubject.value;
  }

  // ==================== GETTERS FOR CURRENT DATA ====================

  /**
   * Get current users array
   */
  getUsers(): UserType[] {
    return this.usersSubject.value;
  }

  /**
   * Get current doctors array
   */
  getDoctors(): DoctorType[] {
    return this.doctorsSubject.value;
  }

  /**
   * Get current patients array
   */
  getPatients(): PatientType[] {
    return this.patientsSubject.value;
  }

  /**
   * Get current appointments array
   */
  getAppointments(): AppointmentType[] {
    return this.appointmentsSubject.value;
  }

  /**
   * Get current medical records array
   */
  getMedicalRecords(): MedicalRecordType[] {
    return this.medicalRecordsSubject.value;
  }

  /**
   * Get current availability array
   */
  getAvailability(): AvailabilityType[] {
    return this.availabilitySubject.value;
  }

  /**
   * Get current doctor-patient relations array
   */
  getDoctorPatientRelations(): DoctorPatient[] {
    return this.doctorPatientSubject.value;
  }

  // ==================== USER OPERATIONS ====================

  /**
   * Register a new user
   */
  registerUser(user: UserType): Observable<UserType> {
    return this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, this.COLLECTIONS.USERS, user.id);
      return from(setDoc(userDocRef, user)).pipe(
        map(() => user),
        tap(() => {
          console.log('User registered successfully:', user);
        }),
        catchError(error => {
          console.error('Error registering user:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Login user (find by email and password)
   */
  loginUser(email: string, password: string): UserType | null {
    const users = this.getUsers();
    return users.find(user => user.email === email && user.password === password) || null;
  }

  /**
   * Get user by ID
   */
  getUserById(userId: string): Observable<UserType | null> {
    return this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, this.COLLECTIONS.USERS, userId);
      return from(getDoc(userDocRef)).pipe(
        map(docSnap => {
          if (docSnap.exists()) {
            return { id: docSnap.id, ...docSnap.data() } as UserType;
          }
          return null;
        }),
        catchError(error => {
          console.error('Error getting user by ID:', error);
          return of(null);
        })
      );
    });
  }

  /**
   * Update user
   */
  updateUser(userId: string, userData: Partial<UserType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, this.COLLECTIONS.USERS, userId);
      return from(updateDoc(userDocRef, userData)).pipe(
        catchError(error => {
          console.error('Error updating user:', error);
          return throwError(() => error);
        })
      );
    });
  }

  // ==================== DOCTOR OPERATIONS ====================

  /**
   * Add doctor profile
   */
  addDoctor(doctor: DoctorType): Observable<DoctorType> {
    return this.ngZone.runOutsideAngular(() => {
      const doctorDocRef = doc(this.firestore, this.COLLECTIONS.DOCTORS, doctor.id);
      return from(setDoc(doctorDocRef, doctor)).pipe(
        map(() => doctor),
        catchError(error => {
          console.error('Error adding doctor:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get doctor by user ID
   */
  getDoctorByUserId(userId: string): Observable<DoctorType | null> {
    return this.ngZone.runOutsideAngular(() => {
      const doctorsRef = collection(this.firestore, this.COLLECTIONS.DOCTORS);
      const doctorQuery = query(doctorsRef, where('user_id', '==', userId));

      return from(getDocs(doctorQuery)).pipe(
        map(querySnapshot => {
          if (!querySnapshot.empty) {
            const doc = querySnapshot.docs[0];
            return { id: doc.id, ...doc.data() } as DoctorType;
          }
          return null;
        }),
        catchError(error => {
          console.error('Error getting doctor by user ID:', error);
          return of(null);
        })
      );
    });
  }

  /**
   * Update doctor profile
   */
  updateDoctor(doctorId: string, doctorData: Partial<DoctorType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const doctorDocRef = doc(this.firestore, this.COLLECTIONS.DOCTORS, doctorId);
      return from(updateDoc(doctorDocRef, doctorData)).pipe(
        catchError(error => {
          console.error('Error updating doctor:', error);
          return throwError(() => error);
        })
      );
    });
  }

  // ==================== PATIENT OPERATIONS ====================

  /**
   * Add patient profile
   */
  addPatient(patient: PatientType): Observable<PatientType> {
    return this.ngZone.runOutsideAngular(() => {
      const patientDocRef = doc(this.firestore, this.COLLECTIONS.PATIENTS, patient.id);
      return from(setDoc(patientDocRef, patient)).pipe(
        map(() => patient),
        catchError(error => {
          console.error('Error adding patient:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get patient by user ID
   */
  getPatientByUserId(userId: string): Observable<PatientType | null> {
    return this.ngZone.runOutsideAngular(() => {
      const patientsRef = collection(this.firestore, this.COLLECTIONS.PATIENTS);
      const patientQuery = query(patientsRef, where('user_id', '==', userId));

      return from(getDocs(patientQuery)).pipe(
        map(querySnapshot => {
          if (!querySnapshot.empty) {
            const doc = querySnapshot.docs[0];
            return { id: doc.id, ...doc.data() } as PatientType;
          }
          return null;
        }),
        catchError(error => {
          console.error('Error getting patient by user ID:', error);
          return of(null);
        })
      );
    });
  }

  /**
   * Update patient profile
   */
  updatePatient(patientId: string, patientData: Partial<PatientType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const patientDocRef = doc(this.firestore, this.COLLECTIONS.PATIENTS, patientId);
      return from(updateDoc(patientDocRef, patientData)).pipe(
        catchError(error => {
          console.error('Error updating patient:', error);
          return throwError(() => error);
        })
      );
    });
  }

  // ==================== APPOINTMENT OPERATIONS ====================

  /**
   * Add appointment
   */
  addAppointment(appointment: AppointmentType): Observable<AppointmentType> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentDocRef = doc(this.firestore, this.COLLECTIONS.APPOINTMENTS, appointment.id);
      return from(setDoc(appointmentDocRef, appointment)).pipe(
        map(() => appointment),
        catchError(error => {
          console.error('Error adding appointment:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Update appointment
   */
  updateAppointment(appointmentId: string, appointmentData: Partial<AppointmentType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentDocRef = doc(this.firestore, this.COLLECTIONS.APPOINTMENTS, appointmentId);
      return from(updateDoc(appointmentDocRef, appointmentData)).pipe(
        catchError(error => {
          console.error('Error updating appointment:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get appointments by doctor ID
   */
  getAppointmentsByDoctorId(doctorId: string): AppointmentType[] {
    return this.getAppointments().filter(appointment => appointment.doctor_id === doctorId);
  }

  /**
   * Get appointments by patient ID and status
   */
  getAppointmentsByPatientId(patientId: string, status?: AppointmentStatusType): AppointmentType[] {
    const appointments = this.getAppointments().filter(appointment => appointment.patient_id === patientId);
    return status ? appointments.filter(appointment => appointment.status === status) : appointments;
  }

  /**
   * Delete appointment
   */
  deleteAppointment(appointmentId: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentDocRef = doc(this.firestore, this.COLLECTIONS.APPOINTMENTS, appointmentId);
      return from(deleteDoc(appointmentDocRef)).pipe(
        catchError(error => {
          console.error('Error deleting appointment:', error);
          return throwError(() => error);
        })
      );
    });
  }

  // ==================== MEDICAL RECORD OPERATIONS ====================

  /**
   * Add medical record
   */
  addMedicalRecord(record: MedicalRecordType): Observable<MedicalRecordType> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, this.COLLECTIONS.MEDICAL_RECORDS, record.id);
      return from(setDoc(recordDocRef, record)).pipe(
        map(() => record),
        catchError(error => {
          console.error('Error adding medical record:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Update medical record
   */
  updateMedicalRecord(recordId: string, recordData: Partial<MedicalRecordType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, this.COLLECTIONS.MEDICAL_RECORDS, recordId);
      return from(updateDoc(recordDocRef, recordData)).pipe(
        catchError(error => {
          console.error('Error updating medical record:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get medical records by appointment ID
   */
  getMedicalRecordsByAppointmentId(appointmentId: string): MedicalRecordType[] {
    return this.getMedicalRecords().filter(record => record.appointment_id === appointmentId);
  }

  // ==================== AVAILABILITY OPERATIONS ====================

  /**
   * Add availability slot
   */
  addAvailability(availability: AvailabilityType): Observable<AvailabilityType> {
    return this.ngZone.runOutsideAngular(() => {
      const availabilityDocRef = doc(this.firestore, this.COLLECTIONS.AVAILABILITY, availability.id);
      return from(setDoc(availabilityDocRef, availability)).pipe(
        map(() => availability),
        catchError(error => {
          console.error('Error adding availability:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Update availability slot
   */
  updateAvailability(availabilityId: string, availabilityData: Partial<AvailabilityType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const availabilityDocRef = doc(this.firestore, this.COLLECTIONS.AVAILABILITY, availabilityId);
      return from(updateDoc(availabilityDocRef, availabilityData)).pipe(
        catchError(error => {
          console.error('Error updating availability:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get availability by doctor ID
   */
  getAvailabilityByDoctorId(doctorId: string): AvailabilityType[] {
    return this.getAvailability().filter(slot => slot.doctor_id === doctorId);
  }

  /**
   * Delete availability slot
   */
  deleteAvailability(availabilityId: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const availabilityDocRef = doc(this.firestore, this.COLLECTIONS.AVAILABILITY, availabilityId);
      return from(deleteDoc(availabilityDocRef)).pipe(
        catchError(error => {
          console.error('Error deleting availability:', error);
          return throwError(() => error);
        })
      );
    });
  }

  // ==================== DOCTOR-PATIENT RELATION OPERATIONS ====================

  /**
   * Add doctor-patient relation
   */
  addDoctorPatientRelation(relation: DoctorPatient): Observable<DoctorPatient> {
    return this.ngZone.runOutsideAngular(() => {
      const relationDocRef = doc(this.firestore, this.COLLECTIONS.DOCTOR_PATIENT, relation.id);
      return from(setDoc(relationDocRef, relation)).pipe(
        map(() => relation),
        catchError(error => {
          console.error('Error adding doctor-patient relation:', error);
          return throwError(() => error);
        })
      );
    });
  }

  /**
   * Get doctor-patient relations by doctor ID
   */
  getDoctorPatientRelationsByDoctorId(doctorId: string): DoctorPatient[] {
    return this.getDoctorPatientRelations().filter(relation => relation.doctor_id === doctorId);
  }

  /**
   * Get doctor-patient relations by patient ID
   */
  getDoctorPatientRelationsByPatientId(patientId: string): DoctorPatient[] {
    return this.getDoctorPatientRelations().filter(relation => relation.patient_id === patientId);
  }

  // ==================== DEVELOPMENT/TESTING METHODS ====================

  /**
   * DANGER: Reset all Firebase data - FOR DEVELOPMENT/TESTING ONLY
   * This will delete ALL data from ALL collections
   */
  resetAllFirebaseData(): Observable<{ success: boolean; message: string; deletedCounts: any }> {
    console.warn('🚨 DANGER: Resetting ALL Firebase data!');

    return this.ngZone.runOutsideAngular(() => {
      const batch = writeBatch(this.firestore);
      const deletedCounts = {
        users: 0,
        doctors: 0,
        patients: 0,
        appointments: 0,
        medicalRecords: 0,
        availability: 0,
        doctorPatientRelations: 0
      };

      // Get all collections and their documents
      const collections = [
        { name: this.COLLECTIONS.USERS, key: 'users' },
        { name: this.COLLECTIONS.DOCTORS, key: 'doctors' },
        { name: this.COLLECTIONS.PATIENTS, key: 'patients' },
        { name: this.COLLECTIONS.APPOINTMENTS, key: 'appointments' },
        { name: this.COLLECTIONS.MEDICAL_RECORDS, key: 'medicalRecords' },
        { name: this.COLLECTIONS.AVAILABILITY, key: 'availability' },
        { name: this.COLLECTIONS.DOCTOR_PATIENT, key: 'doctorPatientRelations' }
      ];

      // Create observables for each collection deletion
      const deletionObservables = collections.map(col =>
        from(getDocs(collection(this.firestore, col.name))).pipe(
          switchMap(querySnapshot => {
            // Add all documents to batch for deletion
            querySnapshot.docs.forEach(docSnapshot => {
              batch.delete(docSnapshot.ref);
              (deletedCounts as any)[col.key]++;
            });
            return of(col);
          }),
          catchError(error => {
            console.error(`Error getting documents from ${col.name}:`, error);
            return of(col);
          })
        )
      );

      // Execute all collection queries, then commit the batch delete
      return from(Promise.all(deletionObservables.map(obs => obs.pipe(take(1)).toPromise()))).pipe(
        switchMap(() => {
          console.log('Deleting documents:', deletedCounts);
          return from(batch.commit());
        }),
        map(() => {
          const totalDeleted = Object.values(deletedCounts).reduce((sum: number, count) => sum + (count as number), 0);
          const result = {
            success: true,
            message: `Successfully deleted ${totalDeleted} documents from Firebase`,
            deletedCounts
          };
          console.log('Firebase reset complete:', result);
          return result;
        }),
        catchError(error => {
          console.error('Error resetting Firebase data:', error);
          return of({
            success: false,
            message: `Failed to reset Firebase data: ${error.message}`,
            deletedCounts
          });
        })
      );
    });
  }

  /**
   * DANGER: Clear current user state - FOR DEVELOPMENT/TESTING ONLY
   */
  clearCurrentUserState(): void {
    console.warn('🚨 Clearing current user state');
    this.currentDoctorSubject.next(null);
    this.currentPatientSubject.next(null);
  }
}
