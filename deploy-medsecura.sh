#!/bin/bash

# MedSecura Comprehensive Deployment Script
# This script builds the Angular app and deploys everything to Firebase

echo "========================================="
echo "    MedSecura Deployment"
echo "========================================="
echo "Project ID: medsecura-5abb2"
echo "Project Number: 479273653321"
echo "Web API Key: AIzaSyCXgGxYTL0Exbgt5Ukt0oW2fdpdUwBMCys"
echo "========================================="

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null
then
    echo "Firebase CLI is not installed. Installing it now..."
    npm install -g firebase-tools
fi

# Login to Firebase if not already logged in
echo "Checking Firebase login status..."
firebase login

# Set the default project
echo "Setting default project to medsecura-5abb2..."
firebase use medsecura-5abb2

# Build the Angular application
echo "Building Angular application..."
cd Desktop/client
npm install
ng build --configuration production
cd ../..

# Create the needed files if they don't exist
if [ ! -f "firestore.rules" ] || [ ! -f "storage.rules" ] || [ ! -f "firestore.indexes.json" ]; then
    echo "Creating Firebase configuration files..."
    
    # Create Firestore security rules
    cat > firestore.rules << 'EOL'
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Function to check if user has a specific role
    function hasRole(role) {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    // Function to check if user is an admin
    function isAdmin() {
      return hasRole('admin');
    }
    
    // Function to check if user is a doctor
    function isDoctor() {
      return hasRole('doctor');
    }
    
    // Function to check if user is a patient
    function isPatient() {
      return hasRole('patient');
    }
    
    // Function to check if user is a receptionist
    function isReceptionist() {
      return hasRole('receptionist');
    }
    
    // Function to check if user is the owner of the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Function to check if a doctor is assigned to a patient
    function isDoctorForPatient(patientId) {
      return isDoctor() && exists(/databases/$(database)/documents/doctorPatients/$(request.auth.uid + '_' + patientId));
    }
    
    // Users collection
    match /users/{userId} {
      // Anyone can read public profile info (name, role, etc.)
      allow read: if isAuthenticated();
      
      // Only admins can create new users through Firestore
      // (normal user creation is handled through Firebase Auth)
      allow create: if isAdmin();
      
      // Users can only update their own profiles, admins can update any profile
      allow update: if isOwner(userId) || isAdmin();
      
      // Only admins can delete users through Firestore
      allow delete: if isAdmin();
    }
    
    // Appointments collection
    match /appointments/{appointmentId} {
      // Admins can read all appointments
      // Doctors can read appointments where they are the doctor
      // Patients can read their own appointments
      // Receptionists can read all appointments
      allow read: if isAdmin() || 
                   (isDoctor() && resource.data.doctorId == request.auth.uid) ||
                   (isPatient() && resource.data.patientId == request.auth.uid) ||
                   isReceptionist();
      
      // Admins and receptionists can create appointments
      // Patients can create their own appointments
      allow create: if isAdmin() || 
                      isReceptionist() || 
                      (isPatient() && request.resource.data.patientId == request.auth.uid);
      
      // Admins and receptionists can update appointments
      // Doctors can update appointments where they are the doctor
      // Patients can update their own appointments (e.g. cancel)
      allow update: if isAdmin() || 
                      isReceptionist() || 
                      (isDoctor() && resource.data.doctorId == request.auth.uid) ||
                      (isPatient() && resource.data.patientId == request.auth.uid);
      
      // Only admins and receptionists can delete appointments
      allow delete: if isAdmin() || isReceptionist();
    }
    
    // Doctor-Patient relationships
    match /doctorPatients/{relationId} {
      // Admins and receptionists can read all relationships
      // Doctors can read relationships where they are the doctor
      // Patients can read relationships where they are the patient
      allow read: if isAdmin() || 
                   isReceptionist() || 
                   (isDoctor() && resource.data.doctorId == request.auth.uid) ||
                   (isPatient() && resource.data.patientId == request.auth.uid);
      
      // Only admins and receptionists can create/update/delete relationships
      allow create, update, delete: if isAdmin() || isReceptionist();
    }
    
    // Medical Records
    match /medicalRecords/{recordId} {
      // Admins can read all records
      // Doctors can read records where they are the doctor or records of their patients
      // Patients can read only their own records
      allow read: if isAdmin() || 
                   (isDoctor() && (resource.data.doctorId == request.auth.uid || isDoctorForPatient(resource.data.patientId))) ||
                   (isPatient() && resource.data.patientId == request.auth.uid);
      
      // Admins can create records
      // Doctors can create records for their patients
      allow create: if isAdmin() || 
                      (isDoctor() && isDoctorForPatient(request.resource.data.patientId));
      
      // Admins can update records
      // Doctors can update records they created or for their patients
      allow update: if isAdmin() || 
                      (isDoctor() && (resource.data.doctorId == request.auth.uid || isDoctorForPatient(resource.data.patientId)));
      
      // Only admins and the doctor who created the record can delete it
      allow delete: if isAdmin() || 
                      (isDoctor() && resource.data.doctorId == request.auth.uid);
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
EOL

    # Create Storage security rules
    cat > storage.rules << 'EOL'
rules_version = '2';

// Firebase Storage Rules for MedSecura
service firebase.storage {
  match /b/{bucket}/o {
    // Function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Medical records folder
    match /medical-records/{fileName} {
      // Allow read if:
      // - User is authenticated and
      // - File metadata contains a patientId and a doctorId and
      // - User is the patient or the doctor who created the record or an admin
      allow read: if isAuthenticated() &&
                    (request.auth.token.role == 'admin' ||
                     request.auth.uid == resource.metadata.patientId ||
                     request.auth.uid == resource.metadata.doctorId);
      
      // Allow create if:
      // - User is authenticated and
      // - File is under 10MB and
      // - File is an allowed content type and
      // - User is a doctor or admin
      allow create: if isAuthenticated() &&
                       request.resource.size < 10 * 1024 * 1024 &&
                       (request.resource.contentType.matches('image/.*') ||
                        request.resource.contentType.matches('application/pdf') ||
                        request.resource.contentType.matches('text/plain') ||
                        request.resource.contentType.matches('application/msword') ||
                        request.resource.contentType.matches('application/vnd.openxmlformats-officedocument.wordprocessingml.document')) &&
                       (request.auth.token.role == 'doctor' || request.auth.token.role == 'admin') &&
                       // Ensure metadata contains patientId and doctorId
                       request.resource.metadata.patientId != null &&
                       request.resource.metadata.doctorId != null;
      
      // Allow update to only change metadata, not the file itself
      allow update: if isAuthenticated() &&
                       request.resource.size == resource.size &&
                       request.resource.contentType == resource.contentType &&
                       (request.auth.token.role == 'admin' ||
                        request.auth.uid == resource.metadata.doctorId);
      
      // Allow delete if:
      // - User is authenticated and
      // - User is the doctor who created the file or an admin
      allow delete: if isAuthenticated() &&
                       (request.auth.token.role == 'admin' ||
                        request.auth.uid == resource.metadata.doctorId);
    }
    
    // User profile pictures folder
    match /profile-pictures/{userId} {
      // Anyone authenticated can view profile pictures
      allow read: if isAuthenticated();
      
      // Users can only upload their own profile picture
      // Profile pictures must be images and under 5MB
      allow create, update: if isAuthenticated() &&
                               request.auth.uid == userId &&
                               request.resource.size < 5 * 1024 * 1024 &&
                               request.resource.contentType.matches('image/.*');
      
      // Users can only delete their own profile picture or admin can delete any
      allow delete: if isAuthenticated() &&
                       (request.auth.uid == userId || request.auth.token.role == 'admin');
    }
    
    // Default deny for all other files
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
EOL

    # Create Firestore indexes
    cat > firestore.indexes.json << EOL
{
  "indexes": [
    {
      "collectionGroup": "appointments",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "doctorId", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "appointmentDate", "order": "ASCENDING" }
      ]
    },
    {
      "collectionGroup": "appointments",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "patientId", "order": "ASCENDING" },
        { "fieldPath": "appointmentDate", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "appointments",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "doctorId", "order": "ASCENDING" },
        { "fieldPath": "appointmentDate", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "medicalRecords",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "patientId", "order": "ASCENDING" },
        { "fieldPath": "date", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "medicalRecords",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "doctorId", "order": "ASCENDING" },
        { "fieldPath": "date", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "users",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "role", "order": "ASCENDING" },
        { "fieldPath": "lastName", "order": "ASCENDING" }
      ]
    }
  ],
  "fieldOverrides": []
}
EOL

fi

# Deploy Firestore and Storage Rules
echo "Deploying Firestore and Storage rules..."
firebase deploy --only firestore:rules,storage

# Deploy the Angular app to Firebase Hosting
echo "Deploying Angular app to Firebase Hosting..."
firebase deploy --only hosting

echo "========================================="
echo "MedSecura deployment complete!"
echo "Your application is now live at: https://medsecura-5abb2.web.app"
echo "=========================================" 