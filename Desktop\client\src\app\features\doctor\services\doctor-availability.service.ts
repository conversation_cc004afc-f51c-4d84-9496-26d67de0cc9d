import { Injectable } from '@angular/core';
import { Observable, from, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  addDoc,
  deleteDoc,
  doc,
  getDocs,
  query,
  where,
  collectionData
} from '@angular/fire/firestore';
import { AvailabilityType } from '../../../type';

@Injectable({
  providedIn: 'root'
})
export class DoctorAvailabilityService {
  private readonly collectionName = 'doctorAvailability';

  constructor(
    private firestore: Firestore
  ) {
    console.log('DoctorAvailabilityService initialized');
    console.log('Firestore instance:', !!this.firestore ? 'Available' : 'Not available');
  }

  // Add new doctor availability
  addAvailability(availability: Omit<AvailabilityType, 'id'>): Observable<AvailabilityType> {
    console.log('Adding availability to Firebase collection:', this.collectionName);
    console.log('Availability data:', availability);

    try {
      const availabilityCollection = collection(this.firestore, this.collectionName);
      console.log('Collection reference created');

      return from(addDoc(availabilityCollection, availability)).pipe(
        tap(docRef => console.log('Document added with ID:', docRef.id)),
        map(docRef => {
          return {
            id: docRef.id,
            ...availability
          } as AvailabilityType;
        }),
        catchError(error => {
          console.error('Error adding availability:', error);
          console.error('Error code:', error.code);
          console.error('Error message:', error.message);
          return throwError(() => error);
        })
      );
    } catch (error) {
      console.error('Exception when trying to access Firestore:', error);
      return throwError(() => error);
    }
  }

  // Get all availabilities for a specific doctor
  getAvailabilitiesByDoctorId(doctorId: string): Observable<AvailabilityType[]> {
    console.log('Getting availabilities for doctor ID:', doctorId);

    try {
      const availabilityCollection = collection(this.firestore, this.collectionName);
      const doctorAvailabilityQuery = query(
        availabilityCollection,
        where('doctor_id', '==', doctorId)
      );

      return collectionData(doctorAvailabilityQuery, { idField: 'id' }).pipe(
        tap(availabilities => console.log('Retrieved availabilities:', availabilities.length)),
        map(availabilities => availabilities as AvailabilityType[]),
        catchError(error => {
          console.error('Error fetching availabilities:', error);
          return throwError(() => error);
        })
      );
    } catch (error) {
      console.error('Exception when trying to access Firestore:', error);
      return throwError(() => error);
    }
  }

  // Delete an availability
  removeAvailability(id: string): Observable<void> {
    console.log('Removing availability with ID:', id);

    try {
      const docRef = doc(this.firestore, `${this.collectionName}/${id}`);

      return from(deleteDoc(docRef)).pipe(
        tap(() => console.log('Document successfully deleted')),
        map(() => undefined),
        catchError(error => {
          console.error('Error removing availability:', error);
          return throwError(() => error);
        })
      );
    } catch (error) {
      console.error('Exception when trying to access Firestore:', error);
      return throwError(() => error);
    }
  }

  // Get availabilities for a specific date
  getAvailabilitiesByDate(doctorId: string, date: string): Observable<AvailabilityType[]> {
    console.log('Getting availabilities for doctor ID:', doctorId, 'and date:', date);

    try {
      const availabilityCollection = collection(this.firestore, this.collectionName);
      const dateAvailabilityQuery = query(
        availabilityCollection,
        where('doctor_id', '==', doctorId),
        where('date', '==', date)
      );

      return collectionData(dateAvailabilityQuery, { idField: 'id' }).pipe(
        tap(availabilities => console.log('Retrieved availabilities for date:', availabilities.length)),
        map(availabilities => availabilities as AvailabilityType[]),
        catchError(error => {
          console.error('Error fetching availabilities by date:', error);
          return throwError(() => error);
        })
      );
    } catch (error) {
      console.error('Exception when trying to access Firestore:', error);
      return throwError(() => error);
    }
  }
}
