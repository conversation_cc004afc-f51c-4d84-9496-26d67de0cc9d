import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FirebaseDbService } from '../../../../core/services/firebase-db.service';

@Component({
  selector: 'app-firebase-reset',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="firebase-reset-container">
      <!-- Development Warning Banner -->
      <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <i class="bi bi-exclamation-triangle text-red-400 text-lg"></i>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              🚨 DEVELOPMENT TOOL - DANGER ZONE
            </h3>
            <div class="mt-2 text-sm text-red-700">
              <p>This tool will permanently delete ALL data from Firebase. Use only for development and testing!</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Reset Button -->
      <div class="bg-white rounded-lg shadow-md p-6 border border-red-200">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900">Reset Firebase Database</h3>
            <p class="text-sm text-gray-600 mt-1">
              Delete all data from Firebase collections to start fresh
            </p>
          </div>
          <div class="flex-shrink-0">
            <i class="bi bi-database-x text-red-500 text-2xl"></i>
          </div>
        </div>

        <!-- Confirmation Steps -->
        <div class="space-y-4 mb-6" *ngIf="!showConfirmation">
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-medium text-gray-900 mb-2">What will be deleted:</h4>
            <ul class="text-sm text-gray-700 space-y-1">
              <li>• All user accounts and profiles</li>
              <li>• All doctor and patient data</li>
              <li>• All appointments and schedules</li>
              <li>• All medical records</li>
              <li>• All availability slots</li>
              <li>• All doctor-patient relationships</li>
            </ul>
          </div>

          <button
            (click)="showConfirmation = true"
            class="w-full bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
            <i class="bi bi-exclamation-triangle mr-2"></i>
            I Want to Reset Firebase Database
          </button>
        </div>

        <!-- Confirmation Dialog -->
        <div class="space-y-4 mb-6" *ngIf="showConfirmation && !loading">
          <div class="bg-red-50 border border-red-200 p-4 rounded-lg">
            <h4 class="font-bold text-red-800 mb-2">⚠️ FINAL WARNING</h4>
            <p class="text-sm text-red-700 mb-3">
              This action cannot be undone. All data will be permanently deleted from Firebase.
            </p>
            <div class="flex items-center space-x-2 mb-3">
              <input
                type="checkbox"
                id="confirmDelete"
                [(ngModel)]="confirmDelete"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
              <label for="confirmDelete" class="text-sm text-red-700">
                I understand this will delete ALL data permanently
              </label>
            </div>
            <div class="flex items-center space-x-2">
              <input
                type="checkbox"
                id="confirmDevelopment"
                [(ngModel)]="confirmDevelopment"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
              <label for="confirmDevelopment" class="text-sm text-red-700">
                I confirm this is for development/testing only
              </label>
            </div>
          </div>

          <div class="flex space-x-3">
            <button
              (click)="resetFirebase()"
              [disabled]="!confirmDelete || !confirmDevelopment"
              class="flex-1 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
              <i class="bi bi-trash mr-2"></i>
              DELETE ALL DATA
            </button>
            <button
              (click)="cancelReset()"
              class="flex-1 bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
              <i class="bi bi-x-circle mr-2"></i>
              Cancel
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div class="space-y-4 mb-6" *ngIf="loading">
          <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
            <div class="flex items-center">
              <i class="bi bi-arrow-repeat animate-spin text-blue-600 mr-3"></i>
              <div>
                <h4 class="font-medium text-blue-800">Resetting Firebase Database...</h4>
                <p class="text-sm text-blue-700 mt-1">{{ progressMessage }}</p>
              </div>
            </div>
          </div>

          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 [style.width.%]="progress"></div>
          </div>
        </div>

        <!-- Result Display -->
        <div class="space-y-4" *ngIf="resetResult">
          <div class="p-4 rounded-lg border"
               [ngClass]="resetResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <i class="text-lg"
                   [ngClass]="resetResult.success ? 'bi bi-check-circle text-green-500' : 'bi bi-x-circle text-red-500'"></i>
              </div>
              <div class="ml-3 flex-1">
                <h4 class="font-semibold"
                    [ngClass]="resetResult.success ? 'text-green-800' : 'text-red-800'">
                  {{ resetResult.success ? 'Reset Successful!' : 'Reset Failed' }}
                </h4>
                <p class="text-sm mt-1"
                   [ngClass]="resetResult.success ? 'text-green-700' : 'text-red-700'">
                  {{ resetResult.message }}
                </p>

                <!-- Success Details -->
                <div *ngIf="resetResult.success && resetResult.deletedCounts" class="mt-3">
                  <h5 class="font-medium text-green-800 mb-2">Deleted Data:</h5>
                  <div class="grid grid-cols-2 gap-2 text-sm text-green-700">
                    <div>Users: {{ resetResult.deletedCounts.users }}</div>
                    <div>Doctors: {{ resetResult.deletedCounts.doctors }}</div>
                    <div>Patients: {{ resetResult.deletedCounts.patients }}</div>
                    <div>Appointments: {{ resetResult.deletedCounts.appointments }}</div>
                    <div>Medical Records: {{ resetResult.deletedCounts.medicalRecords }}</div>
                    <div>Availability: {{ resetResult.deletedCounts.availability }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Reset Again Button -->
          <button
            (click)="resetState()"
            class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg transition duration-200">
            <i class="bi bi-arrow-clockwise mr-2"></i>
            Reset Tool
          </button>
        </div>
      </div>

      <!-- Additional Actions -->
      <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h4 class="font-medium text-yellow-800 mb-2">Additional Development Actions:</h4>
        <div class="space-y-2">
          <button
            (click)="clearUserState()"
            class="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded transition duration-200">
            <i class="bi bi-person-x mr-2"></i>
            Clear Current User State
          </button>
          <button
            (click)="refreshPage()"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-200">
            <i class="bi bi-arrow-clockwise mr-2"></i>
            Refresh Application
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .animate-spin {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .firebase-reset-container {
      max-width: 600px;
      margin: 0 auto;
    }
  `]
})
export class FirebaseResetComponent {
  private firebaseDbService = inject(FirebaseDbService);

  showConfirmation = false;
  confirmDelete = false;
  confirmDevelopment = false;
  loading = false;
  progress = 0;
  progressMessage = '';
  resetResult: { success: boolean; message: string; deletedCounts?: any } | null = null;

  resetFirebase(): void {
    if (!this.confirmDelete || !this.confirmDevelopment) {
      return;
    }

    this.loading = true;
    this.progress = 0;
    this.progressMessage = 'Initializing reset...';
    this.resetResult = null;

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      if (this.progress < 90) {
        this.progress += 15;
        this.updateProgressMessage();
      }
    }, 500);

    this.firebaseDbService.resetAllFirebaseData().subscribe({
      next: (result) => {
        clearInterval(progressInterval);
        this.progress = 100;
        this.progressMessage = 'Reset complete!';
        this.resetResult = result;
        this.loading = false;
        this.showConfirmation = false;
        this.confirmDelete = false;
        this.confirmDevelopment = false;
      },
      error: (error) => {
        clearInterval(progressInterval);
        this.loading = false;
        this.resetResult = {
          success: false,
          message: `Reset failed: ${error.message}`
        };
        this.showConfirmation = false;
        this.confirmDelete = false;
        this.confirmDevelopment = false;
      }
    });
  }

  private updateProgressMessage(): void {
    const messages = [
      'Connecting to Firebase...',
      'Fetching collections...',
      'Preparing batch deletion...',
      'Deleting user data...',
      'Deleting appointments...',
      'Deleting medical records...',
      'Finalizing reset...'
    ];
    const index = Math.floor(this.progress / 15);
    this.progressMessage = messages[index] || 'Processing...';
  }

  cancelReset(): void {
    this.showConfirmation = false;
    this.confirmDelete = false;
    this.confirmDevelopment = false;
  }

  resetState(): void {
    this.showConfirmation = false;
    this.confirmDelete = false;
    this.confirmDevelopment = false;
    this.loading = false;
    this.progress = 0;
    this.progressMessage = '';
    this.resetResult = null;
  }

  clearUserState(): void {
    this.firebaseDbService.clearCurrentUserState();
    alert('Current user state cleared! You may need to log in again.');
  }

  refreshPage(): void {
    window.location.reload();
  }
}
