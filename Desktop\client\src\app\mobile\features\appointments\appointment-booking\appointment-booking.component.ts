import {
  Component,
  OnInit,
  <PERSON><PERSON><PERSON>roy,
  signal,
  WritableSignal,
  inject,
  Ng<PERSON>one
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { BackButtonComponent } from '../../shared/back-button/back-button.component';
import { NavbarComponent } from '../../shared/navbar/navbar.component';
import {
  AppointmentService,
  AppointmentPatient,
  Doctor,
  DoctorSchedule,
} from '../service/appointment.service';
import { Subject, from, of, forkJoin, catchError } from 'rxjs';
import { takeUntil, finalize, switchMap, map } from 'rxjs/operators';
import {
  AppointmentType,
  AvailabilityType,
  DoctorPatient,
  DoctorType,
  UserType,
} from '../../../../type';
import { Db } from '../../../../db';
import { FirebaseAppointmentService } from '../../../../features/shared/services/firebase-appointment.service';
import {
  collection,
  query,
  where,
  doc,
  getDocs,
  getDoc,
  DocumentSnapshot,
  QuerySnapshot,
  DocumentData
} from 'firebase/firestore';

// Interface for slots grouped by date
interface DateGroup {
  date: string;
  slots: AvailabilityType[];
}

interface DaySchedule {
  hasSlots: boolean;
  startTime: string;
  endTime: string;
  availableSlots: number;
}

@Component({
  selector: 'app-appointment-booking',
  standalone: true,
  templateUrl: './appointment-booking.component.html',
  styleUrls: ['./appointment-booking.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    RouterLink,
    BackButtonComponent,
    NavbarComponent,
  ],
})
export class AppointmentBookingComponent implements OnInit, OnDestroy {
  // Properties for doctor selection
  selectedDoctorId: string | null = null;
  showTableView = false;
  isFirstLoad = true;

  // Properties for date groups and dropdowns
  groupedAvailability: DateGroup[] = [];
  openDateGroups: boolean[] = [];
  isLoading = false;

  // Existing properties
  selectedDoctor: Doctor | null = null;
  selectedDate: Date = new Date();
  selectedSlot: AppointmentPatient | null = null;
  availableSlots: AppointmentPatient[] = [];
  datePickerOpen = false;
  calendarDates: Date[] = [];
  viewMonth: Date = new Date();
  showModal = false;
  medicalConcern = '';
  bookingSuccess = false;
  bookingError = '';

  // Weekly schedule section
  showWeeklySchedule = false;
  weeklySchedule: DaySchedule[] = [];
  doctorSchedules: DoctorSchedule[] = [];
  fetchingSchedules = false;

  weekDays = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
  dateLabels: { date: Date; day: string; dateNum: number }[] = [];

  private destroy$ = new Subject<void>();

  //================>

  myDoctors: WritableSignal<UserType[]> = signal<UserType[]>([]);
  db: Db = inject(Db);
  doctorAvailability: WritableSignal<AvailabilityType[]> = signal<
    AvailabilityType[]
  >([]);

  // Add Firebase Service
  private firebaseService = inject(FirebaseAppointmentService);
  private ngZone = inject(NgZone);

  // Add a property to store the availability slot being booked
  private slotToBook: AvailabilityType | null = null;

  constructor(
    private appointmentService: AppointmentService,
    private router: Router
  ) {}

  ngOnInit() {
    this.findMyDoctors();
    this.isFirstLoad = true;
  }

  toggleDoctorAvailability(doctor_id: string): void {
    this.isFirstLoad = false;

    // If clicking the same doctor, close the availability view
    if (this.selectedDoctorId === doctor_id) {
      this.selectedDoctorId = null;
      this.groupedAvailability = [];
      this.doctorAvailability.set([]);
      return;
    }

    // Otherwise, fetch and show the availability
    this.onViewDoctorAvailability(doctor_id);
  }

  onViewDoctorAvailability(doctor_id: string): void {
    this.isLoading = true;
    this.selectedDoctorId = doctor_id;
    this.isFirstLoad = false;

    console.log(`Fetching availability for doctor ID: ${doctor_id}`);

    // First ensure the doctorId is properly formatted and stored
    this.validateAndStoreDoctorId(doctor_id);

    // First synchronize data between Firebase and localStorage
    this.firebaseService.syncAvailabilityData(doctor_id)
      .pipe(
        takeUntil(this.destroy$),
        catchError(error => {
          console.error('Error synchronizing availability data:', error);
          // Continue with loading even if sync fails
          return of(null);
        })
      )
      .subscribe({
        next: () => {
          console.log('Availability data synchronized, now loading data');

          // Now load the availability from Firebase
          this.firebaseService.loadDoctorAvailability(doctor_id)
            .pipe(
              takeUntil(this.destroy$),
              finalize(() => this.isLoading = false)
            )
            .subscribe({
              next: (availability) => {
                console.log(`Loaded ${availability.length} availability slots from Firebase after sync`);

                if (availability.length > 0) {
                  // Sort availability by date and time
                  const sortedAvailability = [...availability].sort((a, b) => {
                    // First compare dates
                    const dateComparison = new Date(a.date).getTime() - new Date(b.date).getTime();
                    if (dateComparison !== 0) return dateComparison;

                    // If dates are the same, compare start times
                    return a.start_time.localeCompare(b.start_time);
                  });

                  // Group availability by date
                  this.groupAvailabilityByDate(sortedAvailability);

                  // Set the selected doctor information
                  const doctorInfo = this.db.userTable().find(user => user.id === doctor_id);
                  if (doctorInfo) {
                    console.log(`Viewing availability for Dr. ${doctorInfo.firstname} ${doctorInfo.lastname}`);
                  }

                  // Update the availability signal
                  this.doctorAvailability.set(sortedAvailability);
                } else {
                  // No availability found in Firebase
                  console.log('No availability slots found for this doctor');
                  this.groupedAvailability = [];
                  this.doctorAvailability.set([]);
                }
              },
              error: (error) => {
                console.error('Error loading availability from Firebase:', error);
                this.isLoading = false;

                // Clear the UI instead of using local storage
                this.groupedAvailability = [];
                this.doctorAvailability.set([]);
              }
            });
        },
        error: (error) => {
          console.error('Failed to synchronize availability data:', error);
          this.isLoading = false;

          // Fallback to direct Firebase query without sync
          this.loadDoctorAvailabilityDirectly(doctor_id);
        }
      });
  }

  // Fallback method to load availability without synchronization
  private loadDoctorAvailabilityDirectly(doctorId: string): void {
    this.firebaseService.loadDoctorAvailability(doctorId)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (availability) => {
          console.log(`Loaded ${availability.length} availability slots directly from Firebase`);

          if (availability.length > 0) {
            const sortedAvailability = [...availability].sort((a, b) => {
              const dateComparison = new Date(a.date).getTime() - new Date(b.date).getTime();
              if (dateComparison !== 0) return dateComparison;
              return a.start_time.localeCompare(b.start_time);
            });

            this.groupAvailabilityByDate(sortedAvailability);
            this.doctorAvailability.set(sortedAvailability);
          } else {
            // No availability found in Firebase
            console.log('No availability slots found for this doctor');
            this.groupedAvailability = [];
            this.doctorAvailability.set([]);
          }
        },
        error: (error) => {
          console.error('Error loading availability directly from Firebase:', error);
          // Clear the UI instead of using local storage
          this.groupedAvailability = [];
          this.doctorAvailability.set([]);
        }
      });
  }

  // Validate and ensure doctor ID is consistent
  private validateAndStoreDoctorId(doctorId: string): void {
    if (!doctorId) {
      console.error('Invalid doctor ID (empty or null)');
      return;
    }

    // Check if the doctor exists in the database
    const doctor = this.db.userTable().find(user => user.id === doctorId);
    if (!doctor) {
      console.warn(`Doctor with ID ${doctorId} not found in user table`);
    } else {
      console.log(`Found doctor: ${doctor.firstname} ${doctor.lastname}`);
    }
  }

  // Group availability slots by date
  groupAvailabilityByDate(slots: AvailabilityType[]): void {
    const grouped: { [date: string]: AvailabilityType[] } = {};

    // Group slots by date
    slots.forEach(slot => {
      if (!grouped[slot.date]) {
        grouped[slot.date] = [];
      }
      grouped[slot.date].push(slot);
    });

    // Convert to array format for template
    this.groupedAvailability = Object.keys(grouped).map(date => ({
      date,
      slots: grouped[date]
    }));

    // Initialize open state for each date group
    this.openDateGroups = this.groupedAvailability.map((_, index) => index === 0); // Open first group by default
  }

  // Toggle date group dropdown
  toggleDateGroup(index: number): void {
    this.openDateGroups[index] = !this.openDateGroups[index];
  }

  // Format date for header display
  formatDateHeader(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Format time for display (e.g., "2:30 PM")
  formatTime(timeString: string): string {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHour = hours % 12 || 12;
    return `${displayHour}:${minutes.toString().padStart(2, '0')} ${period}`;
  }

  isTimeSlotAvailable(doctorId: string, date: string, startTime: string): boolean {
    // Check if there's already an appointment for this doctor, date, and time
    const existingAppointment = this.db.appointmentTable().find(
      app => app.doctor_id === doctorId &&
             app.date === date &&
             app.time === startTime
    );

    return !existingAppointment;
  }

  findMyDoctors() {
    // First get the current patient ID
    const currentPatientId = this.db.current_patient()?.id;
    if (!currentPatientId) {
      console.error('No current patient ID found');
      return;
    }

    console.log('Finding doctors for patient ID:', currentPatientId);

    // Clear existing doctors list
    this.myDoctors.set([]);

    // Load directly from Firebase - try both collections simultaneously
    this.queryDoctorPatientCollection(currentPatientId, 'doctorPatient');
    this.queryDoctorPatientCollection(currentPatientId, 'doctorPatients');

    // Also check for doctors that have this patient assigned
    this.findDoctorsWithThisPatient(currentPatientId);
  }

  private queryDoctorPatientCollection(patientId: string, collectionName: string) {
    // Query Firebase for doctor-patient relationships with both field name formats
    const relationQuery1 = query(
      collection(this.appointmentService['firestore'], collectionName),
      where('patientId', '==', patientId)
    );

    const relationQuery2 = query(
      collection(this.appointmentService['firestore'], collectionName),
      where('patient_id', '==', patientId)
    );

    console.log(`Querying ${collectionName} collection for patient ID: ${patientId}`);

    // Run Firebase operations outside Angular zone to prevent warnings
    this.ngZone.runOutsideAngular(() => {
      // Combine results from both queries
      forkJoin([
        from(getDocs(relationQuery1)),
        from(getDocs(relationQuery2))
      ]).pipe(
        map(([snapshot1, snapshot2]) => {
          const allDocs = [...snapshot1.docs, ...snapshot2.docs];
          if (allDocs.length === 0) {
            console.log(`No doctor-patient relationships found in ${collectionName} collection`);
            return [];
          }

          console.log(`Found doctor-patient relationships in ${collectionName}:`, allDocs.length);

          // Extract all doctor IDs using both field name formats
          const doctorIds = new Set<string>();
          allDocs.forEach(doc => {
            const data = doc.data();
            const doctorId = data['doctorId'] || data['doctor_id'];
            if (doctorId) {
              doctorIds.add(doctorId);
              console.log(`Adding doctor ID from relationship: ${doctorId}`);
            }
          });

          return Array.from(doctorIds);
        }),
        switchMap(doctorIds => {
          if (doctorIds.length === 0) {
            return of([]);
          }

          // For each doctor ID, get the doctor details from Firebase
          const doctorPromises = doctorIds.map((doctorId: string) => {
            const doctorRef = doc(this.appointmentService['firestore'], `users/${doctorId}`);
            return from(getDoc(doctorRef)).pipe(
              map((docSnap: DocumentSnapshot<DocumentData>) => {
                if (!docSnap.exists()) {
                  console.log(`Doctor document for ID ${doctorId} not found`);
                  return null;
                }

                const data = docSnap.data();
                if (!data) return null;

                // Create doctor object from Firebase data
                return {
                  id: doctorId,
                  firstname: data['firstName'] || data['firstname'] || '',
                  lastname: data['lastName'] || data['lastname'] || '',
                  email: data['email'] || '',
                  role: 'DOCTOR',
                  password: ''
                } as UserType;
              }),
              catchError(error => {
                console.error(`Error fetching doctor details:`, error);
                return of(null);
              })
            );
          });

          return forkJoin(doctorPromises);
        }),
        map(doctors => doctors.filter(d => d !== null) as UserType[])
      ).subscribe({
        next: (firebaseDoctors) => {
          this.ngZone.run(() => {
            if (firebaseDoctors.length > 0) {
              // Update the doctors list with Firebase data
              const currentDoctors = this.myDoctors();
              const uniqueDoctors = [...currentDoctors];

              // Add new doctors from Firebase
              firebaseDoctors.forEach(doctor => {
                if (!uniqueDoctors.some(d => d.id === doctor.id)) {
                  uniqueDoctors.push(doctor);
                }
              });

              // Update the doctors list
              this.myDoctors.set(uniqueDoctors);

              // Update local storage for offline access
              this.updateLocalStorage(uniqueDoctors, patientId);
            }
          });
        },
        error: (err) => {
          this.ngZone.run(() => {
            console.error(`Error loading doctors:`, err);
          });
        }
      });
    });
  }

  private updateLocalStorage(doctors: UserType[], patientId: string) {
    // Update user table
    const existingUsers = this.db.userTable();
    const updatedUsers = [...existingUsers];

    doctors.forEach(doctor => {
      const index = updatedUsers.findIndex(u => u.id === doctor.id);
      if (index === -1) {
        updatedUsers.push(doctor);
      } else {
        updatedUsers[index] = doctor;
      }
    });

    this.db.userTable.set(updatedUsers);
    this.db.storage.setItem('USER_TABLE', updatedUsers);

    // Update doctor-patient relationships
    const relationships = doctors.map(doctor => ({
      id: crypto.randomUUID ? crypto.randomUUID() : `${Date.now()}`,
      doctor_id: doctor.id,
      patient_id: patientId
    }));

    this.db.doctorPatientTable.set(relationships);
    this.db.storage.setItem('DOCTOR_PATIENT_TABLE', relationships);
  }

  private findDoctorsWithThisPatient(patientId: string) {
    console.log(`Looking for doctors that have patient ${patientId} in their assignedPatients array`);

    // Run Firebase operations outside Angular zone to prevent warnings
    this.ngZone.runOutsideAngular(() => {
      // Query users collection for doctors with this patient in their assignedPatients array
      const usersQuery = query(
        collection(this.appointmentService['firestore'], 'users'),
        where('role', '==', 'DOCTOR'),
        where('assignedPatients', 'array-contains', patientId)
      );

      from(getDocs(usersQuery))
        .pipe(
          map(querySnapshot => {
            if (querySnapshot.empty) {
              console.log('No doctors found with this patient in their assignedPatients array');
              return [];
            }

            console.log(`Found ${querySnapshot.size} doctors with this patient in assignedPatients`);

            const doctors: UserType[] = [];
            querySnapshot.forEach(doc => {
              const data = doc.data();
              console.log(`Doctor found with patient in assignedPatients:`, doc.id, data);

              const doctor: UserType = {
                id: doc.id,
                firstname: data['firstName'] || data['firstname'] || '',
                lastname: data['lastName'] || data['lastname'] || '',
                email: data['email'] || '',
                role: 'DOCTOR',
                password: '' // We don't have or need the password
              };
              doctors.push(doctor);
            });

            return doctors;
          }),
          catchError(error => {
            console.error('Error finding doctors with this patient:', error);
            return of([]);
          })
        )
        .subscribe({
          next: (doctors) => {
            // Return to Angular zone for UI updates
            this.ngZone.run(() => {
              if (doctors.length === 0) {
                console.log('No additional doctors found via assignedPatients array');
                return;
              }

              console.log('Found additional doctors via assignedPatients array:', doctors);

              // Merge with existing doctors
              const currentDoctors = this.myDoctors();
              const allDoctorIds = new Set(currentDoctors.map(d => d.id));

              // Add new doctors
              const newDoctors = [...currentDoctors];

              for (const doctor of doctors) {
                if (doctor.id && !allDoctorIds.has(doctor.id)) {
                  newDoctors.push(doctor);
                  allDoctorIds.add(doctor.id);

                  // Also add to local database if not there
                  if (!this.db.userTable().some(u => u.id === doctor.id)) {
                    this.db.userTable.update(users => [...users, doctor]);
                    this.db.storage.setItem('USER_TABLE', this.db.userTable());
                  }

                  // Create doctor-patient relationship in local storage
                  const existingRelation = this.db.doctorPatientTable().find(
                    dp => dp.doctor_id === doctor.id && dp.patient_id === patientId
                  );

                  if (!existingRelation) {
                    const newRelation: DoctorPatient = {
                      id: crypto.randomUUID ? crypto.randomUUID() : `${Date.now()}`,
                      doctor_id: doctor.id,
                      patient_id: patientId
                    };

                    this.db.addDoctorPatient(newRelation);
                  }
                }
              }

              this.myDoctors.set(newDoctors);
            });
          },
          error: (err) => {
            this.ngZone.run(() => {
              console.error('Error processing doctors with this patient:', err);
            });
          }
        });
    });
  }

  bookAppointment(val: AvailabilityType) {
    // Store the slot to book
    this.slotToBook = val;

    // Show the modal to collect the reason for visit
    this.showModal = true;

    // Reset the medical concern input
    this.medicalConcern = '';
  }

  confirmBooking() {
    if (!this.slotToBook) {
      console.error('No appointment slot selected');
      return;
    }

    const val = this.slotToBook;
    this.showModal = false;
    this.isLoading = true;

    // Get the current patient
    const currentPatient = this.db.current_patient();
    if (!currentPatient || !currentPatient.id) {
      console.error('No current patient ID found');
      this.bookingError = 'Patient information not found. Please log in again.';
      setTimeout(() => {
        this.bookingError = '';
      }, 3000);
      this.isLoading = false;
      return;
    }

    // Set the reason from the modal input or use default
    const reasonForVisit = this.medicalConcern.trim() || 'General Consultation';

    const appointment: AppointmentType = {
      id: this.db.generateId(),
      date: val.date,
      time: val.start_time,
      doctor_id: val.doctor_id,
      patient_id: currentPatient.id,
      status: 'Pending',
      patientName: `${currentPatient.firstname} ${currentPatient.lastname}`,
      reasonForVisit: reasonForVisit
    };

    // Check for existing appointment
    let existing: AppointmentType | undefined = this.db
      .appointmentTable()
      .find(
        (app) =>
          app.doctor_id == appointment.doctor_id &&
          appointment.patient_id &&
          app.date == appointment.date &&
          app.time == appointment.time
      );

    if (existing) {
      this.bookingError = 'You already have an appointment at this time.';
      setTimeout(() => {
        this.bookingError = '';
      }, 3000);
      this.isLoading = false;
      return;
    }

    // Add appointment to Firebase and also in local storage for backwards compatibility
    const newAppointment: Omit<AppointmentType, 'id'> = {
      date: val.date,
      time: val.start_time,
      doctor_id: val.doctor_id,
      patient_id: this.db.current_patient()?.id || '',
      status: 'Pending',
      patientName: `${this.db.current_patient()?.firstname} ${this.db.current_patient()?.lastname}`,
      reasonForVisit: reasonForVisit
    };

    // Call Firebase service to add the appointment
    this.firebaseService.addAppointment(newAppointment)
      .pipe(
        finalize(() => this.isLoading = false)
      )
      .subscribe({
        next: (id) => {
          console.log('Successfully booked appointment with ID:', id);

          // Also add to local storage for backwards compatibility
          const localAppointment: AppointmentType = {
            ...newAppointment,
            id: id
          };
          this.db.addAppointment(localAppointment);

          // After successful booking
          this.bookingSuccess = true;

          // Remove the availability slot from Firebase that has been converted to an appointment
          this.firebaseService.removeAvailability(val.id).subscribe({
            next: () => {
              console.log('Availability slot removed after booking');
              // Update the UI
              this.onViewDoctorAvailability(val.doctor_id);
            },
            error: (error) => {
              console.error('Error removing availability slot:', error);
            }
          });

          // Reset success message after delay
          setTimeout(() => {
            this.bookingSuccess = false;
          }, 3000);
        },
        error: (error) => {
          console.error('Error booking appointment in Firebase:', error);

          // Fallback to local storage only
          this.db.addAppointment(appointment);

          this.bookingSuccess = true;
          // Update availability display using local storage
          this.onViewDoctorAvailability(val.doctor_id);

          // Reset success message after delay
          setTimeout(() => {
            this.bookingSuccess = false;
          }, 3000);
        }
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
