import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>, NgI<PERSON>, Ng<PERSON><PERSON> } from '@angular/common';
import { Location } from '@angular/common';
import { NavbarComponent } from '../../shared/navbar/navbar.component';
import { BackButtonComponent } from '../../shared/back-button/back-button.component';
import { RouterLink } from '@angular/router';
import { AppointmentPatient, AppointmentService } from '../service/appointment.service';

type TabType = 'upcoming' | 'completed' | 'cancelled';

@Component({
  selector: 'app-appointment-schedule',
  standalone: true,
  imports: [NgFor, NgIf, NgClass, NavbarComponent, BackButtonComponent, RouterLink],
  templateUrl: './appointment-schedule.component.html',
  styleUrls: ['./appointment-schedule.component.css']
})
export class AppointmentScheduleComponent implements OnInit {
  activeTab: TabType = 'upcoming';
  private tabHistory: TabType[] = ['upcoming'];
  appointments: AppointmentPatient[] = [];
  upcomingAppointments: AppointmentPatient[] = [];
  completedAppointments: AppointmentPatient[] = [];
  cancelledAppointments: AppointmentPatient[] = [];
  isLoading = false;
  error: string | null = null;

  constructor(
    private location: Location,
    private appointmentService: AppointmentService
  ) {}

  ngOnInit() {
    this.loadAppointments();
  }

  loadAppointments() {
    this.isLoading = true;
    this.error = null;
    
    // Get all appointments from db first for immediate display
    const localAppointments = this.appointmentService.getAllAppointments();
    console.log('Local appointments loaded:', localAppointments);

    if (localAppointments.length > 0) {
      this.appointments = localAppointments;
      this.categorizeAppointments();
    } else {
      console.warn('No local appointments found');
      // Handle no appointments case
      this.appointments = [];
      this.upcomingAppointments = [];
      this.completedAppointments = [];
      this.cancelledAppointments = [];
    }
    
    this.isLoading = false;
    
    // Force a refresh to ensure we have the latest data
    this.appointmentService.refreshAppointments();
  }

  categorizeAppointments() {
    // Debug log to see what we're working with
    console.log('Categorizing appointments, total count:', this.appointments.length);
    console.log('Appointment statuses:', this.appointments.map(a => a.status));
    
    // Only show Approved in upcoming tab
    this.upcomingAppointments = this.appointments.filter(app => 
      app.status === 'Approved'
    ).sort((a, b) => new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime());
    
    // Only show Completed in completed tab
    this.completedAppointments = this.appointments.filter(app => 
      app.status === 'Completed'
    ).sort((a, b) => new Date(b.appointmentDate).getTime() - new Date(a.appointmentDate).getTime());
    
    // Only show Cancelled and Rejected in cancelled tab (not Pending)
    this.cancelledAppointments = this.appointments.filter(app => 
      ['Cancelled', 'Rejected'].includes(app.status)
    ).sort((a, b) => new Date(b.appointmentDate).getTime() - new Date(a.appointmentDate).getTime());
    
    console.log('Upcoming appointments (Approved only):', this.upcomingAppointments.length);
    console.log('Completed appointments:', this.completedAppointments.length);
    console.log('Cancelled appointments:', this.cancelledAppointments.length);
  }

  goBack(): void {
    if (this.tabHistory.length > 1) {
      this.tabHistory.pop();
      this.activeTab = this.tabHistory[this.tabHistory.length - 1];
    }
  }

  setActiveTab(tab: TabType): void {
    if (this.tabHistory[this.tabHistory.length - 1] !== tab) {
      this.tabHistory.push(tab);
    }
    this.activeTab = tab;
  }

  get filteredAppointments(): AppointmentPatient[] {
    switch (this.activeTab) {
      case 'upcoming':
        return this.upcomingAppointments;
      case 'completed':
        return this.completedAppointments;
      case 'cancelled':
        return this.cancelledAppointments;
      default:
        return [];
    }
  }

  get hasUpcomingAppointments(): boolean {
    return this.upcomingAppointments.length > 0;
  }

  get hasApprovedAppointments(): boolean {
    return this.upcomingAppointments.length > 0; // Same as hasUpcomingAppointments since we only show Approved now
  }

  get hasCompletedAppointments(): boolean {
    return this.completedAppointments.length > 0;
  }

  get hasCancelledAppointments(): boolean {
    return this.cancelledAppointments.length > 0;
  }

  formatDate(date: Date): string {
    const appointmentDate = new Date(date);
    
    // Format day of week (e.g., "Fri")
    const dayOfWeek = appointmentDate.toLocaleDateString('en-US', { weekday: 'short' });
    
    // Format month (e.g., "Mar")
    const month = appointmentDate.toLocaleDateString('en-US', { month: 'short' });
    
    // Get day number (e.g., "14")
    const day = appointmentDate.getDate();
    
    // Return formatted date: "Fri, Mar 14"
    return `${dayOfWeek}, ${month} ${day}`;
  }

  formatTime(time: string): string {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${period}`;
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Pending': return '#eab308';
      case 'Approved': return '#22c55e';
      case 'Rejected': return '#ef4444';
      case 'Completed': return '#199a8e';
      case 'Cancelled': return '#6b7280';
      default: return '#6b7280';
    }
  }

  cancelAppointment(appointment: AppointmentPatient) {
    if (!appointment) return;
    this.isLoading = true;
    this.appointmentService.cancelAppointment(
      appointment.appointmentId,
      'Cancelled by patient'
    ).subscribe({
      next: () => {
        this.loadAppointments();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error cancelling appointment:', error);
        this.error = 'Failed to cancel appointment';
        this.isLoading = false;
      }
    });
  }
}

