:host {
  display: block;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.container{
  width: 100%;
  height: 100%;
  margin: 0 auto;
  padding: 0;
  font-family:Arial, Helvetica, sans-serif;
  color: #333;
}

.about-section {
  width: 100%;
  max-width: 100%;
  padding: 2rem 1rem;
  box-sizing: border-box;
  overflow-x: hidden;
}

.about-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  box-sizing: border-box;
}

  .mission-section {
    width: 100%;
    text-align: center;
    padding: 4rem 2rem;
    margin: 0 auto;
  }

  .mission-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #199A8E;
    position: relative;
    display: inline-block;
  }

  .mission-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #199A8E;
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  .mission-title:hover::after {
    transform: scaleX(1);
  }

  .mission-text {
    font-size: 1rem;
    line-height: 1.6;
  }

  .features-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #199A8E;
    position: relative;
    display: inline-block;
  }

  .features-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #199A8E;
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  .features-title:hover::after {
    transform: scaleX(1);
  }

  .feature-title {
    font-size: 1.5rem;
    margin: 1.2rem 0;
    color: #2b2b2b;
  }

  .features-section {
    text-align: center;
    padding: 4rem 0;
    width: 100%;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 2rem 0;
    width: 100%;
  }

  .feature-card {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    border: 1px solid rgba(25, 154, 142, 0.1);
  }

  .feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(25, 154, 142, 0.2);
    background: rgba(255, 255, 255, 0.8);
  }

  .feature-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #4a4a4a;
    margin: 0.8rem 0;
  }

  .cta-section {
    text-align: center;
    padding: 4rem 2rem;
    margin: 2rem auto;
    max-width: 1200px;
  }

  .cta-text {
    font-size: 1rem;
    color: #333;
    margin-bottom: 2rem;

  }

  .cta-button-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .btn-lg {
    font-size: 1rem;
  }

  .cta-button {
    background-color: #199A8E;
    color: #fff;
    font-size: 1rem;
    padding: 0.8rem 2rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .cta-button:hover {
    background-color: teal;
  }

  .btn-outline-dark {
    border:#199A8E;
    color: white;
    background-color:#199A8E ;
    padding: 5px 20px;
    font-size: 15px;
    font-weight: bold;
    transition: all 0.3s ease-in-out;
  }

  .btn-outline-dark:hover {
    background-color: #343a40;
    color: white;
  }

  .btn-lg {
    font-size: 18px;
    padding: 7px 20px;
  }

  .rounded-pill {
    border-radius: 40px;
  }


  /* Footer */
  .footer {
    width: 100%;
    background-color: #199A8E;
    text-align: center;
    padding: 1rem 0;
    font-size: 0.9rem;

  }

  .footer-text {
    color: white;
  }

  .footer-links a {
    color: white;
    text-decoration: none;
  }

  .footer-links a:hover {
    text-decoration: underline;
  }

  .b{
    height: 200px;
    fill: #199A8E;
  }
  .clinic-op,
  .wait-time,
  .files,
  .people,
  .clip {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 1rem;
  }

  .clinic-op i,
  .wait-time i,
  .files i,
  .people i,
  .clip i,
  .clinic-op svg {
    font-size: 24px;
    color: #199A8E;
  }

/* Add responsive styles for different screen sizes */
@media screen and (max-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 2rem;
  }
  
  .mission-section {
    padding: 3rem 1.5rem;
  }
  
  .mission-title,
  .features-title {
    font-size: 2.2rem;
  }
}

@media screen and (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1.5rem;
  }
  
  .mission-section {
    padding: 2.5rem 1.25rem;
  }
  
  .mission-title,
  .features-title {
    font-size: 2rem;
  }
  
  .feature-card {
    padding: 2rem;
  }
  
  .feature-title {
    font-size: 1.3rem;
  }
  
  .feature-description {
    font-size: 1rem;
  }
}

@media screen and (max-width: 480px) {
  .mission-section {
    padding: 2rem 1rem;
  }
  
  .mission-title,
  .features-title {
    font-size: 1.8rem;
  }
  
  .mission-text {
    font-size: 0.95rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .features-grid {
    padding: 0 1rem;
    gap: 1.25rem;
  }
  
  .feature-title {
    font-size: 1.2rem;
    margin: 1rem 0;
  }
  
  .feature-description {
    font-size: 0.95rem;
  }
}

@media screen and (max-width: 320px) {
  .mission-title,
  .features-title {
    font-size: 1.6rem;
  }
  
  .feature-card {
    padding: 1.25rem;
  }
  
  .features-grid {
    gap: 1rem;
  }
  
  .feature-title {
    font-size: 1.1rem;
  }
  
  .feature-description {
    font-size: 0.9rem;
  }
}
