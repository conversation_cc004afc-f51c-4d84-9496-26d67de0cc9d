<div class="payment-container">
  <div class="payment-card">
    <div class="header">
      <h2>Complete Your Payment</h2>
      <div class="progress-dots">
        <div class="dot active"></div>
        <div class="dot"></div>
      </div>
    </div>

    <form class="payment-form" [formGroup]="practiceForm" (ngSubmit)="onSubmit()">
      <div class="form-row">
        <div class="form-group">
          <label for="practiceName">Practice Name</label>
          <div class="input-with-icon">
            <i class="practice-icon"></i>
            <input
              type="text"
              id="practiceName"
              formControlName="practiceName"
              placeholder="Medical Practice Name">
          </div>
        </div>

        <div class="form-group">
          <label for="doctorName">Doctor's Name</label>
          <div class="input-with-icon">
            <i class="doctor-icon"></i>
            <input
              type="text"
              id="doctorName"
              formControlName="doctorName"
              placeholder="<PERSON><PERSON> <PERSON>">
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="email">Email</label>
          <div class="input-with-icon">
            <i class="email-icon"></i>
            <input
              type="email"
              id="email"
              formControlName="email"
              placeholder="<EMAIL>">
          </div>
        </div>

        <div class="form-group">
          <label for="phone">Phone Number</label>
          <div class="input-with-icon">
            <i class="phone-icon"></i>
            <input
              type="tel"
              id="phone"
              formControlName="phone"
              placeholder="+27 123 456 789">
          </div>
        </div>
      </div>

      <div class="form-group full-width">
        <label for="address">Practice Address</label>
        <div class="input-with-icon">
          <i class="address-icon"></i>
          <textarea
            id="address"
            formControlName="address"
            placeholder="Enter your practice address"
            rows="3">
          </textarea>
        </div>
      </div>

      <button type="submit" class="submit-button" [disabled]="!practiceForm.valid">
        Continue to Payment
      </button>
    </form>
  </div>
</div>
