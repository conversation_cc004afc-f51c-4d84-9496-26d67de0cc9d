<div class="form-container">
  <div class="header">
    <i class="bi bi-clipboard2-pulse-fill header-icon"></i>
    <h1 class="header-title">Patient Medical Record</h1>
  </div>

  <form [formGroup]="medicalRecordForm" class="form-content" (ngSubmit)="onSubmit()">
    <!-- Patient Information -->
    <div class="section">
      <h2 class="section-title">Patient Information</h2>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label" for="fullName">Full Name</label>
          <input type="text" id="fullName" class="form-control" formControlName="fullName" placeholder="Enter patient's full name">
        </div>
        <div class="form-group">
          <label class="form-label" for="dateOfBirth">Date of Birth</label>
          <input type="date" id="dateOfBirth" class="form-control date" formControlName="dateOfBirth">
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label" for="gender">Gender</label>
          <select id="gender" class="select-control" formControlName="gender">
            <option value="" disabled selected>Select Gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="phone">Phone</label>
          <input type="tel" id="phone" class="form-control" formControlName="phone" placeholder="Enter phone number">
        </div>
      </div>
      <div class="form-group">
        <label class="form-label" for="email">Email</label>
        <input type="email" id="email" class="form-control" formControlName="email" placeholder="Enter email address">
      </div>
      <div class="form-group">
        <label class="form-label" for="address">Address</label>
        <textarea id="address" class="form-control textarea" formControlName="address" placeholder="Enter full address"></textarea>
      </div>
    </div>

    <!-- Emergency Contact -->
    <div class="section">
      <h2 class="section-title">Emergency Contact</h2>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label" for="emergencyName">Name</label>
          <input type="text" id="emergencyName" class="form-control" formControlName="emergencyName" placeholder="Enter emergency contact name">
        </div>
        <div class="form-group">
          <label class="form-label" for="emergencyRelationship">Relationship</label>
          <input type="text" id="emergencyRelationship" class="form-control" formControlName="emergencyRelationship" placeholder="Enter relationship to patient">
        </div>
      </div>
      <div class="form-group">
        <label class="form-label" for="emergencyPhone">Phone</label>
        <input type="tel" id="emergencyPhone" class="form-control" formControlName="emergencyPhone" placeholder="Enter emergency contact phone">
      </div>
    </div>

    <!-- Social History -->
    <div class="section">
      <h2 class="section-title">Social History</h2>
      <div class="form-row triple">
        <div class="form-group">
          <label class="form-label" for="smokingStatus">Smoking Status</label>
          <select id="smokingStatus" class="select-control" formControlName="smokingStatus">
            <option value="Never">Never</option>
            <option value="Former">Former</option>
            <option value="Current">Current</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="alcoholConsumption">Alcohol Consumption</label>
          <select id="alcoholConsumption" class="select-control" formControlName="alcoholConsumption">
            <option value="None">None</option>
            <option value="Occasional">Occasional</option>
            <option value="Moderate">Moderate</option>
            <option value="Heavy">Heavy</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="exerciseFrequency">Exercise Frequency</label>
          <select id="exerciseFrequency" class="select-control" formControlName="exerciseFrequency">
            <option value="None">None</option>
            <option value="Occasional">Occasional</option>
            <option value="Regular">Regular</option>
            <option value="Frequent">Frequent</option>
          </select>
        </div>
      </div>
      <div class="form-group">
        <label class="form-label" for="occupation">Occupation</label>
        <input type="text" id="occupation" class="form-control" formControlName="occupation" placeholder="Enter occupation">
      </div>
    </div>

    <!-- Assessment and Plan -->
    <div class="section">
      <h2 class="section-title">Assessment and Plan</h2>
      <div class="form-group">
        <label class="form-label" for="diagnoses">Diagnoses</label>
        <textarea id="diagnoses" class="form-control textarea" formControlName="diagnoses" placeholder="Enter diagnoses"></textarea>
      </div>
      <div class="form-group">
        <label class="form-label" for="treatmentPlans">Treatment Plans</label>
        <textarea id="treatmentPlans" class="form-control textarea" formControlName="treatmentPlans" placeholder="Medications, Therapies"></textarea>
      </div>
      <div class="form-group">
        <label class="form-label" for="followUpDate">Follow-Up Appointments</label>
        <input type="date" id="followUpDate" class="form-control date" formControlName="followUpDate">
      </div>
    </div>

    <!-- Consent and Signatures -->
    <div class="section">
      <h2 class="section-title">Consent and Signatures</h2>
      <div class="checkbox-group">
        <input type="checkbox" id="consent" class="checkbox-input" formControlName="consentChecked">
        <label class="checkbox-label" for="consent">
          I consent to treatment and acknowledge that I have received and understand the privacy practices.
        </label>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label class="form-label" for="patientSignature">Patient Signature</label>
          <input type="text" id="patientSignature" class="form-control" formControlName="patientSignature" placeholder="Enter patient signature">
        </div>
        <div class="form-group">
          <label class="form-label" for="physicianSignature">Physician Signature</label>
          <input type="text" id="physicianSignature" class="form-control" formControlName="physicianSignature" placeholder="Enter physician signature">
        </div>
      </div>
      <div class="form-group">
        <label class="form-label" for="signatureDate">Date</label>
        <input type="date" id="signatureDate" class="form-control date" formControlName="signatureDate">
      </div>
    </div>

    <div class="button-group">
      <button type="button" class="btn btn-cancel" (click)="cancel()">Cancel</button>
      <button type="button" class="btn btn-draft" (click)="saveDraft()">Save as Draft</button>
      <button type="submit" class="btn btn-submit">Submit Record</button>
    </div>
  </form>
</div>
