import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { NavbarComponent } from "../../shared/navbar/navbar.component";
import { BackButtonComponent } from '../../shared/back-button/back-button.component';
import { RouterLink } from '@angular/router';
import { PatientProfileService, PatientProfile } from '../services/patient-profile.service';
import { HttpClientModule } from '@angular/common/http';
import { AuthService } from '../../services/auth.service';
import { getFirestore, doc, setDoc, serverTimestamp } from '@angular/fire/firestore';
import { Subscription, interval } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-patient-update-profile',
  standalone: true,
  imports: [CommonModule, FormsModule, NavbarComponent, BackButtonComponent, RouterLink, HttpClientModule],
  templateUrl: './patient-update-profile.component.html',
  styleUrls: ['./patient-update-profile.component.css']
})
export class PatientUpdateProfileComponent implements OnInit, OnDestroy {
  profileImage: string | ArrayBuffer | null = null;
  selectedImage: string | null = null;
  showSuccessModal: boolean = false;
  isLoading: boolean = false;

  // Add subscription for periodic refresh
  private refreshSubscription: Subscription | null = null;

  profileData: PatientProfile = {
    fullName: '',
    phoneNumber: '',
    email: ''
  };

  constructor(
    private router: Router,
    private profileService: PatientProfileService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Show loading indicator
    this.isLoading = true;

    // First, try to get the profile directly from Firebase
    this.profileService.fetchProfileFromFirebase().subscribe({
      next: (firebaseProfile) => {
        if (firebaseProfile) {
          console.log('Profile loaded from Firebase:', firebaseProfile);
          this.profileData = { ...firebaseProfile };
          this.selectedImage = firebaseProfile.profilePicture || null;
          this.isLoading = false;
        } else {
          // If no Firebase profile, fall back to local storage
          this.loadFromLocalStorage();
        }
      },
      error: (error) => {
        console.error('Error loading profile from Firebase:', error);
        // Fall back to local storage on error
        this.loadFromLocalStorage();
      }
    });

    // Set up periodic refresh (every 30 seconds) to keep data fresh
    this.startPeriodicRefresh();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    this.stopPeriodicRefresh();
  }

  // Start periodic refresh of profile data from Firebase
  private startPeriodicRefresh(): void {
    // Every 30 seconds, refresh data from Firebase (if the component is still active)
    this.refreshSubscription = interval(30000).pipe(
      switchMap(() => this.profileService.fetchProfileFromFirebase())
    ).subscribe({
      next: (refreshedProfile) => {
        if (refreshedProfile && !this.isLoading) { // Only update if not currently saving/loading
          console.log('Periodic refresh from Firebase:', refreshedProfile);

          // Don't update if user has made local changes
          const currentData = JSON.stringify(this.profileData);
          const newData = JSON.stringify(refreshedProfile);

          if (currentData !== newData) {
            console.log('Profile data has changed in Firebase, updating local view');
            this.profileData = { ...refreshedProfile };
            this.selectedImage = refreshedProfile.profilePicture || null;
          }
        }
      },
      error: (error) => {
        console.error('Error during periodic refresh:', error);
      }
    });
  }

  // Stop periodic refresh
  private stopPeriodicRefresh(): void {
    if (this.refreshSubscription) {
      this.refreshSubscription.unsubscribe();
      this.refreshSubscription = null;
    }
  }

  // Helper method to load from local storage and auth service
  private loadFromLocalStorage(): void {
    // Try to get profile from local storage via the service
    const currentProfile = this.profileService.getCurrentProfile();
    if (currentProfile) {
      console.log('Profile loaded from local storage:', currentProfile);
      this.profileData = { ...currentProfile };
      this.selectedImage = currentProfile.profilePicture || null;
    } else {
      // If no profile data, try to get it from auth service
      this.createProfileFromAuth();
    }
    this.isLoading = false;
  }

  // Helper method to create a profile from auth data
  private createProfileFromAuth(): void {
    this.authService.getCurrentUser().then(user => {
      if (user) {
        const userInfo = this.authService.getUserInfo();
        // Get profile image, ensuring it's either string or null (not undefined)
        const profileImage = user.profileImage === undefined ? null : user.profileImage;

        // Create basic profile
        const profile: PatientProfile = {
          id: user.id,
          fullName: user.displayName || '',
          email: user.email || '',
          phoneNumber: userInfo?.phoneNumber || '',
          profilePicture: profileImage,
          address: userInfo?.address || '',
          role: userInfo?.role || 'PATIENT'
        };

        console.log('Created profile from auth data:', profile);
        this.profileData = profile;
        this.selectedImage = profile.profilePicture || null; // Ensure it's never undefined

        // Save this profile to Firebase for future use
        this.saveProfileToFirebase(profile);
      } else {
        console.error('No user found in auth service');
      }
    }).catch(error => {
      console.error('Error getting current user:', error);
    });
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        alert('Image size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e: any) => {
        const base64Image = e.target.result;
        this.selectedImage = base64Image;
        console.log('Image loaded as base64');

        // Show loading state
        this.isLoading = true;

        // Update profile picture immediately
        this.profileService.updateProfilePicture(base64Image).subscribe({
          next: (updatedProfile) => {
            console.log('Profile picture updated successfully:', updatedProfile);
            // Update auth service data to keep everything in sync
            this.authService.updateUserInfoFromProfile(updatedProfile);

            // Update local profile data
            this.profileData = updatedProfile;
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Failed to update profile picture', error);
            this.isLoading = false;

            // If API call failed, update local data and save to Firebase
            if (!this.profileData.id) {
              // Create a local profile if one doesn't exist yet
              this.createLocalProfile(base64Image);
            } else {
              // Just update the local profile with the new image
              this.profileData.profilePicture = base64Image;

              // Save directly to Firebase as fallback
              this.saveProfileToFirebase({
                ...this.profileData,
                profilePicture: base64Image
              });
            }

            // Don't show alert, just continue with local changes
            console.log('Continuing with local profile picture changes');
          }
        });
      };
      reader.onerror = () => {
        alert('Error reading file');
      };
      reader.readAsDataURL(file);
    }
  }

  triggerFileInput(): void {
    const fileInput = document.getElementById('file-upload') as HTMLInputElement;
    if (fileInput) {
      fileInput.click();
    }
  }

  updateProfile(): void {
    this.isLoading = true;

    // Ensure firstName and lastName are set before updating
    if (!this.profileData.firstName || !this.profileData.lastName) {
      const nameParts = this.profileData.fullName.split(' ');
      this.profileData.firstName = nameParts[0] || '';
      this.profileData.lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
    }

    // Ensure role is set
    if (!this.profileData.role) {
      this.profileData.role = 'PATIENT';
    }

    // Update profile using ProfileService
    this.profileService.updateProfile(this.profileData).subscribe({
      next: (updatedProfile) => {
        console.log('Profile updated successfully:', updatedProfile);

        // Also update auth service data to keep everything in sync
        this.authService.updateUserInfoFromProfile({
          ...updatedProfile,
          full_name: updatedProfile.fullName,
          firstName: updatedProfile.firstName,
          lastName: updatedProfile.lastName,
          address: updatedProfile.address,
          role: updatedProfile.role
        });

        this.isLoading = false;
        this.showSuccessModal = true;
      },
      error: (error) => {
        console.error('Error updating profile via service:', error);

        // If the profile service update failed, try saving directly to Firebase
        this.saveProfileToFirebase(this.profileData);

        // Update UI state
        this.isLoading = false;

        // Show a different message since we attempted a fallback save
        alert('Profile saved locally. Some features may be limited until you reconnect.');
      }
    });
  }

  closeSuccessModal() {
    this.showSuccessModal = false;
    this.router.navigate(['/mobile/patient-dashboard']);
  }

  // Save profile directly to Firebase
  private saveProfileToFirebase(profile: PatientProfile): void {
    // Get Firestore instance
    try {
      const db = getFirestore();

      if (!db) {
        console.error('Firestore not available');
        return;
      }

      this.authService.getCurrentUser().then(user => {
        if (!user || !user.id) {
          console.error('User ID not available for Firebase save');
          return;
        }

        // Get additional user info
        const userInfo = this.authService.getUserInfo();

        console.log('Attempting to save profile to Firebase for user:', user.id);

        // Create patient document reference
        const patientDocRef = doc(db, 'patients', user.id);

        // Split the full name into first and last name
        const nameParts = profile.fullName.split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

        // Current timestamp for createdAt if it's a new profile
        const now = new Date().toISOString();

        // Prepare complete data for Firestore
        const patientData = {
          fullName: profile.fullName,
          firstName: firstName,
          lastName: lastName,
          email: profile.email,
          phoneNumber: profile.phoneNumber || '',
          profilePicture: profile.profilePicture || null,
          address: profile.address || userInfo?.address || '',
          role: profile.role || userInfo?.role || 'PATIENT',
          updatedAt: serverTimestamp(),
          // Only set createdAt if this appears to be a new record
          createdAt: profile.id ? undefined : now
        };

        console.log('Saving complete profile data to Firebase:', patientData);

        // Upload to Firestore
        setDoc(patientDocRef, patientData, { merge: true })
          .then(() => {
            console.log('Successfully saved profile to Firebase');

            // Update local profile with ID and additional fields
            this.profileData = {
              ...this.profileData,
              id: user.id,
              firstName,
              lastName,
              address: patientData.address,
              role: patientData.role
            };

            // Also update auth service data to keep everything in sync
            this.authService.updateUserInfoFromProfile({
              ...this.profileData,
              // Add the extra fields needed for proper auth sync
              full_name: profile.fullName,
              firstName: firstName,
              lastName: lastName,
              address: patientData.address,
              role: patientData.role
            });
          })
          .catch(error => {
            console.error('Error saving profile to Firebase:', error);
          });
      }).catch(error => {
        console.error('Error getting current user for Firebase save:', error);
      });
    } catch (error) {
      console.error('Error initializing Firestore:', error);
    }
  }

  // Create a local profile from auth data and try to save to Firebase
  private createLocalProfile(profilePicture: string): void {
    this.authService.getCurrentUser().then(user => {
      if (user) {
        const userInfo = this.authService.getUserInfo();

        // Split the name into first and last name
        const fullName = user.displayName || (userInfo?.full_name || '');
        const nameParts = fullName.split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

        // Create basic profile data
        this.profileData = {
          id: user.id,
          fullName: fullName,
          firstName: firstName,
          lastName: lastName,
          email: user.email || (userInfo?.email || ''),
          phoneNumber: userInfo?.phoneNumber || '',
          profilePicture: profilePicture,
          address: userInfo?.address || '',
          role: userInfo?.role || 'PATIENT',
          createdAt: new Date().toISOString()
        };

        console.log('Created local profile:', this.profileData);

        // Try to save to Firebase directly
        this.saveProfileToFirebase(this.profileData);
      } else {
        console.error('No authenticated user to create local profile');
      }
    }).catch(err => {
      console.error('Error getting current user:', err);
    });
  }
}
