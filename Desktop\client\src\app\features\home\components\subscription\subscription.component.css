@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css");

.subscription-container {
  width: 100%;
  background: #F9FAFB;
  min-height: 100vh;
}

.hero-section {
  width: 100%;
  height: 340px;
  background: #199A8E;
  position: relative;
}

.hero-content {
  width: 100%;
  max-width: 1180px;
  padding: 80px 20px;
  margin: 0 auto;
  text-align: center;
  color: white;
}

.hero-content h1 {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.benefits-button {
  padding: 14px 24px;
  background: white;
  color: #199A8E;
  border: none;
  border-radius: 8px;
  font-weight: 700;
  font-size: 1.25rem;
  cursor: pointer;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  transition: all 0.2s ease-in-out;
}

.benefits-button:hover {
  transform: translateY(-2px);
  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.2);
}

.plans-section {
  padding: 64px 20px;
  max-width: 1280px;
  margin: 0 auto;
}

.plans-section h2 {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 3rem;
}

.plans-container {
  display: flex;
  gap: 32px;
  justify-content: center;
  flex-wrap: wrap;
}

.plan-card {
  width: 100%;
  max-width: 380px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0px 4px 6px -4px rgba(0, 0, 0, 0.10);
  transition: transform 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  min-height: 450px;
}

.plan-card:hover {
  transform: translateY(-4px);
}

.plan-card.premium {
  border: 2px solid #199A8E;
}

.plan-card h3 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.plan-description {
  color: #4B5563;
  margin-bottom: 1.5rem;
}

.price-container {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #E5E7EB;
}

.price {
  font-size: 2rem;
  font-weight: 700;
}

.price-period {
  color: #4B5563;
  margin-left: 0.5rem;
}

.features-list {
  margin: 1.5rem 0 2rem 0;
}

.feature {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.feature-icon {
  color: #199A8E;
  font-size: 1.25rem;
  margin-right: 0.75rem;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-count-controls {
  margin-top: 1.5rem;
}

.user-count-wrapper {
  display: inline-flex;
  min-width: 30px;
  text-align: center;
  cursor: pointer;
}

.user-count {
  font-weight: 700;
  font-size: 1.25rem;
  color: black;
}

.user-count-input {
  min-width: 30px;
  width: auto;
  padding: 4px 8px;
  border: 1px solid #199A8E;
  border-radius: 4px;
  font-size: 1.25rem;
  font-weight: 700;
  color: #199A8E;
  text-align: center;
}

.user-count-input:focus {
  outline: none;
  border-color: #158276;
  box-shadow: 0 0 0 2px rgba(25, 154, 142, 0.1);
}

.counter-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin: 0.5rem 0;
}

.counter-button {
  width: 24px;
  height: 24px;
  border: 1px solid #199A8E;
  border-radius: 2px;
  background: white;
  color: #199A8E;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.counter-button:hover:not(:disabled) {
  background: rgba(25, 154, 142, 0.1);
}

.counter-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.counter-button.hidden {
  visibility: hidden;
}

.pricing-info, .license-info {
  color: #4B5563;
  font-size: 0.75rem;
  text-align: center;
  margin-top: 0.5rem;
}

.license-info {
  font-size: 0.85rem;
  color: #199A8E;
  background: rgba(25, 154, 142, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  margin: 1rem 0;
}

.trial-button, .subscribe-button {
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  font-weight: 700;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: auto;
}

.trial-button {
  background: white;
  color: #199A8E;
  border: 1px solid #199A8E;
}

.trial-button:hover {
  background: rgba(25, 154, 142, 0.1);
  transform: translateY(-2px);
}

.subscribe-button {
  background: #199A8E;
  color: white;
  border: none;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.subscribe-button:hover {
  background: #158276;
  transform: translateY(-2px);
  box-shadow: 0px 6px 8px rgba(0, 0, 0, 0.2);
}

/* Benefits Modal Styles */
.benefits-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.benefits-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.benefits-header {
  background: #199A8E;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.benefits-header h3 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-modal {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-modal:hover {
  transform: rotate(90deg);
}

.benefits-content {
  padding: 2rem;
}

.benefit-card {
  display: flex;
  align-items: flex-start;
  padding: 1.5rem;
  background: #F9FAFB;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: transform 0.2s ease;
}

.benefit-card:last-child {
  margin-bottom: 0;
}

.benefit-card:hover {
  transform: translateY(-2px);
  border-color: #199A8E;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.benefit-card i {
  font-size: 1.75rem;
  color: #199A8E;
  margin-right: 1.25rem;
}

.benefit-text h4 {
  color: #111827;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.benefit-text p {
  color: #4B5563;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .plans-container {
    flex-direction: column;
    align-items: center;
  }

  .plan-card {
    max-width: 100%;
  }

  .benefits-button {
    font-size: 1.1rem;
    padding: 12px 20px;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 1.8rem;
  }

  .plans-section {
    padding: 40px 16px;
  }

  .counter-container {
    gap: 1rem;
  }

  .user-count-input {
    font-size: 1.1rem;
  }

  .benefits-modal {
    width: 95%;
    margin: 1rem;
  }

  .benefits-content {
    padding: 1.5rem;
  }

  .benefit-card {
    padding: 1.25rem;
  }

  .benefit-card i {
    font-size: 1.5rem;
    margin-right: 1rem;
  }

  .benefit-text h4 {
    font-size: 1rem;
  }

  .benefit-text p {
    font-size: 0.9rem;
  }
}
