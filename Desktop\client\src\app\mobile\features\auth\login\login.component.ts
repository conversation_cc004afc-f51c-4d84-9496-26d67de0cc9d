import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, ReactiveFormsModule, FormGroup, Validators } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { HttpClientModule } from '@angular/common/http';
import { Db } from '../../../../db';
import { Storage } from '../../../../storage';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';
import { ErrorModalComponent } from '../../../../shared/components/error-modal/error-modal.component';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    HttpClientModule,
    LoadingSpinnerComponent,
    ErrorModalComponent
  ],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent2 {
  loginForm: FormGroup;
  errorMessage: string = '';
  loading: boolean = false;
  showErrorModal: boolean = false;
  errorModalTitle: string = 'Login Error';
  errorModalMessage: string = '';
  errorModalAction: string = 'Try Again';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private db: Db,
    private storage: Storage
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  onSubmit() {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });

    if (this.loginForm.valid) {
      this.loading = true;
      this.errorMessage = '';

      const { email, password } = this.loginForm.value;

      this.authService.login(email, password).subscribe({
        next: (response) => {
          console.log('Login successful', response);

          // Get the user information
          const user = this.db.current_doctor();
          if (!user) {
            this.errorMessage = 'Error getting user information';
            this.loading = false;
            return;
          }

          // Check if we have a patient ID before navigating
          const patientId = this.authService.getPatientId();
          if (!patientId && response.role === 'PATIENT' && (response.id || response.userId)) {
            // For patients, if no explicit patientId, use the userId/id as patientId
            const userId = response.id || response.userId || '';
            if (userId) {
              // Store the patient ID in both formats for compatibility
              this.storage.setItem('patient_id', userId);
              this.storage.setItem('med_secure_patient_id', userId);
              console.log('Patient ID set from user ID in login component:', userId);
            }
          } else if (patientId) {
            // Ensure we also store the patient ID in the format expected by the medical records service
            this.storage.setItem('med_secure_patient_id', patientId);
            console.log('Patient ID set in med_secure_patient_id:', patientId);
          }

          // Store user info for display on dashboard - combine firstname and lastname for display
          if (user.firstname || user.lastname) {
            const fullName = `${user.firstname || ''} ${user.lastname || ''}`.trim();
            this.storage.setItem('user_displayname', fullName);
          }

          // Verify that user info is saved to localStorage
          const userInfo = localStorage.getItem('user_info');
          if (userInfo) {
            console.log('User info successfully saved to localStorage:', JSON.parse(userInfo));
          } else {
            console.warn('User info not found in localStorage after login');
          }

          this.router.navigate(['/mobile/patient-dashboard']);
        },
        error: (error) => {
          console.error('Login failed', error);
          // Use the error message directly since it's now user-friendly from the service
          this.errorMessage = typeof error === 'string' ? error : (error?.message || "Login failed. Please check your credentials and try again.");
          this.loading = false;

          // Show error modal for better user experience
          this.showErrorModal = true;
          this.errorModalMessage = this.errorMessage;
        },
        complete: () => {
          this.loading = false;
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields correctly.';
      if (this.loginForm.get('email')?.errors) {
        this.errorMessage = this.getEmailErrorMessage();
      } else if (this.loginForm.get('password')?.errors) {
        this.errorMessage = this.getPasswordErrorMessage();
      }
    }
  }

  getEmailErrorMessage() {
    const email = this.loginForm.controls['email'];
    if (email.hasError('required')) return 'Email is required.';
    if (email.hasError('email')) return 'Invalid email format.';
    return '';
  }

  getPasswordErrorMessage() {
    const password = this.loginForm.controls['password'];
    if (password.hasError('required')) return 'Password is required.';
    if (password.hasError('minlength')) return 'Password must be at least 6 characters.';
    return '';
  }

  onModalClose() {
    this.showErrorModal = false;
  }

  navigateToRegister() {
    this.router.navigate(['/mobile/register']);
  }
}
