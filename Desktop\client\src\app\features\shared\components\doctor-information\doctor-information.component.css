.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  width: 100%;
}

.full-width {
  grid-column: 1 / -1; /* Spans all columns */
}

.form-content {
  background: #ffffff;
  border-radius: 0.75rem;
  padding: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.input-group {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.input-group input,
.input-group textarea {
  width: 100%;
  padding: 0.85rem 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: all 0.25s ease;
  background-color: #ffffff;
}

.input-group input:focus,
.input-group textarea:focus {
  border-color: #199A8E;
  box-shadow: 0 0 0 3px rgba(25, 154, 142, 0.15);
  outline: none;
}

.input-group input::placeholder,
.input-group textarea::placeholder {
  color: #cbd5e1;
}

textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.5;
}

.photo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2.5rem;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-preview {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 3px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative; /* For proper positioning */
}

.image-preview:hover {
  border-color: #199A8E;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f1f5f9;
}

.upload-placeholder i {
  font-size: 3rem;
  color: #94a3b8;
}

.upload-button,
.save-button {
  background: #199A8E;
  color: white;
  border: none;
  padding: 0.85rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.upload-button:hover,
.save-button:hover {
  background: #158276;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(25, 154, 142, 0.2);
}

.upload-button:active,
.save-button:active {
  transform: translateY(0);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  min-height: 36px;
  padding: 0.25rem 0;
}

.tag {
  background: #f8fafc;
  padding: 0.6rem 1rem;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #2d3748;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.tag i {
  cursor: pointer;
  color: #64748b;
  transition: color 0.2s ease;
}

.tag i:hover {
  color: #e53e3e;
}

.add-tag {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.add-tag input {
  flex: 1;
  min-width: 0; /* Prevents input from overflowing */
}

.add-tag button {
  background: #199A8E;
  color: white;
  border: none;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap; /* Prevents text wrapping */
}

.add-tag button:hover {
  background: #158276;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(25, 154, 142, 0.2);
}

.action-buttons {
  margin-top: 2.5rem;
  text-align: right;
  padding-right: 1rem;
}

.save-button {
  padding: 1rem 2.5rem;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Back button styles */
.back-button {
  background: none;
  border: none;
  color: #199A8E;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  margin-bottom: 1.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  font-weight: 500;
}

.back-button:hover {
  color: #158276;
  background: rgba(25, 154, 142, 0.05);
  transform: translateX(-2px);
}

/* Header styles */
.header {
  margin-bottom: 2.5rem;
}

.header h1 {
  color: #199A8E;
  font-size: 1.75rem;
  margin: 0;
  font-weight: 600;
}

.header p {
  color: #64748b;
  margin-top: 0.5rem;
  font-size: 0.95rem;
}

/* Page Container and Sidebar Styles */
.page-container {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
  width: 100%;
}

.sidebar {
  width: 260px;
  background: #ffffff;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
}

.main-content {
  flex: 1;
  padding: 2rem;
  margin: 0 auto;
  max-width: 1200px;
  width: 100%;
}

.doctor-info-container {
  background: #ffffff;
  border-radius: 1rem;
  padding: 2.5rem;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
  overflow: hidden; /* Prevents content overflow */
}

/* Responsive Design */
@media (max-width: 768px) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .full-width {
    grid-column: auto;
  }

  .form-content {
    padding: 1.5rem;
  }

  .sidebar {
    width: 100%;
    position: relative;
    height: auto;
  }

  .main-content {
    margin-left: 0;
  }

  .input-group {
    margin-bottom: 1.25rem;
  }

  .photo-section {
    margin-bottom: 2rem;
  }

  .action-buttons {
    text-align: center;
    padding-right: 0;
  }

  .save-button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 1rem;
  }

  .doctor-info-container {
    padding: 1.5rem 1rem;
    border-radius: 0.75rem;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .form-content {
    padding: 1rem;
  }

  .add-tag {
    flex-direction: column;
  }

  .add-tag button {
    width: 100%;
  }
}

@media (max-width: 992px) {
  .doctor-info-container {
    padding: 2rem 1.5rem;
  }

  .form-content {
    padding: 1.5rem;
  }
}
