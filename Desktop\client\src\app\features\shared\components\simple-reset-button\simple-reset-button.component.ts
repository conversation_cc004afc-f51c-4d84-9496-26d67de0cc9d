import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FirebaseDbService } from '../../../../core/services/firebase-db.service';

@Component({
  selector: 'app-simple-reset-button',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div style="position: fixed; top: 20px; right: 20px; z-index: 10000;">
      <button 
        (click)="resetFirebase()"
        [disabled]="loading"
        style="
          background-color: #dc2626; 
          color: white; 
          border: none; 
          padding: 12px 20px; 
          border-radius: 8px; 
          font-weight: bold; 
          font-size: 14px;
          cursor: pointer;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transition: all 0.2s;
        "
        [style.background-color]="loading ? '#9ca3af' : '#dc2626'"
        [style.cursor]="loading ? 'not-allowed' : 'pointer'"
        onmouseover="this.style.backgroundColor='#b91c1c'"
        onmouseout="this.style.backgroundColor='#dc2626'">
        
        <span *ngIf="!loading">🔥 RESET FIREBASE</span>
        <span *ngIf="loading">🔄 Resetting...</span>
      </button>
      
      <!-- Result Message -->
      <div *ngIf="message" 
           style="
             margin-top: 8px; 
             padding: 8px 12px; 
             border-radius: 6px; 
             font-size: 12px; 
             font-weight: 500;
             max-width: 200px;
             word-wrap: break-word;
           "
           [style.background-color]="success ? '#dcfce7' : '#fef2f2'"
           [style.color]="success ? '#166534' : '#dc2626'"
           [style.border]="success ? '1px solid #bbf7d0' : '1px solid #fecaca'">
        {{ message }}
      </div>
    </div>
  `
})
export class SimpleResetButtonComponent {
  private firebaseDbService = inject(FirebaseDbService);

  loading = false;
  message = '';
  success = false;

  resetFirebase(): void {
    if (this.loading) return;

    // Simple confirmation
    const confirmed = confirm(
      '🚨 WARNING: This will DELETE ALL data from Firebase!\n\n' +
      'This includes:\n' +
      '• All users and profiles\n' +
      '• All appointments\n' +
      '• All medical records\n' +
      '• All doctor/patient data\n\n' +
      'This action CANNOT be undone!\n\n' +
      'Are you sure you want to continue?'
    );

    if (!confirmed) return;

    // Double confirmation
    const doubleConfirmed = confirm(
      '⚠️ FINAL WARNING!\n\n' +
      'You are about to permanently delete ALL Firebase data.\n\n' +
      'Type "DELETE" in the next prompt to confirm.'
    );

    if (!doubleConfirmed) return;

    const userInput = prompt('Type "DELETE" to confirm:');
    if (userInput !== 'DELETE') {
      alert('Reset cancelled. You must type "DELETE" exactly.');
      return;
    }

    this.loading = true;
    this.message = '';

    console.log('🚨 Starting Firebase reset...');

    this.firebaseDbService.resetAllFirebaseData().subscribe({
      next: (result) => {
        this.loading = false;
        this.success = result.success;
        
        if (result.success) {
          const totalDeleted = Object.values(result.deletedCounts || {})
            .reduce((sum: number, count) => sum + (count as number), 0);
          
          this.message = `✅ Success! Deleted ${totalDeleted} items.`;
          console.log('✅ Firebase reset successful:', result);
          
          // Auto-refresh after 3 seconds
          setTimeout(() => {
            if (confirm('Reset complete! Refresh the page now?')) {
              window.location.reload();
            }
          }, 3000);
        } else {
          this.message = `❌ Failed: ${result.message}`;
          console.error('❌ Firebase reset failed:', result);
        }

        // Clear message after 10 seconds
        setTimeout(() => {
          this.message = '';
        }, 10000);
      },
      error: (error) => {
        this.loading = false;
        this.success = false;
        this.message = `❌ Error: ${error.message}`;
        console.error('❌ Firebase reset error:', error);

        // Clear message after 10 seconds
        setTimeout(() => {
          this.message = '';
        }, 10000);
      }
    });
  }
}
