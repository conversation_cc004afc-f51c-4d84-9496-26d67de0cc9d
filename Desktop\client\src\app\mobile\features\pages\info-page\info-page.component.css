.info-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100vh;
  padding: clamp(0.5rem, 3vw, 1.5rem);
  box-sizing: border-box;
}

.top {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.current-index {
  width: clamp(60px, 15vw, 80px);
  height: auto;
  display: flex;
  justify-content: space-between;
  gap: clamp(0.3rem, 2vw, 0.6rem);
  margin: clamp(10px, 3vh, 20px) auto;
}

.index {
  background: #bbb5b5;
  width: clamp(12px, 3vw, 17px);
  height: clamp(12px, 3vw, 17px);
  border-radius: 50%;
}

.clicked {
  background: #199a8e;
}

.skip {
  font-size: clamp(14px, 4vw, 17px);
  text-align: end;
  cursor: pointer;
  padding: 0.5rem;
}

.btn {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  bottom: clamp(20px, 10vh, 90px);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  padding: 0 1rem;
}

.btn > button {
  background: #199a8e;
  width: 100%;
  max-width: 400px;
  padding: clamp(0.8rem, 2vh, 1.2rem);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: clamp(14px, 3vw, 16px);
}

@media screen and (max-height: 500px) {
  .info-page {
    padding: 0.5rem;
  }
  
  .btn {
    position: relative;
    bottom: 0;
    margin: 1rem 0;
  }
}

@media screen and (min-width: 768px) {
  .info-page {
    padding: 1.5rem;
  }
  
  .skip {
    font-size: 16px;
  }
} 

  app-info-slider {
    min-height: auto;
  }


@media screen and (min-width: 768px) {
  .info-page {
    padding: 1.5rem;
  }
  
  .skip {
    font-size: 16px;
  }
}