import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService, RegisterRequest } from '../../services/auth.service';
import { HttpClientModule } from '@angular/common/http';
import { ErrorModalComponent } from '../../../../shared/components/error-modal/error-modal.component';
import { LoadingSpinnerComponent } from '../../../../shared/components/loading-spinner/loading-spinner.component';

import { Db } from '../../../../db';
import { DoctorType, UserType } from '../../../../type';
import { DoctorService } from '../../../../features/doctor/services/doctor.service';

interface UserInfo {
  name: string;
  email: string;
  role: string;
  id?: string;
  doctorId?: string;
  firstName?: string;
  lastName?: string;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterLink,
    HttpClientModule,
    ErrorModalComponent,
    LoadingSpinnerComponent
  ],
  providers: [AuthService, DoctorService],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
})
export class RegisterComponent2 implements OnInit {
  registerForm!: FormGroup;
  loading = false;
  errorMessage = '';
  showErrorModal = false;
  errorModalTitle = '';
  errorModalMessage = '';
  errorModalAction = '';
  selectedRole = 'patient'; // Mobile app is always for patients
  emailTouched = false;
  emailErrorMessage = '';
  registrationSuccess = false;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private doctorService: DoctorService,
    private db: Db,
    private router: Router
  ) {
    this.registerForm = this.fb.group(
      {
        fullname: [
          '',
          [Validators.required, Validators.pattern(/^[a-zA-Z]+ [a-zA-Z]+/)],
        ],
        email: [
          '',
          [
            Validators.required,
            Validators.email,
            this.validEmailDomainValidator(),
          ],
        ],
        password: ['', [Validators.required, Validators.minLength(6)]],
        confirmPassword: ['', Validators.required],
      },
      {
        validators: this.passwordMatchValidator,
      }
    );

    // Reset error states when email changes
    this.registerForm.get('email')?.valueChanges.subscribe(() => {
      this.errorMessage = '';
      this.showErrorModal = false;
      if (this.emailTouched) {
        this.updateEmailErrorMessage();
      }
    });
  }

  ngOnInit(): void {
    // Check if user is already logged in
    if (this.authService.isLoggedIn()) {
      this.router.navigate(['/mobile/patient-dashboard']);
    }
  }

  validEmailDomainValidator() {
    return (control: AbstractControl): ValidationErrors | null => {
      const email = control.value;
      if (!email) {
        return null;
      }

      // Basic email pattern check
      const basicEmailPattern =
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!basicEmailPattern.test(email)) {
        return { invalidEmailFormat: true };
      }

      // Check for common domain extensions
      const domainPart = email.split('@')[1];
      if (!domainPart) {
        return { invalidDomain: true };
      }

      // Check if TLD has valid format (2-63 characters)
      const tld = domainPart.split('.').pop();
      if (!tld || tld.length < 2 || tld.length > 63) {
        return { invalidTLD: true };
      }

      // Check for common invalid TLDs that users might make up
      const invalidTLDPatterns = [
        /com[a-z]{1,}$/, // catches commmm, commss, etc.
        /co[a-z]{3,}$/, // catches coooom, cooooza, etc.
        /org[a-z]{1,}$/, // catches orggg, etc.
        /net[a-z]{1,}$/, // catches nettt, etc.
      ];

      for (const pattern of invalidTLDPatterns) {
        if (pattern.test(tld)) {
          return { invalidTLD: true };
        }
      }

      return null;
    };
  }

  passwordMatchValidator(control: AbstractControl): ValidationErrors | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (
      password &&
      confirmPassword &&
      password.value !== confirmPassword.value
    ) {
      confirmPassword.setErrors({ passwordMismatch: true });
      return { passwordMismatch: true };
    }

    return null;
  }

  showDuplicateEmailError() {
    this.errorModalTitle = 'Email Already Registered';
    this.errorModalMessage =
      'An account with this email already exists. Would you like to login instead?';
    this.errorModalAction = 'Go to Login';
    this.showErrorModal = true;
  }

  onEmailBlur() {
    this.emailTouched = true;
    this.updateEmailErrorMessage();
  }

  updateEmailErrorMessage() {
    const emailControl = this.registerForm.get('email');
    if (emailControl && emailControl.errors && this.emailTouched) {
      if (emailControl.errors['required']) {
        this.emailErrorMessage = 'Email is required';
      } else if (emailControl.errors['email']) {
        this.emailErrorMessage = 'Please enter a valid email address';
      } else if (emailControl.errors['invalidEmailFormat']) {
        this.emailErrorMessage = 'Please enter a valid email address';
      } else if (emailControl.errors['invalidDomain']) {
        this.emailErrorMessage = 'Invalid email domain';
      } else if (emailControl.errors['invalidTLD']) {
        this.emailErrorMessage =
          'The email extension you entered appears to be invalid. Please use a valid email address (e.g., <EMAIL>)';
      } else {
        this.emailErrorMessage = 'Please enter a valid email address';
      }
    } else {
      this.emailErrorMessage = '';
    }
  }

  onModalClose() {
    this.showErrorModal = false;

    if (this.registrationSuccess) {
      this.router.navigate(['/mobile/login']);
      return;
    }

    this.errorMessage = '';
    // Reset the email field only if it's a duplicate email error
    if (this.errorModalTitle === 'Email Already Registered') {
      this.registerForm.patchValue({ email: '' });
      this.registerForm.get('email')?.markAsUntouched();
      this.emailTouched = false;
      this.emailErrorMessage = '';
    }
  }

  navigateToLogin() {
    this.router.navigate(['/mobile/login']);
  }

  onSubmit() {
    if (this.showErrorModal) {
      return; // Don't proceed if error modal is showing
    }

    // Mark all fields as touched to trigger validation
    Object.keys(this.registerForm.controls).forEach(key => {
      this.registerForm.get(key)?.markAsTouched();
    });

    if (this.registerForm.valid) {
      this.loading = true;
      this.errorMessage = '';

      // Parse the fullname into first and last name
      const fullname = this.registerForm.get('fullname')?.value.trim();
      const nameParts = fullname.split(' ');
      const firstName = nameParts[0];
      const lastName = nameParts.slice(1).join(' ');

      // Create the registration request
      const registerRequest: RegisterRequest = {
        firstName: firstName,
        lastName: lastName,
        email: this.registerForm.get('email')?.value,
        password: this.registerForm.get('password')?.value,
        role: this.selectedRole // Always 'patient' for mobile app
      };

      this.authService.register(registerRequest).subscribe({
        next: (response) => {
          console.log('Registration successful:', response);
          this.loading = false;
          // Directly navigate to patient dashboard
          this.router.navigate(['/mobile/patient-dashboard']);
        },
        error: (error) => {
          console.error('Registration failed:', error);
          this.loading = false;

          // Handle specific error messages
          if (error.error && error.error.message === 'Email already exists') {
            this.showDuplicateEmailError();
          } else {
            this.errorModalTitle = 'Registration Failed';
            this.errorModalMessage = typeof error === 'string'
              ? error
              : error.error?.message || 'An error occurred during registration. Please try again.';
            this.errorModalAction = 'Try Again';
            this.showErrorModal = true;
          }
        }
      });
    } else {
      // Form is invalid - show error messages
      this.errorMessage = 'Please fill in all required fields correctly.';

      // Check specific validation errors
      if (this.registerForm.get('fullname')?.errors) {
        this.errorMessage = 'Please enter your full name (first and last name).';
      } else if (this.registerForm.get('email')?.errors) {
        this.errorMessage = this.emailErrorMessage || 'Please enter a valid email address.';
      } else if (this.registerForm.get('password')?.errors) {
        this.errorMessage = 'Password must be at least 6 characters.';
      } else if (this.registerForm.hasError('passwordMismatch')) {
        this.errorMessage = 'Passwords do not match.';
      }
    }
  }
}
