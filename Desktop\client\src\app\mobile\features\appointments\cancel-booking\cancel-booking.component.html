<div class="container">
  <app-back-button routerLink="/schedule"/>
  <header class="header">
    <h1>Cancel Appointment</h1>
  </header>

  <app-navbar />

  <main class="main-content">
    <p class="confirmation-text">
      Please select your reason for cancelling your appointment below...
    </p>

    <div class="reasons-list">
      <div *ngFor="let reason of cancelReasons" class="reason-item">
        <label [class]="{'selected': reason.selected}">
          <input type="radio" name="cancelReason" [value]="reason.id" [(ngModel)]="selectedReason" (change)="onReasonSelect(reason)">
          <span class="radio-custom"></span>
          <span class="reason-text">{{ reason.label }}</span>
        </label>
      </div>
    </div>

    <textarea
      class="reason-textarea"
      placeholder="Enter Your Reason Here..."
      [(ngModel)]="additionalReason"
    ></textarea>

    <button class="cancel-button" (click)="onCancelAppointment()">
      Cancel Appointment
    </button>

  

  </main>
</div>
