<div class="doctor-profile-container">
  <header class="profile-header">
    <h1>Doctor Profile</h1>
    <p>Manage your professional information</p>
    <p class="required-fields-note"><span class="required-star">*</span> Required fields must be filled</p>
  </header>

  <main class="profile-content">
    <div class="left-column">
      <section class="personal-info">
        <div class="photo-upload">
          <div class="image-preview" [class.has-image]="imagePreview">
            <img *ngIf="imagePreview" [src]="imagePreview" alt="Profile preview">
            <div *ngIf="!imagePreview" class="upload-placeholder">
              <svg class="upload-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M17 8l-5-5-5 5M12 3v12"/>
              </svg>
              <span>Upload Photo</span>
            </div>
          </div>
          <input type="file"
                 accept="image/*"
                 (change)="onImageSelected($event)"
                 #fileInput
                 style="display: none">
          <button class="upload-button" (click)="fileInput.click()">
            {{ imagePreview ? 'Change Photo' : 'Upload Photo' }}
          </button>
        </div>

        <div class="basic-info">
          <div class="input-container">
            <label>Full Name <span class="required-star">*</span></label>
            <input type="text" [(ngModel)]="fullName" [class.invalid-input]="isSubmitting && !fullName" (input)="onInputChange()">
            <div *ngIf="isSubmitting && !fullName" class="error-message">Full name is required</div>
          </div>

          <div class="input-container">
            <label>Email <span class="required-star">*</span></label>
            <input type="email" [(ngModel)]="email" readonly>
          </div>

          <div class="input-container">
            <label>Phone Number <span class="required-star">*</span></label>
            <input type="tel" [(ngModel)]="phoneNumber" [class.invalid-input]="isSubmitting && !phoneNumber" (input)="onInputChange()">
            <div *ngIf="isSubmitting && !phoneNumber" class="error-message">Phone number is required</div>
          </div>

          <div class="input-container">
            <label>Specialization <span class="required-star">*</span></label>
            <input type="text" [(ngModel)]="specialization" [class.invalid-input]="isSubmitting && !specialization" (input)="onInputChange()">
            <div *ngIf="isSubmitting && !specialization" class="error-message">Specialization is required</div>
          </div>

        </div>
      </section>
    </div>

    <div class="right-column">
      <section class="bio">
        <h2><i class="bi bi-heart-pulse"></i>Bio</h2>
        <textarea [(ngModel)]="bio" placeholder="Please write your bio here..."></textarea>
      </section>

      <section class="qualifications">
        <h2><i class="bi bi-mortarboard"></i>Qualifications & Certifications</h2>
        <div class="qualification-container">
          <div class="qualification-tags">
            <div class="qualification-pill" *ngFor="let qualification of qualifications; let i = index">
              {{ qualification }}
              <i class="bi bi-x-circle" (click)="removeQualification(i)"></i>
            </div>
          </div>
          <input type="text"
                 [(ngModel)]="newQualification"
                 (keyup.enter)="addQualification()"
                 placeholder="Add another qualification...">
        </div>
      </section>

      <section class="hospital-affiliations">
        <h2><i class="bi bi-hospital"></i>Hospital Affiliations</h2>
        <div class="hospital-affiliation-container">
          <div class="hospital-affiliation-tags">
            <div class="hospital-affiliation-pill" *ngFor="let affiliation of hospitalAffiliations; let i = index">
              {{ affiliation }}
              <i class="bi bi-x-circle" (click)="removeHospitalAffiliation(i)"></i>
            </div>
          </div>
          <input type="text"
                 [(ngModel)]="newHospitalAffiliation"
                 (keyup.enter)="addHospitalAffiliation()"
                 placeholder="Add another affiliated institution...">
        </div>
      </section>
    </div>
  </main>

  <div class="action-buttons">
    <button class="save-profile" (click)="proceedToSubscription()">
      Save Profile
    </button>
  </div>
</div>

<!-- Success Modal -->
<div class="success-modal-overlay" *ngIf="showSuccessModal">
  <div class="success-modal">
    <div class="success-icon">
      <i class="bi bi-check-circle-fill"></i>
    </div>
    <h2>Profile Updated Successfully!</h2>
    <p>Your doctor profile has been updated with all the information you provided.</p>
    <button class="proceed-button" (click)="closeModalAndProceed()">
      Proceed to Subscription
    </button>
  </div>
</div>
