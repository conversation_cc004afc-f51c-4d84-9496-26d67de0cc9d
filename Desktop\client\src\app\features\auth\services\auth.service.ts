import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, throwError, BehaviorSubject, from } from 'rxjs';
import { map, catchError, switchMap, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { Db } from '../../../db';
import {
  Auth,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  User as FirebaseUser
} from '@angular/fire/auth';
import { Firestore, doc, getDoc, setDoc, updateDoc, collection, query, where, getDocs } from '@angular/fire/firestore';
import { DoctorType } from '../../../type';
import { AuthErrorMapperService } from '../../../core/services/auth-error-mapper.service';

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: 'doctor' | 'patient' | string;
  specialty?: string;
  medicalLicenseNumber?: string;
}

export interface RegisterResponse {
  userId?: string;
  message: string;
  success: boolean;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token?: string;
  userId?: string;
  name?: string;
  email?: string;
  role?: string;
  message?: string;
  success: boolean;
}

export interface UserInfo {
  name: string;
  email: string;
  role: string;
  id?: string;
  doctorId?: string;
  firstName?: string;
  lastName?: string;
  token?: string;
  specialization?: string;
  // Additional properties needed by other components
  profilePicture?: string | null;
  phoneNumber?: string;
  address?: string;
  bio?: string;
  hospitalAffiliations?: string[] | string;
  educationDetails?: string[] | string;
  certifications?: string[] | string;
  qualifications?: string[] | string;
  services?: string[] | string;
  patientCount?: number;
}

export interface ProfileUpdateResponse {
  message: string;
  id?: string;
  firstName?: string;
  lastName?: string;
  services?: string | string[];
  phoneNumber?: string;
  specialization?: string;
  bio?: string;
  address?: string;
  hospitalAffiliations?: string[] | string;
  qualifications?: string[] | string;
  profilePicture?: string | null;
  contactNumber?: string;
  education?: string;
  experience?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly USER_INFO_KEY = 'med_secure_user_info';
  private readonly TOKEN_KEY = 'med_secure_token';
  private userInfoSubject = new BehaviorSubject<UserInfo | null>(null);
  public isAuthenticated$ = this.userInfoSubject.pipe(map(userInfo => !!userInfo));

  constructor(
    private http: HttpClient,
    private router: Router,
    private db: Db,
    private auth: Auth,
    private firestore: Firestore,
    private authErrorMapper: AuthErrorMapperService
  ) {
    this.autoLogin();
  }

  /**
   * Register a new user
   * @param registerData The registration data
   * @returns Observable<RegisterResponse>
   */
  register(registerData: RegisterRequest): Observable<RegisterResponse> {
    // Check if email already exists in local DB
    const existingUser = this.db.userTable().find(user => user.email === registerData.email);
    if (existingUser) {
      return throwError(() => ({ message: 'Email already exists', success: false }));
    }

    // Start Firebase registration
    return from(createUserWithEmailAndPassword(
      this.auth,
      registerData.email,
      registerData.password
    )).pipe(
      switchMap(userCredential => {
        const userId = userCredential.user.uid;

        // First, create the user in local DB
        const newUser = {
          id: userId,
          firstname: registerData.firstName,
          lastname: registerData.lastName,
          email: registerData.email,
          password: registerData.password, // Note: Storing plaintext passwords in local DB for development only
          role: registerData.role.toUpperCase() as 'DOCTOR' | 'PATIENT'
        };

        // Register user in local Db
        this.db.register(newUser);
        console.log('Registration successful with local DB:', newUser);

        // Create user document in Firestore
        const userDoc = {
          uid: userId,
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          email: registerData.email,
          role: registerData.role.toLowerCase(),
          createdAt: new Date().toISOString(),
          profileComplete: false, // Track if profile is complete
          // Add empty fields for profile completion
          bio: '',
          phoneNumber: '',
          hospitalAffiliations: [],
          qualifications: [],
          services: [],
          profilePicture: null
        };

        // Create UserInfo object right away
        const userInfo: UserInfo = {
          name: `${registerData.firstName} ${registerData.lastName}`,
          email: registerData.email,
          role: registerData.role.toLowerCase(),
          id: userId,
          firstName: registerData.firstName,
          lastName: registerData.lastName,
          // Add empty fields to match what's in Firestore
          bio: '',
          phoneNumber: '',
          hospitalAffiliations: [],
          qualifications: [],
          services: [],
          profilePicture: null
        };

        // If user is a doctor, add doctor specific fields
        if (registerData.role.toLowerCase() === 'doctor') {
          Object.assign(userDoc, {
            specialization: registerData.specialty || '',
            medicalLicenseNumber: registerData.medicalLicenseNumber || '',
          });

          // Add doctor-specific fields to userInfo
          userInfo.doctorId = userId;
          userInfo.specialization = registerData.specialty;

          // Also create doctor in local DB
          const doctorId = userId;
          const newDoctor = {
            id: doctorId,
            bio: '',
            image: '',
            hospitalname: '',
            qualification: registerData.medicalLicenseNumber || '',
            specialisation: registerData.specialty || '',
            contact: '',
            paymentPlan: '',
            user_id: userId
          };

          // Add doctor to local Db
          this.db.addNewDoctor(newDoctor);
        }

        // Save user info to localStorage BEFORE Firestore operation
        // This ensures the data is available immediately after registration
        this.saveUserInfo(userInfo);

        // Store user info in Firestore
        const userDocRef = doc(this.firestore, `users/${userId}`);
        return from(setDoc(userDocRef, userDoc)).pipe(
          map(() => {
            // Return success response
            return {
              userId: userId,
              message: 'Registration successful',
              success: true
            };
          })
        );
      }),
      catchError(error => {
        console.error('Firebase registration error:', error);
        const userFriendlyMessage = this.authErrorMapper.mapRegistrationError(error);
        return throwError(() => ({
          message: userFriendlyMessage,
          success: false
        }));
      })
    );
  }

  /**
   * Login a user
   * @param email The user's email
   * @param password The user's password
   * @returns Observable<LoginResponse>
   */
  login(email: string, password: string): Observable<LoginResponse> {
    // First try Firebase authentication
    return from(signInWithEmailAndPassword(this.auth, email, password)).pipe(
      switchMap(userCredential => {
        const uid = userCredential.user.uid;

        // Also check if user exists in local DB
        const localUser = this.db.login(email, password);

        if (localUser) {
          // User exists in both Firebase and local DB
          const response: LoginResponse = {
            userId: uid,
            name: `${localUser.firstname} ${localUser.lastname}`,
            email: localUser.email,
            role: localUser.role.toLowerCase(),
            success: true
          };

          // Get additional doctor info if user is a doctor
          if (localUser.role === 'DOCTOR') {
            const doctor = this.db.doctorTable().find(d => d.user_id === localUser.id);
            if (doctor) {
              const userInfo: UserInfo = {
                name: `${localUser.firstname} ${localUser.lastname}`,
                email: localUser.email,
                role: localUser.role.toLowerCase(),
                id: uid,
                doctorId: doctor.id,
                firstName: localUser.firstname,
                lastName: localUser.lastname,
                specialization: doctor.specialisation,
                bio: doctor.bio,
                phoneNumber: doctor.contact,
                hospitalAffiliations: doctor.hospitalname,
                profilePicture: doctor.image || null
              };
              this.saveUserInfo(userInfo);
            }
          } else {
            // For non-doctor users
            const userInfo: UserInfo = {
              name: `${localUser.firstname} ${localUser.lastname}`,
              email: localUser.email,
              role: localUser.role.toLowerCase(),
              id: uid,
              firstName: localUser.firstname,
              lastName: localUser.lastname
            };
            this.saveUserInfo(userInfo);
          }

          return of(response);
        } else {
          // User exists in Firebase but not in local DB
          // Create a basic record in local DB
          const names = email.split('@')[0].split('.');
          const firstname = names[0] || 'User';
          const lastname = names.length > 1 ? names[1] : '';

          const newUser = {
            id: uid,
            firstname: firstname,
            lastname: lastname,
            email: email,
            password: password, // Note: Storing plaintext passwords in local DB for development only
            role: 'DOCTOR' as 'DOCTOR' // Properly typed as 'DOCTOR'
          };

          // Add to local DB
          this.db.register(newUser);

          // Create basic UserInfo
          const userInfo: UserInfo = {
            name: `${firstname} ${lastname}`,
            email: email,
            role: 'doctor',
            id: uid,
            doctorId: uid,
            firstName: firstname,
            lastName: lastname
          };

          this.saveUserInfo(userInfo);

          return of({
            userId: uid,
            name: `${firstname} ${lastname}`,
            email: email,
            role: 'doctor',
            success: true
          });
        }
      }),
      catchError(error => {
        console.error('Firebase login error:', error);

        // Try local DB as fallback
        const localUser = this.db.login(email, password);
        if (localUser) {
          // User exists in local DB but not in Firebase
          // We should migrate this user to Firebase
          return this.migrateLocalUserToFirebase(localUser, password);
        }

        // Map Firebase error to user-friendly message
        const userFriendlyMessage = this.authErrorMapper.mapFirebaseError(error);
        return throwError(() => ({
          message: userFriendlyMessage,
          success: false
        }));
      })
    );
  }

  /**
   * Migrate a local user to Firebase
   * @param localUser The local user to migrate
   * @param password The user's password
   * @returns Observable<LoginResponse>
   */
  private migrateLocalUserToFirebase(localUser: any, password: string): Observable<LoginResponse> {
    return from(createUserWithEmailAndPassword(
      this.auth,
      localUser.email,
      password
    )).pipe(
      switchMap(userCredential => {
        const uid = userCredential.user.uid;

        // Update local user with Firebase UID
        localUser.id = uid;

        // Create user document in Firestore
        const userDoc = {
          uid: uid,
          firstName: localUser.firstname,
          lastName: localUser.lastname,
          email: localUser.email,
          role: localUser.role.toLowerCase(),
          createdAt: new Date().toISOString()
        };

        // Store user in Firestore
        const userDocRef = doc(this.firestore, `users/${uid}`);
        return from(setDoc(userDocRef, userDoc)).pipe(
          map(() => {
            // Create UserInfo
            const userInfo: UserInfo = {
              name: `${localUser.firstname} ${localUser.lastname}`,
              email: localUser.email,
              role: localUser.role.toLowerCase(),
              id: uid,
              firstName: localUser.firstname,
              lastName: localUser.lastname
            };

            // Save user info
            this.saveUserInfo(userInfo);

            return {
              userId: uid,
              name: `${localUser.firstname} ${localUser.lastname}`,
              email: localUser.email,
              role: localUser.role.toLowerCase(),
              success: true
            };
          })
        );
      }),
      catchError(error => {
        console.error('Migration to Firebase failed:', error);

        // Still allow login with local DB as fallback
        const userInfo: UserInfo = {
          name: `${localUser.firstname} ${localUser.lastname}`,
          email: localUser.email,
          role: localUser.role.toLowerCase(),
          id: localUser.id,
          firstName: localUser.firstname,
          lastName: localUser.lastname
        };

        this.saveUserInfo(userInfo);

        return of({
          userId: localUser.id,
          name: `${localUser.firstname} ${localUser.lastname}`,
          email: localUser.email,
          role: localUser.role.toLowerCase(),
          success: true
        });
      })
    );
  }

  /**
   * Save user info to localStorage
   * @param userInfo The user info to save
   */
  saveUserInfo(userInfo: UserInfo): void {
    localStorage.setItem(this.USER_INFO_KEY, JSON.stringify(userInfo));

    // If there's a token, save it separately
    if (userInfo.token) {
      localStorage.setItem(this.TOKEN_KEY, userInfo.token);
    }

    // Update the BehaviorSubject
    this.userInfoSubject.next(userInfo);

    console.log('User info saved and updated:', userInfo);
  }

  /**
   * Get user info from localStorage
   * @returns UserInfo | null
   */
  getUserInfo(): UserInfo | null {
    // First try to get from localStorage
    const userInfoStr = localStorage.getItem(this.USER_INFO_KEY);
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr) as UserInfo;
        // Update the BehaviorSubject if not already set
        if (!this.userInfoSubject.getValue()) {
          this.userInfoSubject.next(userInfo);
        }
        return userInfo;
      } catch (error) {
        console.error('Error parsing user info:', error);
        return null;
      }
    }

    // If not in localStorage, try to get from the BehaviorSubject
    const userInfo = this.userInfoSubject.getValue();
    return userInfo;
  }

  /**
   * Check if user is authenticated
   * @returns boolean
   */
  isAuthenticated(): boolean {
    return !!this.getUserInfo();
  }

  /**
   * Logout user
   */
  logout(): void {
    // Sign out from Firebase
    signOut(this.auth).then(() => {
      // Clear local storage
      localStorage.removeItem(this.USER_INFO_KEY);
      localStorage.removeItem(this.TOKEN_KEY);

      // Clear BehaviorSubject
      this.userInfoSubject.next(null);

      // Navigate to login page
      this.router.navigate(['/login']);
    }).catch(error => {
      console.error('Logout error:', error);
    });
  }

  /**
   * Auto login user if user info exists in localStorage
   */
  autoLogin(): void {
    const userInfoStr = localStorage.getItem(this.USER_INFO_KEY);
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr) as UserInfo;
        // Update the BehaviorSubject
        this.userInfoSubject.next(userInfo);

        // If it's a doctor, refresh doctor details
        if (userInfo.role === 'doctor' && userInfo.doctorId) {
          this.getDoctor(userInfo.doctorId).subscribe({
            next: (doctorInfo) => {
              const updatedUserInfo = { ...userInfo, ...doctorInfo };
              this.saveUserInfo(updatedUserInfo);
            },
            error: (error) => console.error('Error refreshing doctor info:', error)
          });
        }

        console.log('Auto login successful:', userInfo);
      } catch (error) {
        console.error('Error during auto login:', error);
      }
    }
  }

  /**
   * Update user info in localStorage
   * @param updates Partial<UserInfo>
   */
  updateUserInfo(updates: Partial<UserInfo>): void {
    const currentUserInfo = this.getUserInfo();
    if (currentUserInfo) {
      const updatedUserInfo = { ...currentUserInfo, ...updates };
      this.saveUserInfo(updatedUserInfo);
    }
  }

  /**
   * Get doctor ID from user info
   * @returns string | null
   */
  getDoctorId(): string | null {
    const userInfo = this.getUserInfo();
    if (!userInfo) {
      return null;
    }

    // For doctor role, use doctorId or id
    if (userInfo.role && userInfo.role.toLowerCase() === 'doctor') {
      return userInfo.doctorId || userInfo.id || null;
    }

    // For non-doctor roles, use doctorId if available
    return userInfo.doctorId || null;
  }

  /**
   * Update doctor profile
   * @param profileData Profile data to update
   * @returns Observable<ProfileUpdateResponse>
   */
  updateProfile(profileData: any): Observable<ProfileUpdateResponse> {
    const doctorId = this.getDoctorId();
    if (!doctorId) {
      return throwError(() => new Error('Doctor ID not found'));
    }

    // Find the doctor in the database
    const doctor = this.db.doctorTable().find(d => d.id === doctorId);
    if (!doctor) {
      return throwError(() => new Error('Doctor profile not found'));
    }

    // Update doctor profile in database
    const updatedDoctor = { ...doctor };

    if (profileData.bio) updatedDoctor.bio = profileData.bio;
    if (profileData.hospitalname) updatedDoctor.hospitalname = profileData.hospitalname;
    if (profileData.specialization) updatedDoctor.specialisation = profileData.specialization;
    if (profileData.contact) updatedDoctor.contact = profileData.contact;
    if (profileData.experience) updatedDoctor.hospitalname = profileData.experience;
    if (profileData.education) updatedDoctor.qualification = profileData.education;

    // Update doctor in database
    this.db.doctorTable.update(doctors =>
      doctors.map(d => d.id === doctorId ? updatedDoctor : d)
    );

    // Save to localStorage
    this.db.storage.setItem('DOCTOR_TABLE', this.db.doctorTable());

    // Prepare data for Firestore update
    const firestoreData = {
      bio: profileData.bio || '',
      hospitalAffiliations: profileData.experience || profileData.hospitalname || '',
      specialization: profileData.specialization || '',
      phoneNumber: profileData.contactNumber || profileData.contact || '',
      qualifications: profileData.education || '',
      services: profileData.services || '',
      updatedAt: new Date().toISOString()
    };

    // Also update user info in Firestore
    const userDocRef = doc(this.firestore, `users/${doctorId}`);
    return from(updateDoc(userDocRef, firestoreData)).pipe(
      map(() => {
        // Update user info in localStorage
        const userInfo = this.getUserInfo();
        if (userInfo) {
          const updatedUserInfo = {
            ...userInfo,
            specialization: profileData.specialization,
            bio: profileData.bio,
            phoneNumber: profileData.contactNumber || profileData.contact,
            hospitalAffiliations: profileData.experience || profileData.hospitalname,
            qualifications: profileData.education,
            services: profileData.services
          };
          this.saveUserInfo(updatedUserInfo);
        }

        // Return success response
        return {
          message: 'Profile updated successfully in both local DB and Firestore',
          id: doctorId,
          bio: profileData.bio,
          specialization: profileData.specialization,
          phoneNumber: profileData.contactNumber || profileData.contact,
          hospitalAffiliations: profileData.experience || profileData.hospitalname,
          qualifications: profileData.education,
          services: profileData.services
        };
      }),
      catchError(error => {
        console.error('Error updating profile in Firestore:', error);

        // Still update local storage since that was successful
        const userInfo = this.getUserInfo();
        if (userInfo) {
          const updatedUserInfo = {
            ...userInfo,
            specialization: profileData.specialization,
            bio: profileData.bio,
            phoneNumber: profileData.contactNumber || profileData.contact,
            hospitalAffiliations: profileData.experience || profileData.hospitalname,
            qualifications: profileData.education,
            services: profileData.services
          };
          this.saveUserInfo(updatedUserInfo);
        }

        // Return success response for local update
        return of({
          message: 'Profile updated in local DB only (Firestore update failed)',
          id: doctorId,
          bio: profileData.bio,
          specialization: profileData.specialization,
          phoneNumber: profileData.contactNumber || profileData.contact,
          hospitalAffiliations: profileData.experience || profileData.hospitalname
        });
      })
    );
  }

  /**
   * Update profile picture
   * @param base64Image Base64 encoded image
   * @returns Observable<any>
   */
  updateProfilePicture(base64Image: string): Observable<any> {
    const doctorId = this.getDoctorId();
    if (!doctorId) {
      return throwError(() => new Error('Doctor ID not found'));
    }

    // Update in Firestore first since this is our source of truth
    const userDocRef = doc(this.firestore, `users/${doctorId}`);

    // First try to update in Firestore which is our priority data source
    return from(updateDoc(userDocRef, {
      profilePicture: base64Image,
      updatedAt: new Date().toISOString()
    })).pipe(
      tap(() => {
        console.log('Profile picture updated successfully in Firestore');

        // Update user info in localStorage
        const userInfo = this.getUserInfo();
        if (userInfo) {
          const updatedUserInfo = { ...userInfo, profilePicture: base64Image };
          this.saveUserInfo(updatedUserInfo);
        }
      }),
      catchError(error => {
        console.warn('Error updating profile picture in Firestore, creating document:', error);

        // If the document doesn't exist, try to create it instead of updating
        return from(setDoc(userDocRef, {
          profilePicture: base64Image,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }, { merge: true })).pipe(
          tap(() => console.log('Created new user document in Firestore with profile picture')),
          catchError(createError => {
            console.error('Could not create user document in Firestore:', createError);
            return of(null); // Continue with local update only
          })
        );
      }),
      // Now handle the local database update
      switchMap(() => {
        // Find the doctor in the local database
        let doctor = this.db.doctorTable().find(d => d.id === doctorId);

        // Create the doctor profile if it doesn't exist
        if (!doctor) {
          console.log('Doctor profile not found in local DB, creating it');

          // Get the user info to link
          const userInfo = this.getUserInfo();
          const userId = userInfo?.id || doctorId; // Use doctorId as fallback

          // Create a new doctor profile with proper type
          const newDoctor: DoctorType = {
            id: doctorId,
            bio: userInfo?.bio || '',
            image: base64Image,
            hospitalname: userInfo?.hospitalAffiliations?.toString() || '',
            qualification: userInfo?.qualifications?.toString() || '',
            specialisation: userInfo?.specialization || '',
            contact: userInfo?.phoneNumber || '',
            paymentPlan: 'Free',
            user_id: userId
          };

          // Add to doctor table
          this.db.doctorTable.update(doctors => [...doctors, newDoctor]);

          // Set our local variable to the created doctor
          doctor = newDoctor;
        } else {
          // Update existing doctor profile picture
          const updatedDoctor = { ...doctor, image: base64Image };

          // Update doctor in database
          this.db.doctorTable.update(doctors =>
            doctors.map(d => d.id === doctorId ? updatedDoctor : d)
          );
        }

        // Save to localStorage
        this.db.storage.setItem('DOCTOR_TABLE', this.db.doctorTable());

        // Update user info in localStorage
        const userInfo = this.getUserInfo();
        if (userInfo) {
          const updatedUserInfo = { ...userInfo, profilePicture: base64Image };
          this.saveUserInfo(updatedUserInfo);

          // If the doctor is the current doctor, update the current doctor object as well
          const currentDoctor = this.db.current_doctor();
          if (currentDoctor && currentDoctor.id) {
            // We don't need to check doctor.user_id here since we're updating by doctorId directly
            this.db.storage.setItem('CURRENT_DOCTOR', currentDoctor);
          }
        }

        // Return success response
        return of({
          message: 'Profile picture updated successfully',
          profilePicture: base64Image
        });
      })
    );
  }

  /**
   * Get doctor information
   * @param doctorId Doctor ID
   * @returns Observable<any>
   */
  getDoctor(doctorId: string): Observable<any> {
    // Find the doctor in the database
    const doctor = this.db.doctorTable().find(d => d.id === doctorId);
    if (!doctor) {
      return throwError(() => new Error('Doctor not found'));
    }

    // Find the user associated with the doctor
    const user = this.db.userTable().find(u => u.id === doctor.user_id);
    if (!user) {
      return throwError(() => new Error('User not found for doctor'));
    }

    // Return doctor information
    return of({
      id: doctor.id,
      firstName: user.firstname,
      lastName: user.lastname,
      email: user.email,
      bio: doctor.bio,
      specialization: doctor.specialisation,
      phoneNumber: doctor.contact,
      hospitalAffiliations: doctor.hospitalname,
      profilePicture: doctor.image
    });
  }

  /**
   * Clear user info from localStorage
   */
  clearUserInfo(): void {
    localStorage.removeItem(this.USER_INFO_KEY);
    localStorage.removeItem(this.TOKEN_KEY);
    // Update the BehaviorSubject
    this.userInfoSubject.next(null);
  }

  /**
   * Verify current password
   * @param password Current password
   * @returns Observable<any>
   */
  verifyCurrentPassword(password: string): Observable<any> {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.email) {
      return throwError(() => new Error('User not logged in'));
    }

    // Find the user in the database
    const user = this.db.userTable().find(u => u.email === userInfo.email);
    if (!user) {
      return throwError(() => new Error('User not found'));
    }

    // Verify password
    if (user.password === password) {
      return of({ verified: true });
    } else {
      return throwError(() => new Error('Current password is incorrect'));
    }
  }

  /**
   * Change password
   * @param currentPassword Current password
   * @param newPassword New password
   * @returns Observable<any>
   */
  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    const userInfo = this.getUserInfo();
    if (!userInfo || !userInfo.email) {
      return throwError(() => new Error('User not logged in'));
    }

    // Find the user in the database
    const user = this.db.userTable().find(u => u.email === userInfo.email);
    if (!user) {
      return throwError(() => new Error('User not found'));
    }

    // Verify current password
    if (user.password !== currentPassword) {
      return throwError(() => new Error('Current password is incorrect'));
    }

    // Update password
    const updatedUser = { ...user, password: newPassword };

    // Update user in database
    this.db.userTable.update(users =>
      users.map(u => u.id === user.id ? updatedUser : u)
    );

    // Save to localStorage
    this.db.storage.setItem('USER_TABLE', this.db.userTable());

    // Return success response
    return of({
      message: 'Password changed successfully'
    });
  }
}
