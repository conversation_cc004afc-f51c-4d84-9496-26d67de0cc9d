#!/bin/bash

# MedSecura Firebase Deployment Script
# This script deploys Firestore and Storage security rules to your Firebase project

echo "Deploying Firebase configuration for MedSecura..."
echo "Project ID: medsecura-5abb2"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null
then
    echo "Firebase CLI is not installed. Installing it now..."
    npm install -g firebase-tools
fi

# Login to Firebase if not already logged in
firebase login

# Initialize Firebase if not already initialized
if [ ! -f "firebase.json" ]; then
    echo "Initializing Firebase project..."
    firebase init --project medsecura-5abb2 firestore storage
fi

# Copy the rules files to their correct locations if they're not already there
if [ ! -f "firestore.rules" ]; then
    echo "Error: firestore.rules file not found in current directory"
    exit 1
fi

if [ ! -f "storage.rules" ]; then
    echo "Error: storage.rules file not found in current directory"
    exit 1
fi

# Update firebase.json to point to our rules files
cat > firebase.json << EOL
{
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  }
}
EOL

# Create empty indexes file if it doesn't exist
if [ ! -f "firestore.indexes.json" ]; then
    echo "Creating empty Firestore indexes file..."
    cat > firestore.indexes.json << EOL
{
  "indexes": [],
  "fieldOverrides": []
}
EOL
fi

# Deploy the rules
echo "Deploying Firestore and Storage rules..."
firebase deploy --only firestore:rules,storage

echo "Firebase deployment complete!"
echo "Your MedSecura application is now secured with proper Firebase rules." 