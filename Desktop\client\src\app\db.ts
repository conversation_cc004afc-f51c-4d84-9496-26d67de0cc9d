import { inject, Injectable, signal } from '@angular/core';
import {
  AppointmentType,
  AvailabilityType,
  DoctorPatient,
  DoctorType,
  MedicalRecordType,
  PatientType,
  UserType,
  AppointmentStatusType,
} from './type';
import {
  APPOINTMENT_TABLE,
  AVAILABILITY_TABLE,
  CURRENT_DOCTOR,
  CURRENT_PATIENT,
  DOCTOR_TABLE,
  DOCTORPATIENT_TABLE,
  MEDICALRECORD_TABLE,
  PATIENT_TABLE,
  USER_TABLE,
} from './constant';
import { Storage } from './storage';
import { Router } from '@angular/router';
import { FirebaseDbService } from './core/services/firebase-db.service';

@Injectable({
  providedIn: 'root',
})
export class Db {
  storage: Storage = inject(Storage);
  router: Router = inject(Router);
  private firebaseDb = inject(FirebaseDbService);

  // Delegate to Firebase service signals
  public userTable = this.firebaseDb.userTable;
  public doctorTable = this.firebaseDb.doctorTable;
  public patientTable = this.firebaseDb.patientTable;
  public avalaibityTable = this.firebaseDb.availabilityTable; // Keep typo for compatibility
  public doctorPatientTable = this.firebaseDb.doctorPatientTable;
  public availabilityTable = this.firebaseDb.availabilityTable;
  public medicalRecordTable = this.firebaseDb.medicalRecordTable;
  public appointmentTable = this.firebaseDb.appointmentTable;

  public current_doctor = this.firebaseDb.current_doctor;
  public current_patient = this.firebaseDb.current_patient;

  constructor() {
    // Firebase service handles initialization automatically
    console.log('Db service initialized with Firebase backend');
  }

  generateId(): string {
    return Date.now() + '';
  }

  // ======================================================> USER SECTION

  // login user
  login(email: string, password: string): UserType | null {
    const user: UserType | undefined = this.userTable().find(
      (val) => val.email == email
    );

    if (user && user.password == password) {
      return user;
    }

    return null;
  }

  // register user
  register(user: UserType) {
    console.log('Registering user via Firebase:', user);
    this.firebaseDb.register(user);
  }

  setCurrentDoctor(user: UserType) {
    this.firebaseDb.setCurrentDoctor(user);
  }

  setCurrentPatient(user: UserType) {
    this.firebaseDb.setCurrentPatient(user);
  }

  loadAllTable() {
    // Firebase service handles data loading automatically
    console.log('loadAllTable called - Firebase service handles data loading automatically');
  }

  // Initialize with sample data if tables are empty
  initializeWithSampleData() {
    // This method has been intentionally emptied
    // No sample data will be initialized
    console.log('Sample data initialization is disabled');
  }

  // add new doctor
  addNewDoctor(doctor: DoctorType) {
    this.firebaseDb.addDoctor(doctor);
  }

  // add new patient
  addNewPatient(patient: PatientType) {
    this.firebaseDb.addPatient(patient);
  }

  // add new doctor patient
  addDoctorPatient(data: DoctorPatient) {
    this.firebaseDb.addDoctorPatientRelation(data);
  }

  // add availability
  addAvailability(data: AvailabilityType) {
    this.firebaseDb.addAvailability(data);
  }

  // remove availability
  removeAvailability(id: string) {
    this.firebaseDb.deleteAvailability(id);
  }

  // update appointment status
  updateAppointmentStatus(id: string, status: AppointmentStatusType) {
    this.firebaseDb.updateAppointment(id, { status });
  }

  // add medical Record
  addMedicalRecord(data: MedicalRecordType) {
    this.firebaseDb.addMedicalRecord(data);
  }

  // add appointment
  addAppointment(data: AppointmentType) {
    this.firebaseDb.addAppointment(data);
  }

  // delete appointment
  deleteAppointment(id: string) {
    this.firebaseDb.deleteAppointment(id);
  }

  // remove doctor patient relationship
  removeDoctorPatient(doctorPatientId: string) {
    // Note: Firebase service doesn't have delete method for relations yet
    // This would need to be implemented in FirebaseDataService if needed
    console.log('removeDoctorPatient called - implement delete in FirebaseDataService if needed');
  }

  // Save all tables to localStorage - now delegates to Firebase
  saveToLocalStorage() {
    this.firebaseDb.saveToLocalStorage();
  }
}
