import { Injectable, NgZone } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, from, throwError, forkJoin } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  collectionData,
  Timestamp
} from '@angular/fire/firestore';
import { Storage, ref, uploadBytesResumable, getDownloadURL, deleteObject } from '@angular/fire/storage';
import { AuthService } from '../../../../core/services/auth.service';

export interface MedicalRecord {
  id?: string;
  patientId: string;
  doctorId: string;
  date: Date;
  diagnosis: string;
  treatment: string;
  prescription?: string;
  notes?: string;
  nextVisit?: Date;
  attachments?: string[]; // URLs to files in Firebase Storage
  doctorName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class MedicalRecordsService {
  private medicalRecordsCollection = 'medicalRecords';

  constructor(
    private http: HttpClient,
    private firestore: Firestore,
    private storage: Storage,
    private authService: AuthService,
    private ngZone: NgZone
  ) {}

  getMedicalRecords(patientId?: string): Observable<MedicalRecord[]> {
    return this.ngZone.runOutsideAngular(() => {
      // If no patientId is provided, try to get the current user's ID (if they are a patient)
      if (!patientId) {
        const currentUser = this.authService.getCurrentUser();
        if (currentUser?.role === 'patient' && currentUser.uid) {
          patientId = currentUser.uid;
        } else {
          return this.ngZone.run(() => {
            console.warn('No patient ID provided and current user is not a patient');
            return of([]);
          });
        }
      }

      const medicalRecordsRef = collection(this.firestore, this.medicalRecordsCollection);
      const recordsQuery = query(
        medicalRecordsRef,
        where('patientId', '==', patientId),
        orderBy('date', 'desc')
      );

      return collectionData(recordsQuery, { idField: 'id' }).pipe(
        map(records => {
          return this.ngZone.run(() => records.map(record => ({
            ...record,
            date: (record['date'] as Timestamp).toDate(),
            nextVisit: record['nextVisit'] ? (record['nextVisit'] as Timestamp).toDate() : undefined
          })) as MedicalRecord[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching medical records:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getMedicalRecordsByDoctor(doctorId: string): Observable<MedicalRecord[]> {
    return this.ngZone.runOutsideAngular(() => {
      const medicalRecordsRef = collection(this.firestore, this.medicalRecordsCollection);
      const recordsQuery = query(
        medicalRecordsRef,
        where('doctorId', '==', doctorId),
        orderBy('date', 'desc')
      );

      return collectionData(recordsQuery, { idField: 'id' }).pipe(
        map(records => {
          return this.ngZone.run(() => records.map(record => ({
            ...record,
            date: (record['date'] as Timestamp).toDate(),
            nextVisit: record['nextVisit'] ? (record['nextVisit'] as Timestamp).toDate() : undefined
          })) as MedicalRecord[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching medical records by doctor:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getMedicalRecordById(id: string): Observable<MedicalRecord> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, `${this.medicalRecordsCollection}/${id}`);
      return from(getDoc(recordDocRef)).pipe(
        map(docSnap => {
          return this.ngZone.run(() => {
            if (docSnap.exists()) {
              const data = docSnap.data();
              return {
                id: docSnap.id,
                ...data,
                date: (data['date'] as Timestamp).toDate(),
                nextVisit: data['nextVisit'] ? (data['nextVisit'] as Timestamp).toDate() : undefined
              } as MedicalRecord;
            } else {
              throw new Error('Medical record not found');
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  addMedicalRecord(record: Omit<MedicalRecord, 'id'>, files?: File[]): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      // First, upload any attached files
      if (files && files.length > 0) {
        return this.uploadFiles(files, record.patientId, record.doctorId).pipe(
          switchMap(fileUrls => {
            const newRecord = {
              ...record,
              attachments: fileUrls
            };
            return this.saveRecord(newRecord);
          })
        );
      } else {
        return this.saveRecord(record);
      }
    });
  }

  private saveRecord(record: Omit<MedicalRecord, 'id'>): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      const medicalRecordsRef = collection(this.firestore, this.medicalRecordsCollection);
      return from(addDoc(medicalRecordsRef, record)).pipe(
        map(docRef => {
          return this.ngZone.run(() => docRef.id);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error adding medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  updateMedicalRecord(id: string, record: Partial<MedicalRecord>, newFiles?: File[]): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, `${this.medicalRecordsCollection}/${id}`);

      // If there are new files to upload and we have patientId and doctorId
      if (newFiles && newFiles.length > 0) {
        // First get the full record to get patientId and doctorId if not provided
        return this.getMedicalRecordById(id).pipe(
          switchMap(existingRecord => {
            return this.ngZone.run(() => {
              const patientId = record.patientId || existingRecord.patientId;
              const doctorId = record.doctorId || existingRecord.doctorId;

              return this.uploadFiles(newFiles, patientId, doctorId).pipe(
                switchMap(newFileUrls => {
                  const existingAttachments = existingRecord.attachments || [];
                  const updatedRecord = {
                    ...record,
                    attachments: [...existingAttachments, ...newFileUrls]
                  };
                  return this.ngZone.runOutsideAngular(() => {
                    return from(updateDoc(recordDocRef, updatedRecord));
                  });
                })
              );
            });
          }),
          catchError(error => {
            return this.ngZone.run(() => {
              console.error('Error updating medical record with new files:', error);
              return throwError(() => error);
            });
          })
        );
      } else {
        // Just update the record without changing attachments
        return from(updateDoc(recordDocRef, record)).pipe(
          catchError(error => {
            return this.ngZone.run(() => {
              console.error('Error updating medical record:', error);
              return throwError(() => error);
            });
          })
        );
      }
    });
  }

  deleteMedicalRecord(id: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      // First get the record to check for attachments
      return this.getMedicalRecordById(id).pipe(
        switchMap(record => {
          return this.ngZone.run(() => {
            const recordDocRef = doc(this.firestore, `${this.medicalRecordsCollection}/${id}`);

            // If there are attachments, delete them first
            if (record.attachments && record.attachments.length > 0) {
              const deletePromises = record.attachments.map(url => {
                // Extract the path from the URL to create a reference
                return this.ngZone.runOutsideAngular(() => {
                  const storageRef = ref(this.storage, url);
                  return deleteObject(storageRef);
                });
              });

              return from(Promise.all(deletePromises)).pipe(
                switchMap(() => this.ngZone.runOutsideAngular(() => {
                  return from(deleteDoc(recordDocRef));
                }))
              );
            } else {
              // No attachments, just delete the document
              return this.ngZone.runOutsideAngular(() => {
                return from(deleteDoc(recordDocRef));
              });
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error deleting medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  // Upload files to Firebase Storage
  private uploadFiles(files: File[], patientId: string, doctorId: string): Observable<string[]> {
    return this.ngZone.runOutsideAngular(() => {
      if (!files || files.length === 0) {
        return of([]);
      }

      const uploads = files.map(file => this.uploadFile(file, patientId, doctorId));
      return forkJoin(uploads);
    });
  }

  private uploadFile(file: File, patientId: string, doctorId: string): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      const filePath = `medical-records/${Date.now()}_${file.name}`;
      const storageRef = ref(this.storage, filePath);

      // Add metadata to comply with security rules
      const metadata = {
        customMetadata: {
          patientId: patientId,
          doctorId: doctorId
        }
      };

      const uploadTask = uploadBytesResumable(storageRef, file, metadata);

      return new Observable<string>(observer => {
        uploadTask.on('state_changed',
          (snapshot) => {
            // Optional: Track upload progress
            this.ngZone.run(() => {
              const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              console.log('Upload is ' + progress + '% done');
            });
          },
          (error) => {
            // Handle unsuccessful uploads
            this.ngZone.run(() => {
              console.error('Upload failed:', error);
              observer.error(error);
            });
          },
          () => {
            // Handle successful uploads on complete
            getDownloadURL(uploadTask.snapshot.ref).then(downloadURL => {
              this.ngZone.run(() => {
                observer.next(downloadURL);
                observer.complete();
              });
            }).catch(error => {
              this.ngZone.run(() => {
                observer.error(error);
              });
            });
          }
        );
      });
    });
  }

  // The HTTP methods don't need NgZone as they're already handled by Angular's HTTP client
  downloadAttachment(url: string): Observable<Blob> {
    return this.http.get(url, { responseType: 'blob' }).pipe(
      catchError(error => {
        console.error('Error downloading attachment:', error);
        return throwError(() => error);
      })
    );
  }

  downloadRecord(recordId: string): Observable<Blob> {
    return this.getMedicalRecordById(recordId).pipe(
      switchMap(record => {
        // If the record has attachments, download the first one
        if (record.attachments && record.attachments.length > 0) {
          return this.downloadAttachment(record.attachments[0]);
        } else {
          // If no attachments, create a simple text representation of the record
          const recordText = `
            Medical Record ID: ${record.id}
            Patient ID: ${record.patientId}
            Doctor: ${record.doctorName || record.doctorId}
            Date: ${record.date.toLocaleDateString()}
            Diagnosis: ${record.diagnosis}
            Treatment: ${record.treatment}
            Prescription: ${record.prescription || 'None'}
            Notes: ${record.notes || 'None'}
            Next Visit: ${record.nextVisit ? record.nextVisit.toLocaleDateString() : 'Not scheduled'}
          `;

          const blob = new Blob([recordText], { type: 'text/plain' });
          return of(blob);
        }
      }),
      catchError(error => {
        console.error('Error downloading medical record:', error);
        return throwError(() => error);
      })
    );
  }
}
