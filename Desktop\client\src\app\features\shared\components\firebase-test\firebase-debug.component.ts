import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  Firestore,
  collection,
  addDoc,
  getDocs,
  query,
  where,
  collectionData
} from '@angular/fire/firestore';

@Component({
  selector: 'app-firebase-debug',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="debug-container">
      <h2>Firebase Debug Tool</h2>

      <div class="test-section">
        <h3>Test Connection</h3>
        <button (click)="testFirebaseConnection()">Test Connection</button>
        <div *ngIf="connectionStatus" [class]="connectionStatus === 'Connected' ? 'success' : 'error'">
          Status: {{ connectionStatus }}
        </div>
      </div>

      <div class="test-section">
        <h3>Add Test Document</h3>
        <div class="form-group">
          <label>Collection Name</label>
          <input type="text" [(ngModel)]="testCollection" class="form-control">
        </div>
        <button (click)="addTestDocument()">Add Test Document</button>
        <div *ngIf="addResult" [class]="addResult.success ? 'success' : 'error'">
          {{ addResult.message }}
        </div>
      </div>

      <div class="test-section">
        <h3>Read Test Documents</h3>
        <div class="form-group">
          <label>Collection Name</label>
          <input type="text" [(ngModel)]="readCollection" class="form-control">
        </div>
        <button (click)="readTestDocuments()">Read Documents</button>
        <div *ngIf="readResult" [class]="readResult.success ? 'success' : 'error'">
          {{ readResult.message }}
        </div>
        <div *ngIf="documents.length > 0">
          <h4>Found {{ documents.length }} documents:</h4>
          <pre>{{ documents | json }}</pre>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .debug-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
    }

    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 10px;
    }

    .success {
      color: #4CAF50;
      margin-top: 10px;
      padding: 10px;
      background-color: #E8F5E9;
      border-radius: 4px;
    }

    .error {
      color: #F44336;
      margin-top: 10px;
      padding: 10px;
      background-color: #FFEBEE;
      border-radius: 4px;
    }

    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      max-height: 300px;
      overflow: auto;
    }
  `]
})
export class FirebaseDebugComponent implements OnInit {
  connectionStatus = '';
  testCollection = 'test_collection';
  readCollection = 'doctorAvailability';
  addResult: { success: boolean, message: string } | null = null;
  readResult: { success: boolean, message: string } | null = null;
  documents: any[] = [];

  constructor(private firestore: Firestore) {}

  ngOnInit() {
    console.log('Firebase Debug Component initialized');
    console.log('Firestore instance:', !!this.firestore ? 'Available' : 'Not available');
  }

  testFirebaseConnection() {
    try {
      // Create a simple test to check if Firestore is accessible
      const testCol = collection(this.firestore, 'connection_test');
      console.log('Collection reference created successfully');
      this.connectionStatus = 'Connected';
    } catch (error) {
      console.error('Error connecting to Firebase:', error);
      this.connectionStatus = 'Connection Failed';
    }
  }

  addTestDocument() {
    try {
      const testCol = collection(this.firestore, this.testCollection);
      const testData = {
        test: true,
        createdAt: new Date().toISOString(),
        message: 'Test document',
      };

      addDoc(testCol, testData)
        .then(docRef => {
          console.log('Document added with ID:', docRef.id);
          this.addResult = {
            success: true,
            message: `Test document added successfully with ID: ${docRef.id}`
          };
        })
        .catch(error => {
          console.error('Error adding document:', error);
          this.addResult = {
            success: false,
            message: `Error adding document: ${error.message}`
          };
        });
    } catch (error: any) {
      console.error('Exception when trying to add document:', error);
      this.addResult = {
        success: false,
        message: `Exception: ${error.message}`
      };
    }
  }

  readTestDocuments() {
    try {
      const col = collection(this.firestore, this.readCollection);

      getDocs(col)
        .then(snapshot => {
          this.documents = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          }));

          this.readResult = {
            success: true,
            message: `Found ${this.documents.length} documents in ${this.readCollection}`
          };
        })
        .catch(error => {
          console.error('Error reading documents:', error);
          this.readResult = {
            success: false,
            message: `Error reading documents: ${error.message}`
          };
        });
    } catch (error: any) {
      console.error('Exception when trying to read documents:', error);
      this.readResult = {
        success: false,
        message: `Exception: ${error.message}`
      };
    }
  }
}
