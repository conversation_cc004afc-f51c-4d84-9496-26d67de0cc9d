import { Injectable, NgZone } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, from, throwError, of } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { AuthService, User } from '../../../core/services/auth.service';
import { catchError, map, switchMap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  getDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  collectionData
} from '@angular/fire/firestore';

export interface Doctor extends User {
  specialization?: string;
  affiliatedInstitution?: string;
  hospitalAffiliations?: string[] | string;
  qualifications?: string[] | string;
  educationDetails?: string[] | string;
  services?: string[] | string;
  certifications?: string[] | string;
  bio?: string;
  name?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DoctorService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private firestore: Firestore,
    private ngZone: Ng<PERSON>one
  ) {}

  addDoctor(doctor: { name: string, email: string, password: string, specialization?: string, affiliatedInstitution?: string }): Observable<any> {
    // Split the name into first and last name
    const names = doctor.name.split(' ');
    const firstName = names[0];
    const lastName = names.slice(1).join(' ');

    // Transform Doctor to RegisterRequest format
    const registerRequest = {
      firstName: firstName,
      lastName: lastName,
      email: doctor.email,
      password: doctor.password,
      role: 'doctor' as const
    };

    // Register the doctor through the auth service, then update with additional fields
    return this.authService.register(registerRequest).pipe(
      switchMap(registeredUser => {
        if (!registeredUser.uid) {
          return throwError(() => new Error('Failed to get user ID after registration'));
        }

        return this.authService.updateUserProfile(registeredUser.uid, {
          specialization: doctor.specialization,
          affiliatedInstitution: doctor.affiliatedInstitution
        });
      })
    );
  }

  updateDoctor(uid: string, doctor: Partial<Doctor>): Observable<void> {
    return this.authService.updateUserProfile(uid, doctor);
  }

  getDoctors(): Observable<Doctor[]> {
    return this.ngZone.runOutsideAngular(() => {
      const usersRef = collection(this.firestore, 'users');
      const doctorsQuery = query(
        usersRef,
        where('role', '==', 'doctor'),
        orderBy('lastName')
      );

      return collectionData(doctorsQuery, { idField: 'uid' }).pipe(
        map(doctors => {
          return this.ngZone.run(() => doctors as Doctor[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching doctors:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getDoctorById(uid: string): Observable<Doctor> {
    return this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, `users/${uid}`);
      return from(getDoc(userDocRef)).pipe(
        map(docSnap => {
          return this.ngZone.run(() => {
            if (docSnap.exists() && docSnap.data()['role'] === 'doctor') {
              return { uid, ...docSnap.data() } as Doctor;
            } else {
              throw new Error('Doctor not found');
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching doctor from Firestore:', error);

            // TEMPORARY FALLBACK: Try to get the doctor info from localStorage
            const userInfoJson = localStorage.getItem('user');
            if (userInfoJson) {
              try {
                const userInfo = JSON.parse(userInfoJson);
                if (userInfo.role === 'doctor' && (userInfo.uid === uid || userInfo.id === uid || userInfo.doctorId === uid)) {
                  console.log('Using localStorage fallback for doctor info:', userInfo);
                  return of({
                    uid: userInfo.uid || userInfo.id || userInfo.doctorId,
                    email: userInfo.email || '',
                    role: 'doctor',
                    firstName: userInfo.firstName || (userInfo.name ? userInfo.name.split(' ')[0] : ''),
                    lastName: userInfo.lastName || (userInfo.name ? userInfo.name.split(' ').slice(1).join(' ') : ''),
                    specialization: userInfo.specialization || '',
                    phoneNumber: userInfo.phoneNumber || '',
                    bio: userInfo.bio || '',
                    name: userInfo.name || `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim()
                  } as Doctor);
                }
              } catch (parseError) {
                console.error('Error parsing user info from localStorage:', parseError);
              }
            }

            return throwError(() => error);
          });
        })
      );
    });
  }

  deleteDoctor(uid: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      // Note: This only deletes the Firestore document, not the Authentication account
      // A complete solution would need to delete the Auth account as well
      const userDocRef = doc(this.firestore, `users/${uid}`);
      return from(deleteDoc(userDocRef)).pipe(
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error deleting doctor:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }
}
