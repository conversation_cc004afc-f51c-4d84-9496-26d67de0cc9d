.dashboard-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  background: var(--bg-light);
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.form-card {
  background: var(--bg-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: 32px;
}

.form-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 32px 0;
}

.staff-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.role-selector {
  display: flex;
  gap: 16px;
}

.role-option {
  flex: 1;
}

.role-option input[type="radio"] {
  display: none;
}

.role-label {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  color: var(--text-primary);
  font-size: 14px;
  font-weight: 500;
}

.role-option input[type="radio"]:checked + .role-label {
  border-color: var(--primary-color);
  background-color: rgba(25, 154, 142, 0.1);
  color: var(--primary-color);
}

.role-option input[type="radio"]:checked + .role-label i {
  color: var(--primary-color);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-start;
}

.input-wrapper i {
  position: absolute;
  left: 12px;
  top: 12px;
  color: var(--text-secondary);
  font-size: 16px;
}

.input-wrapper input,
.input-wrapper textarea {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  font-size: 14px;
  color: var(--text-primary);
  background: var(--bg-white);
  transition: all 0.2s ease;
}

.input-wrapper textarea {
  resize: vertical;
  min-height: 60px;
}

.input-wrapper input:focus,
.input-wrapper textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(25, 154, 142, 0.1);
}

.error-message {
  color: #DC2626;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}

.submit-btn,
.cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: var(--border-radius-md);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-btn {
  background: var(--primary-color);
  color: white;
  border: none;
}

.submit-btn:hover:not(:disabled) {
  background: var(--primary-color-80);
  transform: translateY(-1px);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancel-btn {
  background: white;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.cancel-btn:hover {
  background: var(--bg-light);
  border-color: var(--text-secondary);
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }

  .form-card {
    padding: 24px;
  }

  .role-selector {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .submit-btn,
  .cancel-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px;
  }

  .form-card {
    padding: 16px;
  }

  .form-title {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .staff-form {
    gap: 20px;
  }
}