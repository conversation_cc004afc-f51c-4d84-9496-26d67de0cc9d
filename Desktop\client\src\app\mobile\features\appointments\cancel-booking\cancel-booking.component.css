.container {
  /* max-width: 600px; */
  overflow-y: hidden;
  margin: 0 auto;
  padding: 20px;
  padding-bottom: 80px; 
  font-family: system-ui, -apple-system, sans-serif;
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 32px; 
}

  .back-button {
    background: none;
    border: none;
    font-size: 24px;
    padding: 8px;
    cursor: pointer;
    color:#0e8177;
  }

  h1 {
    margin: 0;
    margin-left: 78px;
    margin-bottom: 15px;
    margin-top: 15px;
    font-size: 24px;
    font-weight: 600;
    color:black;
  }

  .confirmation-text {
    color: #333;
    margin-bottom: 32px;
    width : 100%;
    margin-left: 25px;
  }

  .reasons-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 32px;
    margin-left: 25px;
  }

  .reason-item {
    position: relative;
  }

  .reason-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .reason-item input[type="radio"] {
    position: absolute;
    opacity: 0;
  }

  .radio-custom {
    width: 24px;
    height: 24px;
    border: 2px solid #11998e;
    border-radius: 50%;
    margin-right: 12px;
    position: relative;
  }

  .reason-item input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background-color: #11998e;
    border-radius: 50%;
  }

  .reason-text {
    font-size: 16px;
    color: #333;
  }

  .reason-textarea {
    width: calc(100% - 32px); 
    min-height: 150px; 
    padding: 16px;
    border: 1.5px solid #e0e0e0; 
    border-radius: 16px; 
    margin: 8px 0 40px; 
    font-family: inherit;
    font-size: 16px;
    resize: none;
    color: #333;
    background-color: #fff;
  }

  .reason-textarea::placeholder {
    color: #999;
  }

  .reason-textarea:focus {
    outline: none;
    border-color: #11998e;
  }

  .cancel-button {
    width: 100%;
    padding: 16px;
    background-color: #11998e;
    color: white;
    border: none;
    border-radius: 30px; 
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-top: 40px; 
    margin-bottom: 20px; 
  }

  .cancel-button:hover {
    background-color: #0e8177;
  }

 
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  @keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
  }
  
  
  @media (max-width: 375px) {
    .profile-container {
        padding: 15px;
    }
    
    .menu-item {
        padding: 12px;
    }
    
    .nav-button {
        padding: 6px 12px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 16px;
    }

    .reason-textarea {
      width: calc(100% - 32px);
      min-height: 120px;
    }
  }