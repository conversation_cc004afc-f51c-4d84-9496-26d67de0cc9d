{"name": "med-secura", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.0", "@angular/cdk": "^19.1.3", "@angular/common": "^19.1.0", "@angular/compiler": "^19.1.0", "@angular/core": "^19.1.0", "@angular/fire": "^19.0.0", "@angular/forms": "^19.1.0", "@angular/material": "^19.1.3", "@angular/platform-browser": "^19.1.0", "@angular/platform-browser-dynamic": "^19.1.0", "@angular/router": "^19.1.0", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "firebase": "^11.6.0", "rxjs": "~7.8.0", "tailwindcss": "^3.4.17", "tslib": "^2.3.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.7", "@angular/cli": "^19.1.4", "@angular/compiler-cli": "^19.1.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "^5.6.3"}}