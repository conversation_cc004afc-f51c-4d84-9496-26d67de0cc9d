# MedSecura Project Structure

## Overview
MedSecura is a comprehensive medical facility management system built with Angular frontend and a planned backend architecture. The system provides separate interfaces for doctors, receptionists, and administrators, with shared components for common functionality.

## Project Architecture

### Frontend (Angular)
```
frontend/
├── src/
│   ├── app/                    # Main application code
│   │   ├── core/              # Core functionality
│   │   │   ├── services/      # Global services
│   │   │   │   ├── auth.service.ts     # Authentication service
│   │   │   │   ├── user.service.ts     # User management
│   │   │   │   └── api.service.ts      # API communication
│   │   │   ├── guards/        # Route guards
│   │   │   │   ├── auth.guard.ts       # Authentication guard
│   │   │   │   └── role.guard.ts       # Role-based access
│   │   │   └── interceptors/  # HTTP interceptors
│   │   │       └── auth.interceptor.ts # Token handling
│   │   │
│   │   ├── features/          # Feature modules
│   │   │   ├── auth/          # Authentication module
│   │   │   │   ├── login/     # Login component
│   │   │   │   ├── register/  # Registration
│   │   │   │   └── auth.module.ts
│   │   │   │
│   │   │   ├── doctor/        # Doctor module
│   │   │   │   ├── appointments/   # Appointment management
│   │   │   │   ├── patients/       # Patient records
│   │   │   │   ├── prescriptions/  # Prescription handling
│   │   │   │   └── doctor.module.ts
│   │   │   │
│   │   │   ├── receptionist/  # Receptionist module
│   │   │   │   ├── scheduling/     # Appointment scheduling
│   │   │   │   ├── patients/       # Patient management
│   │   │   │   ├── billing/        # Billing operations
│   │   │   │   └── receptionist.module.ts
│   │   │   │
│   │   │   └── shared/        # Shared components
│   │   │       ├── components/
│   │   │       │   ├── about/      # About page
│   │   │       │   ├── benefits/   # Benefits section
│   │   │       │   ├── dashboard/  # Dashboard layout
│   │   │       │   └── landing/    # Landing page
│   │   │       ├── guards/     # Shared guards
│   │   │       ├── services/   # Shared services
│   │   │       └── shared.module.ts
│   │   │
│   │   ├── app.component.ts   # Root component
│   │   ├── app.component.html # Root template
│   │   ├── app.component.css  # Root styles
│   │   ├── app.config.ts      # App configuration
│   │   └── app.routes.ts      # Main routing
│   │
│   ├── assets/                # Static files
│   │   ├── images/           # Image assets
│   │   ├── icons/            # Icon assets
│   │   └── styles/           # Global styles
│   │
│   └── environments/          # Environment configs
│       ├── environment.ts     # Development
│       └── environment.prod.ts # Production
│
├── node_modules/              # Dependencies
├── .angular/                  # Angular cache
├── .vscode/                   # Editor config
├── package.json              # Project dependencies
├── angular.json              # Angular workspace config
├── tsconfig.json             # TypeScript config
└── README.md                 # Frontend documentation
```

### Backend (Planned)
```
backend/
├── src/
│   ├── controllers/          # API Controllers
│   ├── models/              # Data Models
│   ├── routes/              # API Routes
│   ├── services/            # Business Logic
│   ├── middleware/          # Custom Middleware
│   └── utils/               # Utility Functions
├── config/                  # Configuration Files
└── tests/                   # Test Files
```

## Feature Modules

### Authentication Module
- User registration and login
- Password recovery
- Session management
- JWT token handling

### Doctor Module
- Patient appointment management
- Medical records management
- Prescription handling
- Patient history viewing
- Schedule management

### Receptionist Module
- Appointment scheduling
- Patient registration
- Billing management
- Queue management
- Report generation

### Shared Module
- Common UI components
- Utility services
- Guards and interceptors
- Reusable forms and validators

## Technology Stack

### Frontend
- **Framework**: Angular (Latest version)
- **Language**: TypeScript
- **State Management**: NgRx (planned)
- **UI Components**: Angular Material
- **Styling**: SCSS
- **Package Manager**: npm
- **Development Tools**: VS Code
- **Testing**: Jasmine & Karma

### Backend (Planned)
- **Framework**: Node.js with Express
- **Database**: MongoDB
- **Authentication**: JWT
- **API Documentation**: Swagger
- **Testing**: Jest

## Development Setup

1. **Prerequisites**
   - Node.js (v14 or higher)
   - npm (v6 or higher)
   - Angular CLI (latest version)
   ```bash
   npm install -g @angular/cli
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   ng serve
   ```

3. **Development Server**
   - Frontend: `http://localhost:4200`
   - API (planned): `http://localhost:3000`

4. **Building for Production**
   ```bash
   ng build --configuration production
   ```

## Project Standards

### Code Organization
- Feature-based module structure
- Lazy loading for all feature modules
- Shared components in shared module
- Core services in core module

### Naming Conventions
- Components: `feature-name.component.ts`
- Services: `feature-name.service.ts`
- Modules: `feature-name.module.ts`
- Interfaces: `i-feature-name.ts`
- Models: `feature-name.model.ts`
- Guards: `feature-name.guard.ts`

### Coding Standards
- Follow Angular style guide
- Use TypeScript strict mode
- Implement proper error handling
- Write unit tests for components
- Document public methods and interfaces

### Git Workflow
1. Create feature branch from development
2. Follow commit message conventions
3. Submit PR for review
4. Merge after approval

## Security Measures
- JWT-based authentication
- Role-based access control
- HTTP-only cookies
- XSS protection
- CSRF protection
- Input validation
- Secure headers

## Contributing
1. Follow the existing code structure
2. Maintain component isolation
3. Write clean, documented code
4. Include unit tests
5. Follow Angular best practices
6. Submit detailed PR descriptions

## Documentation
- Component documentation
- API documentation (planned)
- User guides
- Developer guides
- Deployment guides

---
 2025 MedSecura. All rights reserved.
