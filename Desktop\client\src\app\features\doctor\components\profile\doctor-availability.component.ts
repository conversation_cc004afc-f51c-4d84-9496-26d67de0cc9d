import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DoctorAvailabilityService } from '../../services/doctor-availability.service';
import { AvailabilityType } from '../../../../type';
import { AuthService, User } from '../../../../core/services/auth.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-doctor-availability',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="availability-container">
      <h2 class="section-title">Set Your Availability</h2>

      <div class="availability-form">
        <div class="form-group">
          <label for="date">Date</label>
          <input type="date" id="date" [(ngModel)]="newAvailability.date" class="form-control">
        </div>

        <div class="form-group">
          <label for="startTime">Start Time</label>
          <input type="time" id="startTime" [(ngModel)]="newAvailability.start_time" class="form-control">
        </div>

        <div class="form-group">
          <label for="endTime">End Time</label>
          <input type="time" id="endTime" [(ngModel)]="newAvailability.end_time" class="form-control">
        </div>

        <div class="action-buttons">
          <button class="add-availability" (click)="addAvailability()">Add Availability</button>
        </div>
      </div>

      <div class="availability-list">
        <h3>Your Availabilities</h3>
        <p *ngIf="availabilities.length === 0">No availability slots set yet.</p>

        <div class="availability-item" *ngFor="let availability of availabilities">
          <div class="availability-details">
            <div class="availability-date">{{ formatDate(availability.date) }}</div>
            <div class="availability-time">{{ formatTimeRange(availability.start_time, availability.end_time) }}</div>
          </div>
          <button class="remove-button" (click)="removeAvailability(availability.id)">
            <i class="bi bi-trash"></i>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .availability-container {
      background: #fff;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      margin-bottom: 20px;
    }

    .section-title {
      font-size: 1.5rem;
      margin-bottom: 20px;
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }

    .availability-form {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #555;
    }

    .form-control {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 5px;
      font-size: 14px;
    }

    .action-buttons {
      grid-column: 1 / -1;
      display: flex;
      justify-content: flex-end;
    }

    .add-availability {
      background-color: #4caf50;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 5px;
      cursor: pointer;
      font-weight: 500;
      transition: background-color 0.3s;
    }

    .add-availability:hover {
      background-color: #45a049;
    }

    .availability-list {
      margin-top: 30px;
    }

    .availability-list h3 {
      font-size: 1.2rem;
      margin-bottom: 15px;
      color: #444;
    }

    .availability-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      background-color: #f9f9f9;
      border-radius: 5px;
      margin-bottom: 10px;
      border-left: 3px solid #4caf50;
    }

    .availability-details {
      display: flex;
      gap: 15px;
    }

    .availability-date {
      font-weight: 500;
    }

    .availability-time {
      color: #666;
    }

    .remove-button {
      background: none;
      border: none;
      color: #ff5252;
      cursor: pointer;
      font-size: 1.2rem;
      transition: color 0.3s;
    }

    .remove-button:hover {
      color: #ff0000;
    }
  `]
})
export class DoctorAvailabilityComponent implements OnInit {
  availabilities: AvailabilityType[] = [];
  newAvailability: Omit<AvailabilityType, 'id'> = {
    doctor_id: '',
    date: '',
    start_time: '',
    end_time: ''
  };

  userId: string = '';

  constructor(
    private availabilityService: DoctorAvailabilityService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Get the current user from auth service
    const currentUser = this.authService.getCurrentUser();

    if (currentUser && currentUser.uid) {
      this.userId = currentUser.uid;
      this.newAvailability.doctor_id = this.userId;
      this.loadAvailabilities();
    } else {
      // If no user found in auth service, try localStorage
      const userJson = localStorage.getItem('user');
      if (userJson) {
        try {
          const user = JSON.parse(userJson) as User;
          if (user && user.uid) {
            this.userId = user.uid;
            this.newAvailability.doctor_id = this.userId;
            this.loadAvailabilities();
          } else {
            console.error('User found in localStorage but no uid');
            this.redirectToLogin();
          }
        } catch (error) {
          console.error('Error parsing user from localStorage:', error);
          this.redirectToLogin();
        }
      } else {
        console.error('No user found in localStorage');
        this.redirectToLogin();
      }
    }
  }

  private redirectToLogin() {
    alert('Please log in to access this page');
    this.router.navigate(['/login']);
  }

  loadAvailabilities() {
    console.log('Loading availabilities for doctor ID:', this.userId);

    this.availabilityService.getAvailabilitiesByDoctorId(this.userId).subscribe({
      next: (availabilities) => {
        console.log('Successfully loaded availabilities from Firebase:', availabilities);
        this.availabilities = availabilities;
      },
      error: (error) => {
        console.error('Error loading availabilities from Firebase:', error);
        alert('Failed to load availabilities. Error: ' + error.message);
      }
    });
  }

  addAvailability() {
    if (!this.isValidAvailability()) {
      alert('Please fill in all fields correctly');
      return;
    }

    console.log('Adding availability with data:', this.newAvailability);
    console.log('Using doctor ID:', this.userId);

    // Double-check that doctor_id is set
    if (!this.newAvailability.doctor_id) {
      console.warn('doctor_id was not set, setting it now to', this.userId);
      this.newAvailability.doctor_id = this.userId;
    }

    this.availabilityService.addAvailability(this.newAvailability).subscribe({
      next: (newAvailability) => {
        console.log('Successfully added availability to Firebase:', newAvailability);
        this.availabilities.push(newAvailability);
        this.resetForm();
        alert('Availability added successfully!');
      },
      error: (error) => {
        console.error('Error adding availability to Firebase:', error);
        alert('Failed to add availability. Please try again. Error: ' + error.message);
      }
    });
  }

  removeAvailability(id: string) {
    console.log('Removing availability with ID:', id);

    this.availabilityService.removeAvailability(id).subscribe({
      next: () => {
        console.log('Successfully removed availability from Firebase');
        this.availabilities = this.availabilities.filter(a => a.id !== id);
      },
      error: (error) => {
        console.error('Error removing availability from Firebase:', error);
        alert('Failed to remove availability. Error: ' + error.message);
      }
    });
  }

  resetForm() {
    this.newAvailability = {
      doctor_id: this.userId,
      date: '',
      start_time: '',
      end_time: ''
    };
  }

  isValidAvailability(): boolean {
    return !!(
      this.newAvailability.date &&
      this.newAvailability.start_time &&
      this.newAvailability.end_time &&
      this.newAvailability.start_time < this.newAvailability.end_time
    );
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' });
  }

  formatTimeRange(start: string, end: string): string {
    return `${this.formatTime(start)} - ${this.formatTime(end)}`;
  }

  formatTime(timeString: string): string {
    const [hours, minutes] = timeString.split(':');
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minutes} ${ampm}`;
  }
}
