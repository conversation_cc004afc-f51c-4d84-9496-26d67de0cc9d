import { Injectable, NgZone, inject } from '@angular/core';
import { Observable, from, of, throwError, BehaviorSubject } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  addDoc,
  updateDoc,
  deleteDoc,
  Timestamp,
  collectionData,
  writeBatch
} from '@angular/fire/firestore';
import { AvailabilityType, AppointmentType, AppointmentStatusType } from '../../../type';
import { Db } from '../../../db';

/**
 * Service for managing doctor availability and appointments in Firebase
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseAppointmentService {
  private firestore = inject(Firestore);
  private ngZone = inject(NgZone);
  private db = inject(Db);

  // Collection names
  private readonly availabilityCollection = 'doctorAvailability';
  private readonly appointmentsCollection = 'appointments';

  // BehaviorSubjects for state management
  private availabilitySubject = new BehaviorSubject<AvailabilityType[]>([]);
  private appointmentsSubject = new BehaviorSubject<AppointmentType[]>([]);

  // Observables for components to subscribe to
  public availability$ = this.availabilitySubject.asObservable();
  public appointments$ = this.appointmentsSubject.asObservable();

  constructor() { }

  // ==================== AVAILABILITY METHODS ====================

  /**
   * Load availability slots for a doctor from Firebase
   */
  loadDoctorAvailability(doctorId: string): Observable<AvailabilityType[]> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Loading availability for doctor ${doctorId} from Firebase with collection ${this.availabilityCollection}`);

      const availabilityRef = collection(this.firestore, this.availabilityCollection);

      // Use a simpler query that doesn't require a composite index
      // We'll just query by doctor_id and sort the results in memory
      const availabilityQuery = query(
        availabilityRef,
        where('doctor_id', '==', doctorId)
      );

      return from(getDocs(availabilityQuery)).pipe(
        map(querySnapshot => {
          return this.ngZone.run(() => {
            const availability: AvailabilityType[] = [];

            querySnapshot.forEach(doc => {
              const data = doc.data();
              availability.push({
                id: doc.id,
                doctor_id: data['doctor_id'] || '',
                date: data['date'] || '',
                start_time: data['start_time'] || '',
                end_time: data['end_time'] || ''
              });
            });

            // Sort in memory since we're not using the Firebase orderBy
            availability.sort((a, b) => {
              // First sort by date
              const dateCompare = new Date(a.date).getTime() - new Date(b.date).getTime();
              if (dateCompare !== 0) return dateCompare;

              // Then by start time
              return a.start_time.localeCompare(b.start_time);
            });

            console.log(`Loaded ${availability.length} availability slots from Firebase`);
            this.availabilitySubject.next(availability);
            return availability;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error loading doctor availability from Firebase:', error);
            // Instead of falling back to local storage, we'll return an empty array
            // This ensures we're only using Firebase data
            this.availabilitySubject.next([]);
            return of([]);
          });
        })
      );
    });
  }

  /**
   * Debug method to help troubleshoot Firebase availability issues
   */
  private debugAvailabilityCollection(doctorId: string): void {
    console.log('Attempting to debug availability collection');

    // First try to get all documents from the collection to see if it exists
    const availabilityRef = collection(this.firestore, this.availabilityCollection);

    getDocs(availabilityRef).then(snapshot => {
      console.log(`Total documents in ${this.availabilityCollection} collection:`, snapshot.size);

      if (snapshot.size > 0) {
        console.log('Sample of availability data:');
        let count = 0;
        snapshot.forEach(doc => {
          if (count < 3) { // Show up to 3 samples
            console.log(`Document ID: ${doc.id}, Data:`, doc.data());
          }
          count++;
        });

        // Check if there are any slots for the specific doctor
        const docCount = snapshot.docs.filter(doc =>
          doc.data()['doctor_id'] === doctorId
        ).length;

        console.log(`Documents with doctor_id=${doctorId}:`, docCount);

        if (docCount === 0) {
          console.warn(`No availability slots found for doctor ${doctorId}. Check if doctor_id is correct.`);
        }
      } else {
        console.warn(`The ${this.availabilityCollection} collection exists but is empty.`);
      }
    }).catch(error => {
      console.error(`Error accessing ${this.availabilityCollection} collection:`, error);
    });
  }

  /**
   * Add a new availability slot to Firebase
   */
  addAvailability(availability: Omit<AvailabilityType, 'id'>): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      console.log('Adding new availability to Firebase:', availability);

      const availabilityRef = collection(this.firestore, this.availabilityCollection);

      return from(addDoc(availabilityRef, {
        doctor_id: availability.doctor_id,
        date: availability.date,
        start_time: availability.start_time,
        end_time: availability.end_time,
        created_at: new Date()
      })).pipe(
        map(docRef => {
          return this.ngZone.run(() => {
            console.log('Successfully added availability with ID:', docRef.id);

            // Update the local state
            const newAvailability: AvailabilityType = {
              id: docRef.id,
              doctor_id: availability.doctor_id,
              date: availability.date,
              start_time: availability.start_time,
              end_time: availability.end_time
            };

            const currentAvailability = this.availabilitySubject.getValue();
            this.availabilitySubject.next([...currentAvailability, newAvailability]);

            return docRef.id;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error adding availability to Firebase:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  /**
   * Remove an availability slot from Firebase
   */
  removeAvailability(id: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log('Removing availability from Firebase:', id);

      const availabilityRef = doc(this.firestore, `${this.availabilityCollection}/${id}`);

      return from(deleteDoc(availabilityRef)).pipe(
        map(() => {
          return this.ngZone.run(() => {
            console.log('Successfully removed availability');

            // Update the local state
            const currentAvailability = this.availabilitySubject.getValue();
            this.availabilitySubject.next(
              currentAvailability.filter(slot => slot.id !== id)
            );

            return;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error removing availability from Firebase:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  /**
   * Update an availability slot in Firebase
   */
  updateAvailability(id: string, data: Partial<AvailabilityType>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log('Updating availability in Firebase:', id, data);

      const availabilityRef = doc(this.firestore, `${this.availabilityCollection}/${id}`);

      return from(updateDoc(availabilityRef, {
        ...data,
        updated_at: new Date()
      })).pipe(
        map(() => {
          return this.ngZone.run(() => {
            console.log('Successfully updated availability');

            // Update the local state
            const currentAvailability = this.availabilitySubject.getValue();
            const updatedAvailability = currentAvailability.map(slot => {
              if (slot.id === id) {
                return { ...slot, ...data };
              }
              return slot;
            });

            this.availabilitySubject.next(updatedAvailability);

            return;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error updating availability in Firebase:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  // ==================== APPOINTMENT METHODS ====================

  /**
   * Load appointments for a doctor or patient from Firebase
   */
  loadAppointments(userType: 'doctor' | 'patient', userId: string): Observable<AppointmentType[]> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Loading ${userType} appointments for ${userId} from Firebase`);

      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
      const fieldName = userType === 'doctor' ? 'doctor_id' : 'patient_id';

      const appointmentsQuery = query(
        appointmentsRef,
        where(fieldName, '==', userId),
        orderBy('date', 'desc')
      );

      return from(getDocs(appointmentsQuery)).pipe(
        map(querySnapshot => {
          return this.ngZone.run(() => {
            const appointments: AppointmentType[] = [];

            querySnapshot.forEach(doc => {
              const data = doc.data();

              appointments.push({
                id: doc.id,
                doctor_id: data['doctor_id'] || '',
                patient_id: data['patient_id'] || '',
                date: data['date'] || '',
                time: data['time'] || '',
                reasonForVisit: data['reasonForVisit'] || '',
                status: (data['status'] as AppointmentStatusType) || 'Pending',
                doctorNotes: data['doctorNotes'] || ''
              });
            });

            console.log(`Loaded ${appointments.length} appointments from Firebase`);
            this.appointmentsSubject.next(appointments);
            return appointments;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error loading appointments from Firebase:', error);
            return of([]);
          });
        })
      );
    });
  }

  /**
   * Add a new appointment to Firebase
   */
  addAppointment(appointment: Omit<AppointmentType, 'id'>): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      console.log('Adding new appointment to Firebase:', appointment);

      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);

      return from(addDoc(appointmentsRef, {
        doctor_id: appointment.doctor_id,
        patient_id: appointment.patient_id,
        date: appointment.date,
        time: appointment.time,
        reasonForVisit: appointment.reasonForVisit,
        status: appointment.status || 'Pending',
        doctorNotes: appointment.doctorNotes || '',
        created_at: new Date(),
        updated_at: new Date()
      })).pipe(
        map(docRef => {
          return this.ngZone.run(() => {
            console.log('Successfully added appointment with ID:', docRef.id);

            // Update the local state
            const newAppointment: AppointmentType = {
              id: docRef.id,
              doctor_id: appointment.doctor_id,
              patient_id: appointment.patient_id,
              date: appointment.date,
              time: appointment.time,
              reasonForVisit: appointment.reasonForVisit,
              status: appointment.status || 'Pending',
              doctorNotes: appointment.doctorNotes || ''
            };

            const currentAppointments = this.appointmentsSubject.getValue();
            this.appointmentsSubject.next([...currentAppointments, newAppointment]);

            return docRef.id;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error adding appointment to Firebase:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  /**
   * Update an appointment's status in Firebase
   */
  updateAppointmentStatus(id: string, status: AppointmentStatusType, notes?: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log('Updating appointment status in Firebase:', id, status);

      const appointmentRef = doc(this.firestore, `${this.appointmentsCollection}/${id}`);
      const updateData: any = {
        status,
        updated_at: new Date()
      };

      if (notes !== undefined) {
        updateData.doctorNotes = notes;
      }

      return from(updateDoc(appointmentRef, updateData)).pipe(
        map(() => {
          return this.ngZone.run(() => {
            console.log('Successfully updated appointment status');

            // Update the local state
            const currentAppointments = this.appointmentsSubject.getValue();
            const updatedAppointments = currentAppointments.map(appointment => {
              if (appointment.id === id) {
                return {
                  ...appointment,
                  status,
                  doctorNotes: notes !== undefined ? notes : appointment.doctorNotes
                };
              }
              return appointment;
            });

            this.appointmentsSubject.next(updatedAppointments);

            return;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error updating appointment status in Firebase:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  /**
   * Convert availability slots to appointments for booking
   */
  convertAvailabilityToAppointments(doctorId: string, date: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Converting availability slots for doctor ${doctorId} on ${date} to appointments`);

      // First, get all availability slots for the given doctor and date
      const availabilityRef = collection(this.firestore, this.availabilityCollection);
      const availabilityQuery = query(
        availabilityRef,
        where('doctor_id', '==', doctorId),
        where('date', '==', date)
      );

      return from(getDocs(availabilityQuery)).pipe(
        switchMap(querySnapshot => {
          return this.ngZone.run(() => {
            if (querySnapshot.empty) {
              console.log('No availability slots found for conversion');
              return of(undefined);
            }

            console.log(`Found ${querySnapshot.size} availability slots to convert`);
            const batch = writeBatch(this.firestore);
            const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
            const convertedSlots: { startTime: string, endTime: string }[] = [];

            querySnapshot.forEach(docSnapshot => {
              const data = docSnapshot.data();
              const startTime = data['start_time'];
              const endTime = data['end_time'];

              convertedSlots.push({ startTime, endTime });

              // Calculate duration in minutes for compatibility
              const startMinutes = this.timeToMinutes(startTime);
              const endMinutes = this.timeToMinutes(endTime);
              const durationMinutes = endMinutes - startMinutes;

              // Create an appointment for each availability slot
              const appointmentRef = doc(appointmentsRef);
              batch.set(appointmentRef, {
                doctor_id: doctorId,
                patient_id: '',  // Empty patient_id indicates an available slot
                patientName: '',
                date: data['date'],
                time: startTime,
                end_time: endTime,
                durationMinutes: durationMinutes,
                status: 'Available',
                reasonForVisit: '',
                doctorNotes: '',
                created_at: new Date(),
                updated_at: new Date()
              });

              // Mark the availability as converted or delete it
              batch.delete(docSnapshot.ref);
            });

            return from(batch.commit()).pipe(
              map(() => {
                console.log('Successfully converted availability to appointments', convertedSlots);

                // Refresh the availability list
                this.loadDoctorAvailability(doctorId).subscribe();

                return;
              }),
              catchError(error => {
                console.error('Failed to commit batch conversion:', error);
                return throwError(() => new Error(`Failed to convert availability: ${error.message}`));
              })
            );
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error converting availability to appointments:', error);
            return throwError(() => new Error(`Error converting availability: ${error.message}`));
          });
        })
      );
    });
  }

  /**
   * Synchronize availability data between Firebase and localStorage
   */
  syncAvailabilityData(doctorId: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Starting bidirectional sync of availability for doctor ${doctorId}`);

      return from(Promise.all([
        // Get availability from Firebase
        getDocs(query(
          collection(this.firestore, this.availabilityCollection),
          where('doctor_id', '==', doctorId)
        )),
        // This is a promise that resolves immediately with the local data
        Promise.resolve(this.getLocalAvailability(doctorId))
      ])).pipe(
        map(([firebaseSnapshot, localAvailability]) => {
          return this.ngZone.run(() => {
            // Extract Firebase availability data
            const firebaseAvailability: AvailabilityType[] = [];
            firebaseSnapshot.forEach(doc => {
              const data = doc.data();
              firebaseAvailability.push({
                id: doc.id,
                doctor_id: data['doctor_id'] || '',
                date: data['date'] || '',
                start_time: data['start_time'] || '',
                end_time: data['end_time'] || ''
              });
            });

            console.log(`Found ${firebaseAvailability.length} availability slots in Firebase`);
            console.log(`Found ${localAvailability.length} availability slots in localStorage`);

            // Track counts for logging
            let addedToFirebase = 0;
            let addedToLocal = 0;
            let updatedInLocal = 0;

            // Update localStorage with Firebase data
            // This ensures localStorage has all Firebase records
            for (const fbSlot of firebaseAvailability) {
              const existingLocalSlot = localAvailability.find(ls => ls.id === fbSlot.id);
              if (!existingLocalSlot) {
                // Add this Firebase record to localStorage
                console.log(`Adding Firebase slot to localStorage: ${JSON.stringify(fbSlot)}`);
                this.addAvailabilityToLocalStorage(fbSlot);
                addedToLocal++;
              }
            }

            console.log('Updated localStorage availability data');
            console.log(`Bidirectional sync complete: ${addedToFirebase} slots added to Firebase, ${addedToLocal} slots added to localStorage, ${updatedInLocal} slots updated in localStorage`);

            return;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error during availability sync:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  /**
   * Get availability data from localStorage for a specific doctor
   * Private helper method
   */
  private getLocalAvailability(doctorId: string): AvailabilityType[] {
    // We're just reading from localStorage here, not using it as a source of truth
    return this.db.availabilityTable().filter(slot => slot.doctor_id === doctorId);
  }

  /**
   * Add availability to localStorage
   * Private helper method used only for keeping localStorage in sync with Firebase
   */
  private addAvailabilityToLocalStorage(availability: AvailabilityType): void {
    this.db.addAvailability(availability);
  }

  /**
   * Helper method to convert time string (HH:MM) to minutes
   */
  private timeToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
