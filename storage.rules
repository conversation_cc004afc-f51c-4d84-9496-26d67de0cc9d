rules_version = '2';

// Firebase Storage Rules for MedSecura
service firebase.storage {
  match /b/{bucket}/o {
    // Function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Function to check if user is the owner of the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Profile pictures for users
    match /profilePictures/{userId}/{fileName} {
      // Allow users to read/write their own profile picture
      allow read, write: if isAuthenticated() && request.auth.uid == userId;
      
      // Allow reading profile pictures by authenticated users
      allow read: if isAuthenticated();
    }
    
    // Medical records and images
    match /medicalRecords/{recordId}/{fileName} {
      // Allow the patient who owns the record to read
      allow read: if isAuthenticated() && 
                    firestore.get(/databases/(default)/documents/medicalRecords/$(recordId)).data.patientId == request.auth.uid;
      
      // Allow the doctor who created the record to read/write
      allow read, write: if isAuthenticated() && 
                            firestore.get(/databases/(default)/documents/medicalRecords/$(recordId)).data.doctorId == request.auth.uid;
      
      // Allow admin to read/write any medical record
      allow read, write: if isAuthenticated() && 
                            firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
} 