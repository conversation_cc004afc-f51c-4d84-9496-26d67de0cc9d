import { Component, OnInit, NgZone, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  Firestore,
  doc,
  getDoc,
  setDoc,
  collection,
  addDoc,
  deleteDoc,
  DocumentReference,
  collectionData,
  query,
  limit
} from '@angular/fire/firestore';
import { Auth, onAuthStateChanged, User } from '@angular/fire/auth';
import { Subscription } from 'rxjs';

interface TestResult {
  operation: string;
  path: string;
  success: boolean;
  error?: string;
  data?: any;
}

@Component({
  selector: 'app-firebase-validation',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  template: /* template remains the same */
})
export class FirebaseValidationComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  private authSubscription: Subscription | null = null;

  testOperation = 'read';
  testCollection = 'public_test';
  testDocId = '';

  results: TestResult[] = [];

  constructor(
    private firestore: Firestore,
    private auth: Auth,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    // Use NgZone properly to avoid Firebase Zone.js warnings
    this.ngZone.runOutsideAngular(() => {
      this.authSubscription = new Subscription();
      const unsubscribe = onAuthStateChanged(this.auth, (user) => {
        this.ngZone.run(() => {
          this.currentUser = user;
        });
      });

      // Store the unsubscribe function in our subscription for cleanup
      this.authSubscription.add(() => unsubscribe());
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  // Rest of the component methods remain the same
}
