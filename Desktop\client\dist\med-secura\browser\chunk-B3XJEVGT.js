import{A as Ie,B as be,Bb as Ut,Bc as Fe,C as mt,Cb as Pt,D as _,Db as Mt,E as b,Fb as Nt,H as ft,Ha as gt,Hb as Ft,Hc as Le,I as Ge,Ib as Lt,J as ee,Ja as se,Jb as Bt,Jc as Zt,Kc as Xt,O as d,Oa as oe,Qa as _t,R as te,Ra as Oe,S as $,Tb as Ae,U as x,V as De,Vb as Vt,X as Ze,Xb as jt,Yb as qt,Zb as X,_b as Ue,a as W,ba as a,ca as l,cc as $t,d as Se,da as I,dc as Ht,e as dt,ec as zt,fc as Wt,ga as M,gc as Gt,h as pt,ha as w,hb as bt,hc as Pe,i as F,ia as m,ib as xt,j as ke,jb as vt,k as L,kb as ae,kc as Me,l as G,lb as wt,mb as Rt,nb as yt,o as B,oa as c,ob as Ct,oc as Ne,pa as Z,pb as Et,qa as E,qb as Tt,rb as xe,sa as ne,sb as St,t as ge,ta as re,tb as kt,ua as ie,ub as It,v as _e,vb as Dt,wb as le,x as Q,xb as Ot,y as ht,yb as At,z as U}from"./chunk-YV65XDJO.js";var rn="firebasestorage.googleapis.com",sn="storageBucket",Mn=2*60*1e3,Nn=10*60*1e3,Fn=1e3;var v=class t extends vt{constructor(e,n,r=0){super(Xe(e),`Firebase Storage: ${n} (${Xe(e)})`),this.status_=r,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,t.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return Xe(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}},f=function(t){return t.UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment",t}(f||{});function Xe(t){return"storage/"+t}function it(){let t="An unknown error occurred, please check the error payload for server response.";return new v(f.UNKNOWN,t)}function Ln(t){return new v(f.OBJECT_NOT_FOUND,"Object '"+t+"' does not exist.")}function Bn(t){return new v(f.QUOTA_EXCEEDED,"Quota for bucket '"+t+"' exceeded, please view quota on https://firebase.google.com/pricing/.")}function Vn(){let t="User is not authenticated, please authenticate using Firebase Authentication and try again.";return new v(f.UNAUTHENTICATED,t)}function jn(){return new v(f.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project.")}function qn(t){return new v(f.UNAUTHORIZED,"User does not have permission to access '"+t+"'.")}function on(){return new v(f.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function an(){return new v(f.CANCELED,"User canceled the upload/download.")}function $n(t){return new v(f.INVALID_URL,"Invalid URL '"+t+"'.")}function Hn(t){return new v(f.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}function zn(){return new v(f.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+sn+"' property when initializing the app?")}function ln(){return new v(f.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.")}function Wn(){return new v(f.SERVER_FILE_WRONG_SIZE,"Server recorded incorrect upload file size, please retry the upload.")}function Gn(){return new v(f.NO_DOWNLOAD_URL,"The given file does not have any download URLs.")}function Zn(t){return new v(f.UNSUPPORTED_ENVIRONMENT,`${t} is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.`)}function Je(t){return new v(f.INVALID_ARGUMENT,t)}function cn(){return new v(f.APP_DELETED,"The Firebase app was deleted.")}function Xn(t){return new v(f.INVALID_ROOT_OPERATION,"The operation '"+t+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}function we(t,e){return new v(f.INVALID_FORMAT,"String does not match format '"+t+"': "+e)}function ve(t){throw new v(f.INTERNAL_ERROR,"Internal error: "+t)}var N=class t{constructor(e,n){this.bucket=e,this.path_=n}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){let e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(e,n){let r;try{r=t.makeFromUrl(e,n)}catch{return new t(e,"")}if(r.path==="")return r;throw Hn(e)}static makeFromUrl(e,n){let r=null,i="([A-Za-z0-9.\\-_]+)";function s(k){k.path.charAt(k.path.length-1)==="/"&&(k.path_=k.path_.slice(0,-1))}let o="(/(.*))?$",p=new RegExp("^gs://"+i+o,"i"),u={bucket:1,path:3};function h(k){k.path_=decodeURIComponent(k.path)}let g="v[A-Za-z0-9_]+",R=n.replace(/[.]/g,"\\."),C="(/([^?#]*).*)?$",T=new RegExp(`^https?://${R}/${g}/b/${i}/o${C}`,"i"),S={bucket:1,path:3},P=n===rn?"(?:storage.googleapis.com|storage.cloud.google.com)":n,y="([^?#]*)",j=new RegExp(`^https?://${P}/${i}/${y}`,"i"),O=[{regex:p,indices:u,postModify:s},{regex:T,indices:S,postModify:h},{regex:j,indices:{bucket:1,path:2},postModify:h}];for(let k=0;k<O.length;k++){let J=O[k],Y=J.regex.exec(e);if(Y){let ze=Y[J.indices.bucket],fe=Y[J.indices.path];fe||(fe=""),r=new t(ze,fe),J.postModify(r);break}}if(r==null)throw $n(e);return r}},Ye=class{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}};function Kn(t,e,n){let r=1,i=null,s=null,o=!1,p=0;function u(){return p===2}let h=!1;function g(...y){h||(h=!0,e.apply(null,y))}function R(y){i=setTimeout(()=>{i=null,t(T,u())},y)}function C(){s&&clearTimeout(s)}function T(y,...j){if(h){C();return}if(y){C(),g.call(null,y,...j);return}if(u()||o){C(),g.call(null,y,...j);return}r<64&&(r*=2);let O;p===1?(p=2,O=0):O=(r+Math.random())*1e3,R(O)}let S=!1;function P(y){S||(S=!0,C(),!h&&(i!==null?(y||(p=2),clearTimeout(i),R(0)):y||(p=1)))}return R(0),s=setTimeout(()=>{o=!0,P(!0)},n),P}function Jn(t){t(!1)}function Yn(t){return t!==void 0}function Qn(t){return typeof t=="function"}function er(t){return typeof t=="object"&&!Array.isArray(t)}function qe(t){return typeof t=="string"||t instanceof String}function Kt(t){return st()&&t instanceof Blob}function st(){return typeof Blob<"u"}function Jt(t,e,n,r){if(r<e)throw Je(`Invalid value for '${t}'. Expected ${e} or greater.`);if(r>n)throw Je(`Invalid value for '${t}'. Expected ${n} or less.`)}function me(t,e,n){let r=e;return n==null&&(r=`https://${e}`),`${n}://${r}/v0${t}`}function un(t){let e=encodeURIComponent,n="?";for(let r in t)if(t.hasOwnProperty(r)){let i=e(r)+"="+e(t[r]);n=n+i+"&"}return n=n.slice(0,-1),n}var de=function(t){return t[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT",t}(de||{});function dn(t,e){let n=t>=500&&t<600,i=[408,429].indexOf(t)!==-1,s=e.indexOf(t)!==-1;return n||i||s}var Qe=class{constructor(e,n,r,i,s,o,p,u,h,g,R,C=!0){this.url_=e,this.method_=n,this.headers_=r,this.body_=i,this.successCodes_=s,this.additionalRetryCodes_=o,this.callback_=p,this.errorCallback_=u,this.timeout_=h,this.progressCallback_=g,this.connectionFactory_=R,this.retry=C,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((T,S)=>{this.resolve_=T,this.reject_=S,this.start_()})}start_(){let e=(r,i)=>{if(i){r(!1,new ue(!1,null,!0));return}let s=this.connectionFactory_();this.pendingConnection_=s;let o=p=>{let u=p.loaded,h=p.lengthComputable?p.total:-1;this.progressCallback_!==null&&this.progressCallback_(u,h)};this.progressCallback_!==null&&s.addUploadProgressListener(o),s.send(this.url_,this.method_,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&s.removeUploadProgressListener(o),this.pendingConnection_=null;let p=s.getErrorCode()===de.NO_ERROR,u=s.getStatus();if(!p||dn(u,this.additionalRetryCodes_)&&this.retry){let g=s.getErrorCode()===de.ABORT;r(!1,new ue(!1,null,g));return}let h=this.successCodes_.indexOf(u)!==-1;r(!0,new ue(h,s))})},n=(r,i)=>{let s=this.resolve_,o=this.reject_,p=i.connection;if(i.wasSuccessCode)try{let u=this.callback_(p,p.getResponse());Yn(u)?s(u):s()}catch(u){o(u)}else if(p!==null){let u=it();u.serverResponse=p.getErrorText(),this.errorCallback_?o(this.errorCallback_(p,u)):o(u)}else if(i.canceled){let u=this.appDelete_?cn():an();o(u)}else{let u=on();o(u)}};this.canceled_?n(!1,new ue(!1,null,!0)):this.backoffId_=Kn(e,n,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,this.backoffId_!==null&&Jn(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}},ue=class{constructor(e,n,r){this.wasSuccessCode=e,this.connection=n,this.canceled=!!r}};function tr(t,e){e!==null&&e.length>0&&(t.Authorization="Firebase "+e)}function nr(t,e){t["X-Firebase-Storage-Version"]="webjs/"+(e??"AppManager")}function rr(t,e){e&&(t["X-Firebase-GMPID"]=e)}function ir(t,e){e!==null&&(t["X-Firebase-AppCheck"]=e)}function sr(t,e,n,r,i,s,o=!0){let p=un(t.urlParams),u=t.url+p,h=Object.assign({},t.headers);return rr(h,e),tr(h,n),nr(h,s),ir(h,r),new Qe(u,t.method,h,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,i,o)}function or(){return typeof BlobBuilder<"u"?BlobBuilder:typeof WebKitBlobBuilder<"u"?WebKitBlobBuilder:void 0}function ar(...t){let e=or();if(e!==void 0){let n=new e;for(let r=0;r<t.length;r++)n.append(t[r]);return n.getBlob()}else{if(st())return new Blob(t);throw new v(f.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}}function lr(t,e,n){return t.webkitSlice?t.webkitSlice(e,n):t.mozSlice?t.mozSlice(e,n):t.slice?t.slice(e,n):null}function cr(t){if(typeof atob>"u")throw Zn("base-64");return atob(t)}var V={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"},Re=class{constructor(e,n){this.data=e,this.contentType=n||null}};function ur(t,e){switch(t){case V.RAW:return new Re(pn(e));case V.BASE64:case V.BASE64URL:return new Re(hn(t,e));case V.DATA_URL:return new Re(pr(e),hr(e))}throw it()}function pn(t){let e=[];for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(r<=127)e.push(r);else if(r<=2047)e.push(192|r>>6,128|r&63);else if((r&64512)===55296)if(!(n<t.length-1&&(t.charCodeAt(n+1)&64512)===56320))e.push(239,191,189);else{let s=r,o=t.charCodeAt(++n);r=65536|(s&1023)<<10|o&1023,e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|r&63)}else(r&64512)===56320?e.push(239,191,189):e.push(224|r>>12,128|r>>6&63,128|r&63)}return new Uint8Array(e)}function dr(t){let e;try{e=decodeURIComponent(t)}catch{throw we(V.DATA_URL,"Malformed data URL.")}return pn(e)}function hn(t,e){switch(t){case V.BASE64:{let i=e.indexOf("-")!==-1,s=e.indexOf("_")!==-1;if(i||s)throw we(t,"Invalid character '"+(i?"-":"_")+"' found: is it base64url encoded?");break}case V.BASE64URL:{let i=e.indexOf("+")!==-1,s=e.indexOf("/")!==-1;if(i||s)throw we(t,"Invalid character '"+(i?"+":"/")+"' found: is it base64 encoded?");e=e.replace(/-/g,"+").replace(/_/g,"/");break}}let n;try{n=cr(e)}catch(i){throw i.message.includes("polyfill")?i:we(t,"Invalid character found")}let r=new Uint8Array(n.length);for(let i=0;i<n.length;i++)r[i]=n.charCodeAt(i);return r}var Ve=class{constructor(e){this.base64=!1,this.contentType=null;let n=e.match(/^data:([^,]+)?,/);if(n===null)throw we(V.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");let r=n[1]||null;r!=null&&(this.base64=mr(r,";base64"),this.contentType=this.base64?r.substring(0,r.length-7):r),this.rest=e.substring(e.indexOf(",")+1)}};function pr(t){let e=new Ve(t);return e.base64?hn(V.BASE64,e.rest):dr(e.rest)}function hr(t){return new Ve(t).contentType}function mr(t,e){return t.length>=e.length?t.substring(t.length-e.length)===e:!1}var je=class t{constructor(e,n){let r=0,i="";Kt(e)?(this.data_=e,r=e.size,i=e.type):e instanceof ArrayBuffer?(n?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),r=this.data_.length):e instanceof Uint8Array&&(n?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),r=e.length),this.size_=r,this.type_=i}size(){return this.size_}type(){return this.type_}slice(e,n){if(Kt(this.data_)){let r=this.data_,i=lr(r,e,n);return i===null?null:new t(i)}else{let r=new Uint8Array(this.data_.buffer,e,n-e);return new t(r,!0)}}static getBlob(...e){if(st()){let n=e.map(r=>r instanceof t?r.data_:r);return new t(ar.apply(null,n))}else{let n=e.map(o=>qe(o)?ur(V.RAW,o).data:o.data_),r=0;n.forEach(o=>{r+=o.byteLength});let i=new Uint8Array(r),s=0;return n.forEach(o=>{for(let p=0;p<o.length;p++)i[s++]=o[p]}),new t(i,!0)}}uploadData(){return this.data_}};function mn(t){let e;try{e=JSON.parse(t)}catch{return null}return er(e)?e:null}function fr(t){if(t.length===0)return null;let e=t.lastIndexOf("/");return e===-1?"":t.slice(0,e)}function gr(t,e){let n=e.split("/").filter(r=>r.length>0).join("/");return t.length===0?n:t+"/"+n}function fn(t){let e=t.lastIndexOf("/",t.length-2);return e===-1?t:t.slice(e+1)}function _r(t,e){return e}var D=class{constructor(e,n,r,i){this.server=e,this.local=n||e,this.writable=!!r,this.xform=i||_r}},Be=null;function br(t){return!qe(t)||t.length<2?t:fn(t)}function gn(){if(Be)return Be;let t=[];t.push(new D("bucket")),t.push(new D("generation")),t.push(new D("metageneration")),t.push(new D("name","fullPath",!0));function e(s,o){return br(o)}let n=new D("name");n.xform=e,t.push(n);function r(s,o){return o!==void 0?Number(o):o}let i=new D("size");return i.xform=r,t.push(i),t.push(new D("timeCreated")),t.push(new D("updated")),t.push(new D("md5Hash",null,!0)),t.push(new D("cacheControl",null,!0)),t.push(new D("contentDisposition",null,!0)),t.push(new D("contentEncoding",null,!0)),t.push(new D("contentLanguage",null,!0)),t.push(new D("contentType",null,!0)),t.push(new D("metadata","customMetadata",!0)),Be=t,Be}function xr(t,e){function n(){let r=t.bucket,i=t.fullPath,s=new N(r,i);return e._makeStorageReference(s)}Object.defineProperty(t,"ref",{get:n})}function vr(t,e,n){let r={};r.type="file";let i=n.length;for(let s=0;s<i;s++){let o=n[s];r[o.local]=o.xform(r,e[o.server])}return xr(r,t),r}function _n(t,e,n){let r=mn(e);return r===null?null:vr(t,r,n)}function wr(t,e,n,r){let i=mn(e);if(i===null||!qe(i.downloadTokens))return null;let s=i.downloadTokens;if(s.length===0)return null;let o=encodeURIComponent;return s.split(",").map(h=>{let g=t.bucket,R=t.fullPath,C="/b/"+o(g)+"/o/"+o(R),T=me(C,n,r),S=un({alt:"media",token:h});return T+S})[0]}function bn(t,e){let n={},r=e.length;for(let i=0;i<r;i++){let s=e[i];s.writable&&(n[s.server]=t[s.local])}return JSON.stringify(n)}var z=class{constructor(e,n,r,i){this.url=e,this.method=n,this.handler=r,this.timeout=i,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}};function H(t){if(!t)throw it()}function ot(t,e){function n(r,i){let s=_n(t,i,e);return H(s!==null),s}return n}function Rr(t,e){function n(r,i){let s=_n(t,i,e);return H(s!==null),wr(s,i,t.host,t._protocol)}return n}function Ce(t){function e(n,r){let i;return n.getStatus()===401?n.getErrorText().includes("Firebase App Check token is invalid")?i=jn():i=Vn():n.getStatus()===402?i=Bn(t.bucket):n.getStatus()===403?i=qn(t.path):i=r,i.status=n.getStatus(),i.serverResponse=r.serverResponse,i}return e}function at(t){let e=Ce(t);function n(r,i){let s=e(r,i);return r.getStatus()===404&&(s=Ln(t.path)),s.serverResponse=i.serverResponse,s}return n}function yr(t,e,n){let r=e.fullServerUrl(),i=me(r,t.host,t._protocol),s="GET",o=t.maxOperationRetryTime,p=new z(i,s,ot(t,n),o);return p.errorHandler=at(e),p}function Cr(t,e,n){let r=e.fullServerUrl(),i=me(r,t.host,t._protocol),s="GET",o=t.maxOperationRetryTime,p=new z(i,s,Rr(t,n),o);return p.errorHandler=at(e),p}function Er(t,e){let n=e.fullServerUrl(),r=me(n,t.host,t._protocol),i="DELETE",s=t.maxOperationRetryTime;function o(u,h){}let p=new z(r,i,o,s);return p.successCodes=[200,204],p.errorHandler=at(e),p}function Tr(t,e){return t&&t.contentType||e&&e.type()||"application/octet-stream"}function xn(t,e,n){let r=Object.assign({},n);return r.fullPath=t.path,r.size=e.size(),r.contentType||(r.contentType=Tr(null,e)),r}function Sr(t,e,n,r,i){let s=e.bucketOnlyServerUrl(),o={"X-Goog-Upload-Protocol":"multipart"};function p(){let O="";for(let k=0;k<2;k++)O=O+Math.random().toString().slice(2);return O}let u=p();o["Content-Type"]="multipart/related; boundary="+u;let h=xn(e,r,i),g=bn(h,n),R="--"+u+`\r
Content-Type: application/json; charset=utf-8\r
\r
`+g+`\r
--`+u+`\r
Content-Type: `+h.contentType+`\r
\r
`,C=`\r
--`+u+"--",T=je.getBlob(R,r,C);if(T===null)throw ln();let S={name:h.fullPath},P=me(s,t.host,t._protocol),y="POST",j=t.maxUploadRetryTime,q=new z(P,y,ot(t,n),j);return q.urlParams=S,q.headers=o,q.body=T.uploadData(),q.errorHandler=Ce(e),q}var pe=class{constructor(e,n,r,i){this.current=e,this.total=n,this.finalized=!!r,this.metadata=i||null}};function lt(t,e){let n=null;try{n=t.getResponseHeader("X-Goog-Upload-Status")}catch{H(!1)}return H(!!n&&(e||["active"]).indexOf(n)!==-1),n}function kr(t,e,n,r,i){let s=e.bucketOnlyServerUrl(),o=xn(e,r,i),p={name:o.fullPath},u=me(s,t.host,t._protocol),h="POST",g={"X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${r.size()}`,"X-Goog-Upload-Header-Content-Type":o.contentType,"Content-Type":"application/json; charset=utf-8"},R=bn(o,n),C=t.maxUploadRetryTime;function T(P){lt(P);let y;try{y=P.getResponseHeader("X-Goog-Upload-URL")}catch{H(!1)}return H(qe(y)),y}let S=new z(u,h,T,C);return S.urlParams=p,S.headers=g,S.body=R,S.errorHandler=Ce(e),S}function Ir(t,e,n,r){let i={"X-Goog-Upload-Command":"query"};function s(h){let g=lt(h,["active","final"]),R=null;try{R=h.getResponseHeader("X-Goog-Upload-Size-Received")}catch{H(!1)}R||H(!1);let C=Number(R);return H(!isNaN(C)),new pe(C,r.size(),g==="final")}let o="POST",p=t.maxUploadRetryTime,u=new z(n,o,s,p);return u.headers=i,u.errorHandler=Ce(e),u}var Yt=256*1024;function Dr(t,e,n,r,i,s,o,p){let u=new pe(0,0);if(o?(u.current=o.current,u.total=o.total):(u.current=0,u.total=r.size()),r.size()!==u.total)throw Wn();let h=u.total-u.current,g=h;i>0&&(g=Math.min(g,i));let R=u.current,C=R+g,T="";g===0?T="finalize":h===g?T="upload, finalize":T="upload";let S={"X-Goog-Upload-Command":T,"X-Goog-Upload-Offset":`${u.current}`},P=r.slice(R,C);if(P===null)throw ln();function y(k,J){let Y=lt(k,["active","final"]),ze=u.current+g,fe=r.size(),We;return Y==="final"?We=ot(e,s)(k,J):We=null,new pe(ze,fe,Y==="final",We)}let j="POST",q=e.maxUploadRetryTime,O=new z(n,j,y,q);return O.headers=S,O.body=P.uploadData(),O.progressCallback=p||null,O.errorHandler=Ce(t),O}var A={RUNNING:"running",PAUSED:"paused",SUCCESS:"success",CANCELED:"canceled",ERROR:"error"};function Ke(t){switch(t){case"running":case"pausing":case"canceling":return A.RUNNING;case"paused":return A.PAUSED;case"success":return A.SUCCESS;case"canceled":return A.CANCELED;case"error":return A.ERROR;default:return A.ERROR}}var et=class{constructor(e,n,r){if(Qn(e)||n!=null||r!=null)this.next=e,this.error=n??void 0,this.complete=r??void 0;else{let s=e;this.next=s.next,this.error=s.error,this.complete=s.complete}}};function ce(t){return(...e)=>{Promise.resolve().then(()=>t(...e))}}var Qt=null,tt=class{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=de.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=de.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=de.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,n,r,i){if(this.sent_)throw ve("cannot .send() more than once");if(this.sent_=!0,this.xhr_.open(n,e,!0),i!==void 0)for(let s in i)i.hasOwnProperty(s)&&this.xhr_.setRequestHeader(s,i[s].toString());return r!==void 0?this.xhr_.send(r):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw ve("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw ve("cannot .getStatus() before sending");try{return this.xhr_.status}catch{return-1}}getResponse(){if(!this.sent_)throw ve("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw ve("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){this.xhr_.upload!=null&&this.xhr_.upload.removeEventListener("progress",e)}},nt=class extends tt{initXhr(){this.xhr_.responseType="text"}};function K(){return Qt?Qt():new nt}var rt=class{isExponentialBackoffExpired(){return this.sleepTime>this.maxSleepTime}constructor(e,n,r=null){this._transferred=0,this._needToFetchStatus=!1,this._needToFetchMetadata=!1,this._observers=[],this._error=void 0,this._uploadUrl=void 0,this._request=void 0,this._chunkMultiplier=1,this._resolve=void 0,this._reject=void 0,this._ref=e,this._blob=n,this._metadata=r,this._mappings=gn(),this._resumable=this._shouldDoResumable(this._blob),this._state="running",this._errorHandler=i=>{if(this._request=void 0,this._chunkMultiplier=1,i._codeEquals(f.CANCELED))this._needToFetchStatus=!0,this.completeTransitions_();else{let s=this.isExponentialBackoffExpired();if(dn(i.status,[]))if(s)i=on();else{this.sleepTime=Math.max(this.sleepTime*2,Fn),this._needToFetchStatus=!0,this.completeTransitions_();return}this._error=i,this._transition("error")}},this._metadataErrorHandler=i=>{this._request=void 0,i._codeEquals(f.CANCELED)?this.completeTransitions_():(this._error=i,this._transition("error"))},this.sleepTime=0,this.maxSleepTime=this._ref.storage.maxUploadRetryTime,this._promise=new Promise((i,s)=>{this._resolve=i,this._reject=s,this._start()}),this._promise.then(null,()=>{})}_makeProgressCallback(){let e=this._transferred;return n=>this._updateProgress(e+n)}_shouldDoResumable(e){return e.size()>256*1024}_start(){this._state==="running"&&this._request===void 0&&(this._resumable?this._uploadUrl===void 0?this._createResumable():this._needToFetchStatus?this._fetchStatus():this._needToFetchMetadata?this._fetchMetadata():this.pendingTimeout=setTimeout(()=>{this.pendingTimeout=void 0,this._continueUpload()},this.sleepTime):this._oneShotUpload())}_resolveToken(e){Promise.all([this._ref.storage._getAuthToken(),this._ref.storage._getAppCheckToken()]).then(([n,r])=>{switch(this._state){case"running":e(n,r);break;case"canceling":this._transition("canceled");break;case"pausing":this._transition("paused");break}})}_createResumable(){this._resolveToken((e,n)=>{let r=kr(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),i=this._ref.storage._makeRequest(r,K,e,n);this._request=i,i.getPromise().then(s=>{this._request=void 0,this._uploadUrl=s,this._needToFetchStatus=!1,this.completeTransitions_()},this._errorHandler)})}_fetchStatus(){let e=this._uploadUrl;this._resolveToken((n,r)=>{let i=Ir(this._ref.storage,this._ref._location,e,this._blob),s=this._ref.storage._makeRequest(i,K,n,r);this._request=s,s.getPromise().then(o=>{o=o,this._request=void 0,this._updateProgress(o.current),this._needToFetchStatus=!1,o.finalized&&(this._needToFetchMetadata=!0),this.completeTransitions_()},this._errorHandler)})}_continueUpload(){let e=Yt*this._chunkMultiplier,n=new pe(this._transferred,this._blob.size()),r=this._uploadUrl;this._resolveToken((i,s)=>{let o;try{o=Dr(this._ref._location,this._ref.storage,r,this._blob,e,this._mappings,n,this._makeProgressCallback())}catch(u){this._error=u,this._transition("error");return}let p=this._ref.storage._makeRequest(o,K,i,s,!1);this._request=p,p.getPromise().then(u=>{this._increaseMultiplier(),this._request=void 0,this._updateProgress(u.current),u.finalized?(this._metadata=u.metadata,this._transition("success")):this.completeTransitions_()},this._errorHandler)})}_increaseMultiplier(){Yt*this._chunkMultiplier*2<32*1024*1024&&(this._chunkMultiplier*=2)}_fetchMetadata(){this._resolveToken((e,n)=>{let r=yr(this._ref.storage,this._ref._location,this._mappings),i=this._ref.storage._makeRequest(r,K,e,n);this._request=i,i.getPromise().then(s=>{this._request=void 0,this._metadata=s,this._transition("success")},this._metadataErrorHandler)})}_oneShotUpload(){this._resolveToken((e,n)=>{let r=Sr(this._ref.storage,this._ref._location,this._mappings,this._blob,this._metadata),i=this._ref.storage._makeRequest(r,K,e,n);this._request=i,i.getPromise().then(s=>{this._request=void 0,this._metadata=s,this._updateProgress(this._blob.size()),this._transition("success")},this._errorHandler)})}_updateProgress(e){let n=this._transferred;this._transferred=e,this._transferred!==n&&this._notifyObservers()}_transition(e){if(this._state!==e)switch(e){case"canceling":case"pausing":this._state=e,this._request!==void 0?this._request.cancel():this.pendingTimeout&&(clearTimeout(this.pendingTimeout),this.pendingTimeout=void 0,this.completeTransitions_());break;case"running":let n=this._state==="paused";this._state=e,n&&(this._notifyObservers(),this._start());break;case"paused":this._state=e,this._notifyObservers();break;case"canceled":this._error=an(),this._state=e,this._notifyObservers();break;case"error":this._state=e,this._notifyObservers();break;case"success":this._state=e,this._notifyObservers();break}}completeTransitions_(){switch(this._state){case"pausing":this._transition("paused");break;case"canceling":this._transition("canceled");break;case"running":this._start();break}}get snapshot(){let e=Ke(this._state);return{bytesTransferred:this._transferred,totalBytes:this._blob.size(),state:e,metadata:this._metadata,task:this,ref:this._ref}}on(e,n,r,i){let s=new et(n||void 0,r||void 0,i||void 0);return this._addObserver(s),()=>{this._removeObserver(s)}}then(e,n){return this._promise.then(e,n)}catch(e){return this.then(null,e)}_addObserver(e){this._observers.push(e),this._notifyObserver(e)}_removeObserver(e){let n=this._observers.indexOf(e);n!==-1&&this._observers.splice(n,1)}_notifyObservers(){this._finishPromise(),this._observers.slice().forEach(n=>{this._notifyObserver(n)})}_finishPromise(){if(this._resolve!==void 0){let e=!0;switch(Ke(this._state)){case A.SUCCESS:ce(this._resolve.bind(null,this.snapshot))();break;case A.CANCELED:case A.ERROR:let n=this._reject;ce(n.bind(null,this._error))();break;default:e=!1;break}e&&(this._resolve=void 0,this._reject=void 0)}}_notifyObserver(e){switch(Ke(this._state)){case A.RUNNING:case A.PAUSED:e.next&&ce(e.next.bind(e,this.snapshot))();break;case A.SUCCESS:e.complete&&ce(e.complete.bind(e))();break;case A.CANCELED:case A.ERROR:e.error&&ce(e.error.bind(e,this._error))();break;default:e.error&&ce(e.error.bind(e,this._error))()}}resume(){let e=this._state==="paused"||this._state==="pausing";return e&&this._transition("running"),e}pause(){let e=this._state==="running";return e&&this._transition("pausing"),e}cancel(){let e=this._state==="running"||this._state==="pausing";return e&&this._transition("canceling"),e}};var he=class t{constructor(e,n){this._service=e,n instanceof N?this._location=n:this._location=N.makeFromUrl(n,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,n){return new t(e,n)}get root(){let e=new N(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return fn(this._location.path)}get storage(){return this._service}get parent(){let e=fr(this._location.path);if(e===null)return null;let n=new N(this._location.bucket,e);return new t(this._service,n)}_throwIfRoot(e){if(this._location.path==="")throw Xn(e)}};function Or(t,e,n){return t._throwIfRoot("uploadBytesResumable"),new rt(t,new je(e),n)}function Ar(t){t._throwIfRoot("getDownloadURL");let e=Cr(t.storage,t._location,gn());return t.storage.makeRequestWithTokens(e,K).then(n=>{if(n===null)throw Gn();return n})}function Ur(t){t._throwIfRoot("deleteObject");let e=Er(t.storage,t._location);return t.storage.makeRequestWithTokens(e,K)}function Pr(t,e){let n=gr(t._location.path,e),r=new N(t._location.bucket,n);return new he(t.storage,r)}function Mr(t){return/^[A-Za-z]+:\/\//.test(t)}function Nr(t,e){return new he(t,e)}function vn(t,e){if(t instanceof ye){let n=t;if(n._bucket==null)throw zn();let r=new he(n,n._bucket);return e!=null?vn(r,e):r}else return e!==void 0?Pr(t,e):t}function Fr(t,e){if(e&&Mr(e)){if(t instanceof ye)return Nr(t,e);throw Je("To use ref(service, url), the first argument must be a Storage instance.")}else return vn(t,e)}function en(t,e){let n=e?.[sn];return n==null?null:N.makeFromBucketSpec(n,t)}function Lr(t,e,n,r={}){t.host=`${e}:${n}`,t._protocol="http";let{mockUserToken:i}=r;i&&(t._overrideAuthToken=typeof i=="string"?i:xt(i,t.app.options.projectId))}var ye=class{constructor(e,n,r,i,s){this.app=e,this._authProvider=n,this._appCheckProvider=r,this._url=i,this._firebaseVersion=s,this._bucket=null,this._host=rn,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=Mn,this._maxUploadRetryTime=Nn,this._requests=new Set,i!=null?this._bucket=N.makeFromBucketSpec(i,this._host):this._bucket=en(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,this._url!=null?this._bucket=N.makeFromBucketSpec(this._url,e):this._bucket=en(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){Jt("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){Jt("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}_getAuthToken(){return Se(this,null,function*(){if(this._overrideAuthToken)return this._overrideAuthToken;let e=this._authProvider.getImmediate({optional:!0});if(e){let n=yield e.getToken();if(n!==null)return n.accessToken}return null})}_getAppCheckToken(){return Se(this,null,function*(){if(Ct(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let e=this._appCheckProvider.getImmediate({optional:!0});return e?(yield e.getToken()).token:null})}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new he(this,e)}_makeRequest(e,n,r,i,s=!0){if(this._deleted)return new Ye(cn());{let o=sr(e,this._appId,r,i,n,this._firebaseVersion,s);return this._requests.add(o),o.getPromise().then(()=>this._requests.delete(o),()=>this._requests.delete(o)),o}}makeRequestWithTokens(e,n){return Se(this,null,function*(){let[r,i]=yield Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,n,r,i).getPromise()})}},tn="@firebase/storage",nn="0.13.7";var wn="storage";function Rn(t,e,n){return t=ae(t),Or(t,e,n)}function yn(t){return t=ae(t),Ar(t)}function Cn(t){return t=ae(t),Ur(t)}function En(t,e){return t=ae(t),Fr(t,e)}function Tn(t=Tt(),e){t=ae(t);let r=yt(t,wn).getImmediate({identifier:e}),i=bt("storage");return i&&Sn(r,...i),r}function Sn(t,e,n,r={}){Lr(t,e,n,r)}function Br(t,{instanceIdentifier:e}){let n=t.getProvider("app").getImmediate(),r=t.getProvider("auth-internal"),i=t.getProvider("app-check-internal");return new ye(n,r,i,e,Et)}function Vr(){Rt(new wt(wn,Br,"PUBLIC").setMultipleInstances(!0)),xe(tn,nn,""),xe(tn,nn,"esm2017")}Vr();var Ee=class{constructor(e){return e}},kn="storage",ct=class{constructor(){return It(kn)}};var ut=new ht("angularfire2.storage-instances");function jr(t,e){let n=kt(kn,t,e);return n&&new Ee(n)}function qr(t){return(e,n)=>{let r=e.runOutsideAngular(()=>t(n));return new Ee(r)}}var $r={provide:ct,deps:[[new be,ut]]},Hr={provide:Ee,useFactory:jr,deps:[[new be,ut],Ot]};function Ui(t,...e){return xe("angularfire",St.full,"gcs"),mt([Hr,$r,{provide:ut,useFactory:qr(t),multi:!0,deps:[ee,ft,Dt,At,[new be,Mt],[new be,Ut],...e]}])}var Pi=le(Cn,!0,2);var Mi=le(yn,!0);var Ni=le(Tn,!0);var Fi=le(En,!0,2);var Li=le(Rn,!0);function zr(t,e){if(t&1){let n=M();a(0,"button",11),w("click",function(){_(n);let i=m(2);return b(i.onActionClick())}),c(1),l()}if(t&2){let n=m(2);d(),E(" ",n.actionText," ")}}function Wr(t,e){if(t&1){let n=M();a(0,"div",1),w("click",function(){_(n);let i=m();return b(i.onClose())}),a(1,"div",2),w("click",function(i){return _(n),b(i.stopPropagation())}),a(2,"div",3),I(3,"i",4),a(4,"h3"),c(5),l(),a(6,"button",5),w("click",function(){_(n);let i=m();return b(i.onClose())}),I(7,"i",6),l()(),a(8,"div",7)(9,"p"),c(10),l()(),a(11,"div",8),$(12,zr,2,1,"button",9),a(13,"button",10),w("click",function(){_(n);let i=m();return b(i.onClose())}),c(14,"Close"),l()()()()}if(t&2){let n=m();d(5),Z(n.title),d(5),Z(n.message),d(2),x("ngIf",n.actionText)}}var In=class t{show=!1;title="Error";message="";actionText="";onAction=()=>{};showChange=new Ge;close=new Ge;onClose(){this.show=!1,this.showChange.emit(!1),this.close.emit()}onActionClick(){this.onAction(),this.onClose()}static \u0275fac=function(n){return new(n||t)};static \u0275cmp=te({type:t,selectors:[["app-error-modal"]],inputs:{show:"show",title:"title",message:"message",actionText:"actionText",onAction:"onAction"},outputs:{showChange:"showChange",close:"close"},decls:1,vars:1,consts:[["class","modal-overlay",3,"click",4,"ngIf"],[1,"modal-overlay",3,"click"],[1,"modal-content",3,"click"],[1,"modal-header"],[1,"bi","bi-exclamation-circle"],[1,"close-button",3,"click"],[1,"bi","bi-x"],[1,"modal-body"],[1,"modal-footer"],["class","action-button",3,"click",4,"ngIf"],[1,"close-button-text",3,"click"],[1,"action-button",3,"click"]],template:function(n,r){n&1&&$(0,Wr,15,3,"div",0),n&2&&x("ngIf",r.show)},dependencies:[oe,se],styles:[".modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000;animation:_ngcontent-%COMP%_fadeIn .2s ease-out}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:12px;width:90%;max-width:400px;box-shadow:0 20px 25px -5px #0000001a,0 10px 10px -5px #0000000a;animation:_ngcontent-%COMP%_slideIn .3s ease-out}.modal-header[_ngcontent-%COMP%]{padding:1.5rem;display:flex;align-items:center;gap:1rem;border-bottom:1px solid #e5e7eb}.modal-header[_ngcontent-%COMP%]   i.bi-exclamation-circle[_ngcontent-%COMP%]{font-size:1.5rem;color:#dc2626}.modal-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.25rem;color:#111827;flex-grow:1}.close-button[_ngcontent-%COMP%]{background:none;border:none;padding:.5rem;cursor:pointer;color:#6b7280;display:flex;align-items:center;justify-content:center;border-radius:6px;transition:all .2s}.close-button[_ngcontent-%COMP%]:hover{background:#f3f4f6;color:#111827}.modal-body[_ngcontent-%COMP%]{padding:1.5rem}.modal-body[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#4b5563;line-height:1.5}.modal-footer[_ngcontent-%COMP%]{padding:1rem 1.5rem;display:flex;justify-content:flex-end;gap:1rem;border-top:1px solid #e5e7eb}.action-button[_ngcontent-%COMP%]{background:#199a8e;color:#fff;border:none;padding:.5rem 1rem;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s}.action-button[_ngcontent-%COMP%]:hover{background:#168276}.close-button-text[_ngcontent-%COMP%]{background:none;border:none;padding:.5rem 1rem;color:#6b7280;font-weight:500;cursor:pointer;border-radius:6px;transition:all .2s}.close-button-text[_ngcontent-%COMP%]:hover{background:#f3f4f6;color:#111827}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}"]})};var $e=class t{constructor(e){this.http=e}apiUrl=Xt.apiUrl;get(e,n=new _t){return this.http.get(`${this.apiUrl}${e}`,{params:n})}post(e,n={}){return this.http.post(`${this.apiUrl}${e}`,n)}put(e,n={}){return this.http.put(`${this.apiUrl}${e}`,n)}delete(e){return this.http.delete(`${this.apiUrl}${e}`)}static \u0275fac=function(n){return new(n||t)(U(Oe))};static \u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})};var He=class t{constructor(e,n,r,i,s){this.apiService=e;this.auth=n;this.firestore=r;this.ngZone=i;this.authErrorMapper=s;this.loadStoredUser(),this.initAuthStateListener()}currentUserSubject=new pt(null);currentUser$=this.currentUserSubject.asObservable();authSubscription=null;loadStoredUser(){let e=localStorage.getItem("user");if(e)try{this.currentUserSubject.next(JSON.parse(e))}catch(n){console.error("Error parsing stored user data:",n),localStorage.removeItem("user")}else this.currentUserSubject.next(null)}initAuthStateListener(){this.ngZone.runOutsideAngular(()=>{this.authSubscription=new dt;let e=Ft(this.auth,n=>{this.ngZone.run(()=>{n?this.getUserData(n.uid).subscribe(r=>{r&&(this.currentUserSubject.next(r),localStorage.setItem("user",JSON.stringify(r)))}):(this.currentUserSubject.next(null),localStorage.removeItem("user"))})});this.authSubscription.add(()=>e())})}getUserData(e){let n=X(this.firestore,`users/${e}`);return F(Ue(n)).pipe(G(r=>r.exists()?W({uid:e},r.data()):null),B(r=>(console.error("Error fetching user data:",r),ke(null))))}login(e){return F(Lt(this.auth,e.email,e.password)).pipe(ge(n=>this.getUserData(n.user.uid)),G(n=>{if(!n)throw new Error("User data not found");return n}),_e(n=>{localStorage.setItem("user",JSON.stringify(n)),this.currentUserSubject.next(n)}),B(n=>{console.error("Login error:",n);let r=this.authErrorMapper.mapFirebaseError(n);return L(()=>new Error(r))}))}register(e){return F(Nt(this.auth,e.email,e.password)).pipe(ge(n=>{let r={uid:n.user.uid,email:e.email,role:e.role,firstName:e.firstName,lastName:e.lastName},i=X(this.firestore,`users/${n.user.uid}`);return F(zt(i,r)).pipe(G(()=>r))}),_e(n=>{localStorage.setItem("user",JSON.stringify(n)),this.currentUserSubject.next(n)}),B(n=>{console.error("Registration error:",n);let r=this.authErrorMapper.mapRegistrationError(n);return L(()=>new Error(r))}))}logout(){return F(Bt(this.auth)).pipe(_e(()=>{localStorage.removeItem("user"),this.currentUserSubject.next(null)}),B(e=>(console.error("Logout error:",e),L(()=>new Error("Logout failed: "+e.message)))))}updateUserProfile(e,n){let r=X(this.firestore,`users/${e}`);return F(Wt(r,W({},n))).pipe(_e(()=>{let i=this.currentUserSubject.value;if(i&&i.uid===e){let s=W(W({},i),n);this.currentUserSubject.next(s),localStorage.setItem("user",JSON.stringify(s))}}),B(i=>(console.error("Update profile error:",i),L(()=>new Error("Update profile failed: "+i.message)))))}isAuthenticated(){return!!this.currentUserSubject.value}hasRole(e){return this.currentUserSubject.value?.role===e}getCurrentUser(){return this.currentUserSubject.value}ngOnDestroy(){this.authSubscription&&this.authSubscription.unsubscribe()}static \u0275fac=function(n){return new(n||t)(U($e),U(Pt),U(Ae),U(ee),U(Zt))};static \u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})};var An=class t{constructor(e,n,r,i){this.http=e;this.authService=n;this.firestore=r;this.ngZone=i}addDoctor(e){let n=e.name.split(" "),r=n[0],i=n.slice(1).join(" "),s={firstName:r,lastName:i,email:e.email,password:e.password,role:"doctor"};return this.authService.register(s).pipe(ge(o=>o.uid?this.authService.updateUserProfile(o.uid,{specialization:e.specialization,affiliatedInstitution:e.affiliatedInstitution}):L(()=>new Error("Failed to get user ID after registration"))))}updateDoctor(e,n){return this.authService.updateUserProfile(e,n)}getDoctors(){return this.ngZone.runOutsideAngular(()=>{let e=jt(this.firestore,"users"),n=Ht(e,Gt("role","==","doctor"),$t("lastName"));return Vt(n,{idField:"uid"}).pipe(G(r=>this.ngZone.run(()=>r)),B(r=>this.ngZone.run(()=>(console.error("Error fetching doctors:",r),L(()=>r)))))})}getDoctorById(e){return this.ngZone.runOutsideAngular(()=>{let n=X(this.firestore,`users/${e}`);return F(Ue(n)).pipe(G(r=>this.ngZone.run(()=>{if(r.exists()&&r.data().role==="doctor")return W({uid:e},r.data());throw new Error("Doctor not found")})),B(r=>this.ngZone.run(()=>{console.error("Error fetching doctor from Firestore:",r);let i=localStorage.getItem("user");if(i)try{let s=JSON.parse(i);if(s.role==="doctor"&&(s.uid===e||s.id===e||s.doctorId===e))return console.log("Using localStorage fallback for doctor info:",s),ke({uid:s.uid||s.id||s.doctorId,email:s.email||"",role:"doctor",firstName:s.firstName||(s.name?s.name.split(" ")[0]:""),lastName:s.lastName||(s.name?s.name.split(" ").slice(1).join(" "):""),specialization:s.specialization||"",phoneNumber:s.phoneNumber||"",bio:s.bio||"",name:s.name||`${s.firstName||""} ${s.lastName||""}`.trim()})}catch(s){console.error("Error parsing user info from localStorage:",s)}return L(()=>r)})))})}deleteDoctor(e){return this.ngZone.runOutsideAngular(()=>{let n=X(this.firestore,`users/${e}`);return F(qt(n)).pipe(B(r=>this.ngZone.run(()=>(console.error("Error deleting doctor:",r),L(()=>r)))))})}static \u0275fac=function(n){return new(n||t)(U(Oe),U(He),U(Ae),U(ee))};static \u0275prov=Q({token:t,factory:t.\u0275fac,providedIn:"root"})};function Yr(t,e){if(t&1){let n=M();a(0,"div",22)(1,"div",23)(2,"h4",24),c(3,"What will be deleted:"),l(),a(4,"ul",25)(5,"li"),c(6,"\u2022 All user accounts and profiles"),l(),a(7,"li"),c(8,"\u2022 All doctor and patient data"),l(),a(9,"li"),c(10,"\u2022 All appointments and schedules"),l(),a(11,"li"),c(12,"\u2022 All medical records"),l(),a(13,"li"),c(14,"\u2022 All availability slots"),l(),a(15,"li"),c(16,"\u2022 All doctor-patient relationships"),l()()(),a(17,"button",26),w("click",function(){_(n);let i=m();return b(i.showConfirmation=!0)}),I(18,"i",27),c(19," I Want to Reset Firebase Database "),l()()}}function Qr(t,e){if(t&1){let n=M();a(0,"div",22)(1,"div",28)(2,"h4",29),c(3,"\u26A0\uFE0F FINAL WARNING"),l(),a(4,"p",30),c(5," This action cannot be undone. All data will be permanently deleted from Firebase. "),l(),a(6,"div",31)(7,"input",32),ie("ngModelChange",function(i){_(n);let s=m();return re(s.confirmDelete,i)||(s.confirmDelete=i),b(i)}),l(),a(8,"label",33),c(9," I understand this will delete ALL data permanently "),l()(),a(10,"div",34)(11,"input",35),ie("ngModelChange",function(i){_(n);let s=m();return re(s.confirmDevelopment,i)||(s.confirmDevelopment=i),b(i)}),l(),a(12,"label",36),c(13," I confirm this is for development/testing only "),l()()(),a(14,"div",37)(15,"button",38),w("click",function(){_(n);let i=m();return b(i.resetFirebase())}),I(16,"i",39),c(17," DELETE ALL DATA "),l(),a(18,"button",40),w("click",function(){_(n);let i=m();return b(i.cancelReset())}),I(19,"i",41),c(20," Cancel "),l()()()}if(t&2){let n=m();d(7),ne("ngModel",n.confirmDelete),d(4),ne("ngModel",n.confirmDevelopment),d(4),x("disabled",!n.confirmDelete||!n.confirmDevelopment)}}function ei(t,e){if(t&1&&(a(0,"div",22)(1,"div",42)(2,"div",43),I(3,"i",44),a(4,"div")(5,"h4",45),c(6,"Resetting Firebase Database..."),l(),a(7,"p",46),c(8),l()()()(),a(9,"div",47),I(10,"div",48),l()()),t&2){let n=m();d(8),Z(n.progressMessage),d(2),De("width",n.progress,"%")}}function ti(t,e){if(t&1&&(a(0,"div",58)(1,"h5",59),c(2,"Deleted Data:"),l(),a(3,"div",60)(4,"div"),c(5),l(),a(6,"div"),c(7),l(),a(8,"div"),c(9),l(),a(10,"div"),c(11),l(),a(12,"div"),c(13),l(),a(14,"div"),c(15),l()()()),t&2){let n=m(2);d(5),E("Users: ",n.resetResult.deletedCounts.users,""),d(2),E("Doctors: ",n.resetResult.deletedCounts.doctors,""),d(2),E("Patients: ",n.resetResult.deletedCounts.patients,""),d(2),E("Appointments: ",n.resetResult.deletedCounts.appointments,""),d(2),E("Medical Records: ",n.resetResult.deletedCounts.medicalRecords,""),d(2),E("Availability: ",n.resetResult.deletedCounts.availability,"")}}function ni(t,e){if(t&1){let n=M();a(0,"div",49)(1,"div",50)(2,"div",51)(3,"div",3),I(4,"i",52),l(),a(5,"div",53)(6,"h4",54),c(7),l(),a(8,"p",55),c(9),l(),$(10,ti,16,6,"div",56),l()()(),a(11,"button",57),w("click",function(){_(n);let i=m();return b(i.resetState())}),I(12,"i",21),c(13," Reset Tool "),l()()}if(t&2){let n=m();d(),x("ngClass",n.resetResult.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"),d(3),x("ngClass",n.resetResult.success?"bi bi-check-circle text-green-500":"bi bi-x-circle text-red-500"),d(2),x("ngClass",n.resetResult.success?"text-green-800":"text-red-800"),d(),E(" ",n.resetResult.success?"Reset Successful!":"Reset Failed"," "),d(),x("ngClass",n.resetResult.success?"text-green-700":"text-red-700"),d(),E(" ",n.resetResult.message," "),d(),x("ngIf",n.resetResult.success&&n.resetResult.deletedCounts)}}var Un=class t{firebaseDbService=Ie(Le);showConfirmation=!1;confirmDelete=!1;confirmDevelopment=!1;loading=!1;progress=0;progressMessage="";resetResult=null;resetFirebase(){if(!this.confirmDelete||!this.confirmDevelopment)return;this.loading=!0,this.progress=0,this.progressMessage="Initializing reset...",this.resetResult=null;let e=setInterval(()=>{this.progress<90&&(this.progress+=15,this.updateProgressMessage())},500);this.firebaseDbService.resetAllFirebaseData().subscribe({next:n=>{clearInterval(e),this.progress=100,this.progressMessage="Reset complete!",this.resetResult=n,this.loading=!1,this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1},error:n=>{clearInterval(e),this.loading=!1,this.resetResult={success:!1,message:`Reset failed: ${n.message}`},this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1}})}updateProgressMessage(){let e=["Connecting to Firebase...","Fetching collections...","Preparing batch deletion...","Deleting user data...","Deleting appointments...","Deleting medical records...","Finalizing reset..."],n=Math.floor(this.progress/15);this.progressMessage=e[n]||"Processing..."}cancelReset(){this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1}resetState(){this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1,this.loading=!1,this.progress=0,this.progressMessage="",this.resetResult=null}clearUserState(){this.firebaseDbService.clearCurrentUserState(),alert("Current user state cleared! You may need to log in again.")}refreshPage(){window.location.reload()}static \u0275fac=function(n){return new(n||t)};static \u0275cmp=te({type:t,selectors:[["app-firebase-reset"]],decls:34,vars:4,consts:[[1,"firebase-reset-container"],[1,"bg-red-50","border-l-4","border-red-400","p-4","mb-4"],[1,"flex"],[1,"flex-shrink-0"],[1,"bi","bi-exclamation-triangle","text-red-400","text-lg"],[1,"ml-3"],[1,"text-sm","font-medium","text-red-800"],[1,"mt-2","text-sm","text-red-700"],[1,"bg-white","rounded-lg","shadow-md","p-6","border","border-red-200"],[1,"flex","items-center","justify-between","mb-4"],[1,"text-lg","font-semibold","text-gray-900"],[1,"text-sm","text-gray-600","mt-1"],[1,"bi","bi-database-x","text-red-500","text-2xl"],["class","space-y-4 mb-6",4,"ngIf"],["class","space-y-4",4,"ngIf"],[1,"mt-4","bg-yellow-50","border","border-yellow-200","rounded-lg","p-4"],[1,"font-medium","text-yellow-800","mb-2"],[1,"space-y-2"],[1,"w-full","bg-yellow-600","hover:bg-yellow-700","text-white","font-medium","py-2","px-4","rounded","transition","duration-200",3,"click"],[1,"bi","bi-person-x","mr-2"],[1,"w-full","bg-blue-600","hover:bg-blue-700","text-white","font-medium","py-2","px-4","rounded","transition","duration-200",3,"click"],[1,"bi","bi-arrow-clockwise","mr-2"],[1,"space-y-4","mb-6"],[1,"bg-gray-50","p-4","rounded-lg"],[1,"font-medium","text-gray-900","mb-2"],[1,"text-sm","text-gray-700","space-y-1"],[1,"w-full","bg-red-600","hover:bg-red-700","text-white","font-bold","py-3","px-4","rounded-lg","transition","duration-200",3,"click"],[1,"bi","bi-exclamation-triangle","mr-2"],[1,"bg-red-50","border","border-red-200","p-4","rounded-lg"],[1,"font-bold","text-red-800","mb-2"],[1,"text-sm","text-red-700","mb-3"],[1,"flex","items-center","space-x-2","mb-3"],["type","checkbox","id","confirmDelete",1,"h-4","w-4","text-red-600","focus:ring-red-500","border-gray-300","rounded",3,"ngModelChange","ngModel"],["for","confirmDelete",1,"text-sm","text-red-700"],[1,"flex","items-center","space-x-2"],["type","checkbox","id","confirmDevelopment",1,"h-4","w-4","text-red-600","focus:ring-red-500","border-gray-300","rounded",3,"ngModelChange","ngModel"],["for","confirmDevelopment",1,"text-sm","text-red-700"],[1,"flex","space-x-3"],[1,"flex-1","bg-red-600","hover:bg-red-700","disabled:bg-gray-400","text-white","font-bold","py-3","px-4","rounded-lg","transition","duration-200",3,"click","disabled"],[1,"bi","bi-trash","mr-2"],[1,"flex-1","bg-gray-500","hover:bg-gray-600","text-white","font-bold","py-3","px-4","rounded-lg","transition","duration-200",3,"click"],[1,"bi","bi-x-circle","mr-2"],[1,"bg-blue-50","border","border-blue-200","p-4","rounded-lg"],[1,"flex","items-center"],[1,"bi","bi-arrow-repeat","animate-spin","text-blue-600","mr-3"],[1,"font-medium","text-blue-800"],[1,"text-sm","text-blue-700","mt-1"],[1,"w-full","bg-gray-200","rounded-full","h-2"],[1,"bg-blue-600","h-2","rounded-full","transition-all","duration-300"],[1,"space-y-4"],[1,"p-4","rounded-lg","border",3,"ngClass"],[1,"flex","items-start"],[1,"text-lg",3,"ngClass"],[1,"ml-3","flex-1"],[1,"font-semibold",3,"ngClass"],[1,"text-sm","mt-1",3,"ngClass"],["class","mt-3",4,"ngIf"],[1,"w-full","bg-gray-500","hover:bg-gray-600","text-white","font-bold","py-2","px-4","rounded-lg","transition","duration-200",3,"click"],[1,"mt-3"],[1,"font-medium","text-green-800","mb-2"],[1,"grid","grid-cols-2","gap-2","text-sm","text-green-700"]],template:function(n,r){n&1&&(a(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),I(4,"i",4),l(),a(5,"div",5)(6,"h3",6),c(7," \u{1F6A8} DEVELOPMENT TOOL - DANGER ZONE "),l(),a(8,"div",7)(9,"p"),c(10,"This tool will permanently delete ALL data from Firebase. Use only for development and testing!"),l()()()()(),a(11,"div",8)(12,"div",9)(13,"div")(14,"h3",10),c(15,"Reset Firebase Database"),l(),a(16,"p",11),c(17," Delete all data from Firebase collections to start fresh "),l()(),a(18,"div",3),I(19,"i",12),l()(),$(20,Yr,20,0,"div",13)(21,Qr,21,3,"div",13)(22,ei,11,3,"div",13)(23,ni,14,7,"div",14),l(),a(24,"div",15)(25,"h4",16),c(26,"Additional Development Actions:"),l(),a(27,"div",17)(28,"button",18),w("click",function(){return r.clearUserState()}),I(29,"i",19),c(30," Clear Current User State "),l(),a(31,"button",20),w("click",function(){return r.refreshPage()}),I(32,"i",21),c(33," Refresh Application "),l()()()()),n&2&&(d(20),x("ngIf",!r.showConfirmation),d(),x("ngIf",r.showConfirmation&&!r.loading),d(),x("ngIf",r.loading),d(),x("ngIf",r.resetResult))},dependencies:[oe,gt,se,Fe,Pe,Me,Ne],styles:[".animate-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.firebase-reset-container[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}"]})};function ri(t,e){if(t&1){let n=M();a(0,"div")(1,"div",4)(2,"h3",5),c(3,"\u{1F525} DEV TOOL"),l(),a(4,"button",6),w("click",function(){_(n);let i=m();return b(i.toggleVisibility())}),c(5),l()(),a(6,"div",7)(7,"p",8),c(8," Reset Firebase Database (Delete ALL data) "),l(),a(9,"button",9),w("click",function(){_(n);let i=m();return b(i.showConfirmation=!0)}),c(10," \u{1F5D1}\uFE0F RESET FIREBASE "),l()()()}if(t&2){let n=m();d(5),E(" ",n.isMinimized?"\u25BC":"\u25B2"," "),d(),x("hidden",n.isMinimized)}}function ii(t,e){if(t&1){let n=M();a(0,"div")(1,"h3",10),c(2,"\u26A0\uFE0F CONFIRM RESET"),l(),a(3,"p",8),c(4," This will DELETE ALL data permanently! "),l(),a(5,"div",11)(6,"label",12)(7,"input",13),ie("ngModelChange",function(i){_(n);let s=m();return re(s.confirmDelete,i)||(s.confirmDelete=i),b(i)}),l(),a(8,"span",14),c(9,"I understand data will be deleted"),l()(),a(10,"label",12)(11,"input",13),ie("ngModelChange",function(i){_(n);let s=m();return re(s.confirmDevelopment,i)||(s.confirmDevelopment=i),b(i)}),l(),a(12,"span",14),c(13,"This is for development only"),l()()(),a(14,"div",15)(15,"button",16),w("click",function(){_(n);let i=m();return b(i.resetFirebase())}),c(16," DELETE ALL "),l(),a(17,"button",17),w("click",function(){_(n);let i=m();return b(i.cancelReset())}),c(18," Cancel "),l()()()}if(t&2){let n=m();d(7),ne("ngModel",n.confirmDelete),d(4),ne("ngModel",n.confirmDevelopment),d(4),x("disabled",!n.confirmDelete||!n.confirmDevelopment)}}function si(t,e){if(t&1&&(a(0,"div")(1,"h3",10),c(2,"\u{1F504} RESETTING..."),l(),a(3,"div",18),I(4,"div",19),l(),a(5,"p",20),c(6),l()()),t&2){let n=m();d(4),De("width",n.progress,"%"),d(2),Z(n.progressMessage)}}function oi(t,e){if(t&1&&(a(0,"div",24)(1,"p",25),c(2,"Deleted:"),l(),a(3,"div",26)(4,"span"),c(5),l(),a(6,"span"),c(7),l(),a(8,"span"),c(9),l(),a(10,"span"),c(11),l()()()),t&2){let n=m(2);d(5),E("Users: ",n.resetResult.deletedCounts.users,""),d(2),E("Doctors: ",n.resetResult.deletedCounts.doctors,""),d(2),E("Patients: ",n.resetResult.deletedCounts.patients,""),d(2),E("Appointments: ",n.resetResult.deletedCounts.appointments,"")}}function ai(t,e){if(t&1){let n=M();a(0,"div")(1,"h3",10),c(2),l(),a(3,"p",21),c(4),l(),$(5,oi,12,4,"div",22),a(6,"div",15)(7,"button",23),w("click",function(){_(n);let i=m();return b(i.refreshPage())}),c(8," \u{1F504} Refresh "),l(),a(9,"button",17),w("click",function(){_(n);let i=m();return b(i.resetState())}),c(10," Reset Tool "),l()()()}if(t&2){let n=m();d(),Ze(n.resetResult.success?"text-green-300":"text-red-300"),d(),E(" ",n.resetResult.success?"\u2705 SUCCESS":"\u274C FAILED"," "),d(),Ze(n.resetResult.success?"text-green-200":"text-red-200"),d(),E(" ",n.resetResult.message," "),d(),x("ngIf",n.resetResult.success&&n.resetResult.deletedCounts)}}function li(t,e){if(t&1){let n=M();a(0,"div",27)(1,"button",28),w("click",function(){_(n);let i=m();return b(i.clearUserState())}),c(2," Clear User State "),l()()}}var Pn=class t{firebaseDbService=Ie(Le);isMinimized=!1;showConfirmation=!1;confirmDelete=!1;confirmDevelopment=!1;loading=!1;progress=0;progressMessage="";resetResult=null;toggleVisibility(){this.isMinimized=!this.isMinimized}resetFirebase(){if(!this.confirmDelete||!this.confirmDevelopment)return;this.loading=!0,this.progress=0,this.progressMessage="Initializing reset...",this.resetResult=null;let e=setInterval(()=>{this.progress<90&&(this.progress+=15,this.updateProgressMessage())},500);this.firebaseDbService.resetAllFirebaseData().subscribe({next:n=>{clearInterval(e),this.progress=100,this.progressMessage="Reset complete!",this.resetResult=n,this.loading=!1,this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1},error:n=>{clearInterval(e),this.loading=!1,this.resetResult={success:!1,message:`Reset failed: ${n.message}`},this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1}})}updateProgressMessage(){let e=["Connecting to Firebase...","Fetching collections...","Preparing batch deletion...","Deleting user data...","Deleting appointments...","Deleting medical records...","Finalizing reset..."],n=Math.floor(this.progress/15);this.progressMessage=e[n]||"Processing..."}cancelReset(){this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1}resetState(){this.showConfirmation=!1,this.confirmDelete=!1,this.confirmDevelopment=!1,this.loading=!1,this.progress=0,this.progressMessage="",this.resetResult=null}clearUserState(){this.firebaseDbService.clearCurrentUserState(),alert("Current user state cleared! You may need to log in again.")}refreshPage(){window.location.reload()}static \u0275fac=function(n){return new(n||t)};static \u0275cmp=te({type:t,selectors:[["app-firebase-reset-button"]],decls:7,vars:5,consts:[[1,"firebase-reset-button-container",2,"position","fixed","top","10px","right","10px","z-index","9999"],[1,"bg-red-600","text-white","p-4","rounded-lg","shadow-lg","border-2","border-red-700",2,"min-width","300px"],[4,"ngIf"],["class","mt-2",4,"ngIf"],[1,"flex","items-center","justify-between","mb-3"],[1,"font-bold","text-lg"],[1,"text-red-200","hover:text-white","text-sm",3,"click"],[3,"hidden"],[1,"text-sm","mb-3","text-red-100"],[1,"w-full","bg-red-800","hover:bg-red-900","text-white","font-bold","py-2","px-4","rounded","transition","duration-200",3,"click"],[1,"font-bold","text-lg","mb-3"],[1,"space-y-2","mb-4"],[1,"flex","items-center","text-sm"],["type","checkbox",1,"mr-2","h-4","w-4",3,"ngModelChange","ngModel"],[1,"text-red-100"],[1,"flex","space-x-2"],[1,"flex-1","bg-red-900","hover:bg-red-950","disabled:bg-gray-600","text-white","font-bold","py-2","px-3","rounded","text-sm","transition","duration-200",3,"click","disabled"],[1,"flex-1","bg-gray-600","hover:bg-gray-700","text-white","font-bold","py-2","px-3","rounded","text-sm","transition","duration-200",3,"click"],[1,"w-full","bg-red-800","rounded-full","h-2","mb-2"],[1,"bg-white","h-2","rounded-full","transition-all","duration-300"],[1,"text-sm","text-red-100"],[1,"text-sm","mb-3"],["class","mb-3",4,"ngIf"],[1,"flex-1","bg-green-600","hover:bg-green-700","text-white","font-bold","py-2","px-3","rounded","text-sm","transition","duration-200",3,"click"],[1,"mb-3"],[1,"text-xs","text-green-200"],[1,"text-xs","text-green-100","grid","grid-cols-2","gap-1"],[1,"mt-2"],[1,"w-full","bg-yellow-600","hover:bg-yellow-700","text-white","font-medium","py-1","px-2","rounded","text-xs","transition","duration-200",3,"click"]],template:function(n,r){n&1&&(a(0,"div",0)(1,"div",1),$(2,ri,11,2,"div",2)(3,ii,19,3,"div",2)(4,si,7,3,"div",2)(5,ai,11,7,"div",2)(6,li,3,0,"div",3),l()()),n&2&&(d(2),x("ngIf",!r.showConfirmation&&!r.loading&&!r.resetResult),d(),x("ngIf",r.showConfirmation&&!r.loading),d(),x("ngIf",r.loading),d(),x("ngIf",r.resetResult),d(),x("ngIf",r.isMinimized&&!r.showConfirmation&&!r.loading&&!r.resetResult))},dependencies:[oe,se,Fe,Pe,Me,Ne],styles:[".firebase-reset-button-container[_ngcontent-%COMP%]{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}input[type=checkbox][_ngcontent-%COMP%]{accent-color:#dc2626}button[_ngcontent-%COMP%]:disabled{cursor:not-allowed;opacity:.6}@media (max-width: 768px){.firebase-reset-button-container[_ngcontent-%COMP%]{position:relative!important;top:auto!important;right:auto!important;margin:1rem}}"]})};export{Ee as a,Ui as b,Pi as c,Mi as d,Ni as e,Fi as f,Li as g,In as h,He as i,An as j,Un as k,Pn as l};
