rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Function to check if user is the owner of the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Users collection - allow authenticated users to read their own document
    match /users/{userId} {
      // Allow users to read and write their own document
      allow read: if isAuthenticated();
      allow write: if isOwner(userId);
    }
    
    // Allow doctors to access their own data
    match /doctors/{doctorId} {
      allow read: if isAuthenticated();
      allow write: if isOwner(doctorId);
    }
    
    // Doctor Availability collection
    match /doctorAvailability/{availabilityId} {
      // Any authenticated user can read availabilities
      allow read: if isAuthenticated();
      // Only the doctor who owns the availability can write to it
      allow write: if isAuthenticated() && 
                     (resource == null || 
                      request.resource.data.doctor_id == resource.data.doctor_id) && 
                     (request.auth.uid == request.resource.data.doctor_id || 
                      get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == "admin");
    }
    
    // Public test collection - any authenticated user can read/write
    match /public_test/{docId} {
      allow read, write: if isAuthenticated();
    }
    
    // DEBUG MODE: Allow all operations during testing
    // IMPORTANT: REMOVE BEFORE GOING TO PRODUCTION
    match /{document=**} {
      allow read, write: if true;
    }
  }
} 