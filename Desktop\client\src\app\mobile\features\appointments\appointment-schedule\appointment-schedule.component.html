<div class="schedule-screen">
  <app-back-button routerLink="/patient-dashboard"/>
  <h1 class="title">My Appointments</h1>

  <!-- Error Message -->
  <div *ngIf="error" class="error-alert">
    <span>{{ error }}</span>
    <button (click)="loadAppointments()">Retry</button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading appointments...</p>
  </div>

  <div class="content" *ngIf="!isLoading">
    <!-- Tabs -->
    <div class="tabs">
      <button
        [class.active]="activeTab === 'upcoming'"
        (click)="setActiveTab('upcoming')"
        class="tab">
        Approved
        <span class="badge" *ngIf="hasUpcomingAppointments"></span>
      </button>
      <button
        [class.active]="activeTab === 'completed'"
        (click)="setActiveTab('completed')"
        class="tab">
        Completed
      </button>
      <button
        [class.active]="activeTab === 'cancelled'"
        (click)="setActiveTab('cancelled')"
        class="tab">
        Cancelled
      </button>
    </div>

    <!-- Appointment List -->
    <div class="appointments-list">
      <ng-container *ngIf="filteredAppointments.length > 0; else noAppointments">
        <div
          *ngFor="let appointment of filteredAppointments"
          class="appointment-card"
          [class.approved-appointment]="appointment.status === 'Approved'">

          <!-- Card Status Indicator -->
          <div class="card-status-indicator" [style.backgroundColor]="getStatusColor(appointment.status)"></div>

          <!-- Card Header -->
          <div class="card-header">
            <div class="doctor-info">
              <h3>Dr. {{ appointment.doctorName }}</h3>
              <p class="specialization">{{ appointment.specialization || 'General Practitioner' }}</p>
            </div>
            <div class="status-badge" [style.backgroundColor]="getStatusColor(appointment.status) + '20'" [style.color]="getStatusColor(appointment.status)">
              {{ appointment.status }}
            </div>
          </div>

          <!-- Appointment Details -->
          <div class="appointment-meta">
            <div class="meta-item">
              <i class="bi bi-calendar-check"></i>
              <span>{{ formatDate(appointment.appointmentDate) }}</span>
            </div>
            <div class="meta-item">
              <i class="bi bi-clock"></i>
              <span>{{ formatTime(appointment.appointmentTime) }}</span>
            </div>
            <div class="meta-item">
              <i class="bi bi-hourglass-split"></i>
              <span>{{ appointment.durationMinutes }} min</span>
            </div>
          </div>


          <!-- Doctor's Notes (if available) -->
          <div class="doctor-notes" *ngIf="appointment.doctorNotes && activeTab === 'completed'">
            <h4>Doctor's Notes</h4>
            <p>{{ appointment.doctorNotes }}</p>
          </div>

        </div>
      </ng-container>

      <ng-template #noAppointments>
        <div class="no-appointments">
          <div class="empty-state">
            <i class="bi" [ngClass]="{
              'bi-calendar-x': activeTab === 'cancelled',
              'bi-calendar-check': activeTab === 'completed',
              'bi-calendar-plus': activeTab === 'upcoming'
            }"></i>
            <p *ngIf="activeTab === 'upcoming'">
              You don't have any approved appointments.
              <br>
              <small>Would you like to book one?</small>
            </p>
            <p *ngIf="activeTab === 'completed'">
              You don't have any completed appointments yet.
            </p>
            <p *ngIf="activeTab === 'cancelled'">
              You don't have any cancelled appointments.
            </p>
          </div>
        </div>
      </ng-template>
    </div>
  </div>

  <app-navbar />
</div>
