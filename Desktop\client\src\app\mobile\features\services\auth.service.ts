import { Injectable, Ng<PERSON>one } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError, BehaviorSubject, from } from 'rxjs';
import { catchError, tap, map, switchMap } from 'rxjs/operators';

// Firebase imports
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword,
  signOut, UserCredential, updateProfile, User as FirebaseUser } from '@angular/fire/auth';
import { Firestore, doc, getDoc, setDoc, updateDoc, collection, query,
  where, getDocs } from '@angular/fire/firestore';

import { ApiUrlService } from '../../core/services/api-url.service';
import { Db } from '../../../../app/db';
import { UserType, PatientType } from '../../../../app/type';
import { LocalStorageService } from './local-storage.service';
import { CURRENT_PATIENT } from '../../../../app/constant';
import { AuthErrorMapperService } from '../../../core/services/auth-error-mapper.service';

export interface RegisterRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: string;
}

interface LoginResponse {
  message: string;
  token: string;
  name: string;
  email: string;
  id?: string;
  userId?: string;
  patientId?: string;
  profilePicture?: string | null;
  phoneNumber?: string;
  address?: string;
  role?: string;
  firstName?: string;
  lastName?: string;
  success: boolean;
}

export interface UserInfo {
  id?: string;
  patientId?: string;
  email: string;
  full_name: string;
  profilePicture?: string | null;
  role?: string;
  phoneNumber?: string;
  address?: string;
  firstName?: string;
  lastName?: string;
}

interface ProfileUpdateResponse {
  message: string;
  user: UserInfo;
}

interface User {
  displayName: string;
  profileImage: string | null;
  email: string;
  id?: string;
  patientId?: string;
}

export interface RegisterResponse {
  message: string;
  email: string;
  id?: string;
  userId?: string;
  patientId?: string;
  token?: string;
  name?: string;
  offline?: boolean; // Add offline property for offline registration handling
  role?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly tokenKey = 'med_secure_token'; // Updated to use consistent key
  private readonly userInfoKey = 'user_info';
  private readonly baseUrl: string;

  private currentUserSubject = new BehaviorSubject<User | null>(null);
  currentUser$ = this.currentUserSubject.asObservable();
  private userIdKey = 'patient_user_id';
  private readonly patientIdKey = 'med_secure_patient_id';
  private readonly legacyPatientIdKey = 'patient_id';

  constructor(
    private http: HttpClient,
    private apiUrlService: ApiUrlService,
    private auth: Auth,  // Firebase Auth
    private firestore: Firestore,  // Firebase Firestore
    private storage: LocalStorageService,
    private ngZone: NgZone,  // Add NgZone
    private db: Db,
    private authErrorMapper: AuthErrorMapperService
  ) {
    this.baseUrl = this.apiUrlService.getBaseUrl();
    console.log('Auth Service initialized with base URL:', this.baseUrl);

    // Auto login removed - users must manually log in

    // Set up Firebase auth state listener (only for active sessions, not auto-login)
    this.auth.onAuthStateChanged((user) => {
      if (user) {
        // Only handle active Firebase sessions during login, not automatic restoration
        console.log('Firebase auth state changed - user signed in:', user.uid);
      } else {
        // User is signed out
        this.currentUserSubject.next(null);
        console.log('Firebase auth state changed - user signed out');
      }
    });
  }

  // Auto login method removed - users must manually log in

  /**
   * Handle Firebase user authentication
   */
  private handleFirebaseUser(firebaseUser: FirebaseUser): void {
    console.log('Handling Firebase user:', firebaseUser);
    // Run Firebase operations outside Angular zone to prevent warnings
    this.ngZone.runOutsideAngular(() => {
      // Fetch additional user data from Firestore
      this.fetchUserDataFromFirestore(firebaseUser.uid)
        .then(userData => {
          // Return to Angular zone for UI updates
          this.ngZone.run(() => {
            if (userData) {
              // Create user info from Firebase and Firestore data
              const userInfo: UserInfo = {
                id: firebaseUser.uid,
                patientId: userData['patientId'] || firebaseUser.uid,
                full_name: firebaseUser.displayName || userData['fullName'] || `${userData['firstName'] || ''} ${userData['lastName'] || ''}`.trim(),
                email: firebaseUser.email || userData['email'] || '',
                profilePicture: firebaseUser.photoURL || userData['profilePicture'] || null,
                phoneNumber: userData['phoneNumber'] || '',
                address: userData['address'] || '',
                role: userData['role'] || 'patient'
              };

              // Save user info to localStorage as backup
              this.storage.setItem(this.userInfoKey, userInfo);

              // Update current user subject
              this.currentUserSubject.next({
                displayName: userInfo.full_name,
                profileImage: userInfo.profilePicture !== undefined ? userInfo.profilePicture : null,
                email: userInfo.email,
                id: userInfo.id || '',
                patientId: userInfo.patientId
              });

              // Set current patient in DB service if role is patient
              if ((userInfo.role?.toUpperCase() === 'PATIENT' || !userInfo.role) && userInfo.id) {
                console.log('Setting current patient in DB from handleFirebaseUser:', userInfo);
                const patientUser: UserType = {
                  id: userInfo.id,
                  email: userInfo.email,
                  firstname: userInfo.full_name.split(' ')[0] || '',
                  lastname: userInfo.full_name.split(' ').slice(1).join(' ') || '',
                  role: 'PATIENT',
                  password: '' // We don't store or need the password
                };
                this.db.setCurrentPatient(patientUser);
                this.storage.setItem(CURRENT_PATIENT, patientUser);
                console.log('Current patient set successfully in handleFirebaseUser');
              }

              console.log('Firebase user data processed successfully');
            } else {
              console.warn('No Firestore data found for Firebase user');
            }
          });
        })
        .catch(error => {
          this.ngZone.run(() => {
            console.error('Error fetching user data from Firestore:', error);
          });
        });
    });
  }

  /**
   * Fetch user data from Firestore
   */
  private async fetchUserDataFromFirestore(userId: string): Promise<Record<string, any> | null> {
    try {
      // Run Firebase operations outside Angular zone
      return await this.ngZone.runOutsideAngular(async () => {
        // Try to get user data from the patients collection first
        const patientDocRef = doc(this.firestore, 'patients', userId);
        const patientDoc = await getDoc(patientDocRef);

        if (patientDoc.exists()) {
          return { ...patientDoc.data(), patientId: patientDoc.id };
        }

        // If not found in patients, check users collection
        const userDocRef = doc(this.firestore, 'users', userId);
        const userDoc = await getDoc(userDocRef);

        if (userDoc.exists()) {
          return userDoc.data() as Record<string, any>;
        }

        console.warn('No user data found in Firestore for ID:', userId);
        return null;
      });
    } catch (error) {
      console.error('Error fetching user data from Firestore:', error);
      return null;
    }
  }

  login(email: string, password: string): Observable<LoginResponse> {
    console.log('Attempting login with Firebase for:', email);

    return new Observable<LoginResponse>(observer => {
      // Run Firebase operations outside Angular zone
      this.ngZone.runOutsideAngular(() => {
        signInWithEmailAndPassword(this.auth, email, password)
          .then((userCredential: UserCredential) => {
            console.log('Firebase login successful:', userCredential);
            const firebaseUser = userCredential.user;

            // Fetch user data from Firestore
            this.fetchUserDataFromFirestore(firebaseUser.uid)
              .then(userData => {
                // Return to Angular zone for UI updates
                this.ngZone.run(() => {
                  // Get profile picture with fallback to null
                  const profilePicture = userData?.['profilePicture'] || null;

                  // Create response object
                  const response: LoginResponse = {
                    message: 'Login successful',
                    token: userCredential.user.refreshToken,
                    name: userData?.['fullName'] || `${userData?.['firstName'] || ''} ${userData?.['lastName'] || ''}`.trim() || firebaseUser.displayName || email,
                    email: firebaseUser.email || '',
                    id: firebaseUser.uid,
                    role: userData?.['role']?.toLowerCase() || 'patient',
                    success: true,
                    firstName: userData?.['firstName'] || '',
                    lastName: userData?.['lastName'] || '',
                    profilePicture: profilePicture
                  };

                  // Save user info to local storage
                  const userInfo: UserInfo = {
                    id: firebaseUser.uid,
                    email: firebaseUser.email || '',
                    full_name: response.name,
                    role: response.role,
                    profilePicture: profilePicture,
                    firstName: response.firstName,
                    lastName: response.lastName
                  };
                  this.storage.setItem(this.userInfoKey, userInfo);

                  // Set current patient in DB service if role is patient
                  if ((response.role?.toUpperCase() === 'PATIENT' || !response.role) && response.id) {
                    console.log('Setting current patient in DB after login:', response);
                    const patientUser: UserType = {
                      id: response.id,
                      email: response.email,
                      firstname: response.firstName || response.name?.split(' ')[0] || '',
                      lastname: response.lastName || (response.name?.split(' ').length > 1 ? response.name?.split(' ').slice(1).join(' ') : ''),
                      role: 'PATIENT',
                      password: '' // We don't store or need the password
                    };
                    this.db.setCurrentPatient(patientUser);
                    this.storage.setItem(CURRENT_PATIENT, patientUser);
                    console.log('Current patient set in DB after login:', patientUser);
                  }

                  observer.next(response);
                  observer.complete();
                });
              })
              .catch(error => {
                this.ngZone.run(() => {
                  console.error('Error fetching user data from Firestore after login:', error);
                  observer.error('Login failed: Error fetching user data');
                });
              });
          })
          .catch(error => {
            this.ngZone.run(() => {
              console.error('Firebase login error:', error);
              const userFriendlyMessage = this.authErrorMapper.mapFirebaseError(error);
              observer.error(userFriendlyMessage);
            });
          });
      });
    });
  }

  /**
   * Legacy login method for backward compatibility
   */
  private legacyLogin(email: string, password: string): Observable<LoginResponse> {
    // First check if we have existing user info in local storage that matches this email
    const userInfo = this.storage.getItem<UserInfo>(this.userInfoKey);
    if (userInfo && userInfo.email === email) {
      console.log('Found matching user info in local storage, attempting local login');
      const localLoginResult = this.loginWithLocalDb(email, password);
      if (localLoginResult) {
        return localLoginResult;
      }
    }

    const loginData = { email, password };
    const loginEndpoint = this.apiUrlService.getUrl('auth/login');

    console.log('Logging in at endpoint:', loginEndpoint);

    return this.http.post<LoginResponse>(loginEndpoint, loginData)
      .pipe(
        tap(response => {
          console.log('Login successful:', response);

          // Save the token
          if (response.token) {
            this.storage.setItem(this.tokenKey, response.token);
            console.log('Token saved to localStorage');
          } else {
            console.warn('No token received in login response');
          }

          // Save user ID if present
          if (response.id) {
            this.storage.setItem(this.userIdKey, response.id);
            console.log('User ID saved:', response.id);
          } else if (response.userId) {
            this.storage.setItem(this.userIdKey, response.userId);
            console.log('User ID saved from userId:', response.userId);
          }

          // Save patient ID if present
          if (response.patientId) {
            this.storage.setItem(this.patientIdKey, response.patientId);
            console.log('Patient ID saved:', response.patientId);
          } else if (response.role === 'PATIENT' && (response.id || response.userId)) {
            // For patients, if no explicit patientId, use the userId/id as patientId
            const userId = response.id || response.userId || '';
            if (userId) {
              this.storage.setItem(this.patientIdKey, userId);
              console.log('Patient ID saved from userId:', userId);
            }
          }

          // Get the latest profile data from the server after login
          this.fetchLatestUserProfile(response);
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('Login error:', error);

          // If there's a network error or the server is unavailable, try to use the local database
          if (error.status === 0 || error.status === 500) {
            console.log('Network error or server error, trying local database login');
            return this.loginWithLocalDb(email, password);
          }

          if (error.error instanceof ErrorEvent) {
            return throwError(() => new Error('Network error occurred'));
          }
          return throwError(() => error.error?.message || 'Login failed');
        })
      );
  }

  private fetchLatestUserProfile(loginResponse: LoginResponse) {
    // Get latest profile data from server
    const token = this.storage.getItem<string>(this.tokenKey);

    if (!token) {
      console.warn('No auth token available for profile fetch');
      this.handleFallbackUserInfo(loginResponse);
      return;
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    console.log('Fetching profile with token:', token);
    const profileEndpoint = this.apiUrlService.getUrl('patients/profile/info');
    console.log('Request URL:', profileEndpoint);

    this.http.get<any>(profileEndpoint, { headers })
      .pipe(
        catchError(error => {
          console.error('Error fetching latest profile:', error);

          // Specific handling for 404 errors
          if (error.status === 404) {
            console.warn('Patient profile endpoint not found (404). Using fallback user info.');
          } else if (error.status === 0) {
            console.warn('Network error when fetching profile. Backend may be unavailable.');
          } else {
            console.error(`Error ${error.status} when fetching profile: ${error.message}`);
          }

          this.handleFallbackUserInfo(loginResponse);
          return throwError(() => error);
        })
      )
      .subscribe({
        next: (response) => {
          console.log('Profile response:', response);
          if (response.success && response.data) {
            // Get saved IDs or use from response
            const userId = response.data.id || this.storage.getItem<string>(this.userIdKey) || undefined;
            const patientId = response.data.patientId || this.storage.getItem<string>(this.patientIdKey) || undefined;

            // Save IDs to localStorage
            if (userId) this.storage.setItem(this.userIdKey, userId);
            if (patientId) this.storage.setItem(this.patientIdKey, patientId);

            const userInfo: UserInfo = {
              id: userId,
              patientId: patientId,
              full_name: response.data.fullName,
              email: response.data.email,
              profilePicture: response.data.profilePicture || null,
              phoneNumber: response.data.phoneNumber,
              address: loginResponse.address,
              role: response.data.role
            };

            this.storage.setItem(this.userInfoKey, userInfo);
            this.currentUserSubject.next({
              displayName: response.data.fullName,
              profileImage: response.data.profilePicture,
              email: response.data.email,
              id: userId,
              patientId: patientId
            });

            // Set current patient in DB service if role is patient
            if ((response.data.role?.toUpperCase() === 'PATIENT' || !response.data.role) && userId) {
              console.log('Setting current patient in DB from profile fetch:', response.data);
              const patientUser: UserType = {
                id: userId,
                email: response.data.email,
                firstname: response.data.fullName.split(' ')[0] || '',
                lastname: response.data.fullName.split(' ').slice(1).join(' ') || '',
                role: 'PATIENT',
                password: '' // We don't store or need the password
              };
              this.db.setCurrentPatient(patientUser);
              this.storage.setItem(CURRENT_PATIENT, patientUser);
              console.log('Current patient set from profile fetch:', patientUser);
            }
          } else {
            this.handleFallbackUserInfo(loginResponse);
          }
        },
        error: (error) => {
          console.error('Error in profile subscription:', error);
          this.handleFallbackUserInfo(loginResponse);
        }
      });
  }

  private handleFallbackUserInfo(loginResponse: LoginResponse) {
    console.log('Using fallback user info from login response');

    // Fallback to login response data
    // Get saved IDs or use from login response
    const userId = loginResponse.id || loginResponse.userId || this.storage.getItem<string>(this.userIdKey) || undefined;
    const patientId = loginResponse.patientId || this.storage.getItem<string>(this.patientIdKey) || undefined;

    // Save IDs to localStorage
    if (userId) this.storage.setItem(this.userIdKey, userId);
    if (patientId) this.storage.setItem(this.patientIdKey, patientId);

    // For patient role, if no explicit patientId, use the userId/id as patientId
    if (loginResponse.role === 'PATIENT' && userId && !patientId) {
      this.storage.setItem(this.patientIdKey, userId);
      console.log('Set patient ID from user ID:', userId);
    }

    const userInfo: UserInfo = {
      id: userId,
      patientId: patientId || (loginResponse.role === 'PATIENT' ? userId : undefined),
      full_name: loginResponse.name || `${loginResponse.firstName || ''} ${loginResponse.lastName || ''}`.trim(),
      email: loginResponse.email,
      profilePicture: loginResponse.profilePicture || null,
      phoneNumber: loginResponse.phoneNumber,
      address: loginResponse.address,
      role: loginResponse.role
    };

    this.storage.setItem(this.userInfoKey, userInfo);
    this.currentUserSubject.next({
      displayName: userInfo.full_name,
      profileImage: userInfo.profilePicture !== undefined ? userInfo.profilePicture : null,
      email: userInfo.email,
      id: userInfo.id || '',
      patientId: userInfo.patientId
    });

    // Set current patient in DB service if role is patient
    if ((userInfo.role?.toUpperCase() === 'PATIENT' || !userInfo.role) && userInfo.id) {
      console.log('Setting current patient in DB from fallback user info:', userInfo);
      const patientUser: UserType = {
        id: userInfo.id,
        email: userInfo.email,
        firstname: userInfo.full_name.split(' ')[0] || '',
        lastname: userInfo.full_name.split(' ').slice(1).join(' ') || '',
        role: 'PATIENT',
        password: '' // We don't store or need the password
      };
      this.db.setCurrentPatient(patientUser);
      this.storage.setItem(CURRENT_PATIENT, patientUser);
      console.log('Current patient set from fallback user info:', patientUser);
    }
  }

  async getCurrentUser(): Promise<User | null> {
    // First check if we have a user in the currentUserSubject
    const currentUser = this.currentUserSubject.getValue();
    if (currentUser) {
      return currentUser;
    }

    // Then check if we have a Firebase user
    const firebaseUser = this.auth.currentUser;
    if (firebaseUser) {
      try {
        // Fetch additional user data from Firestore
        const userData = await this.fetchUserDataFromFirestore(firebaseUser.uid);

        // Create user info
        const userInfo: UserInfo = {
          id: firebaseUser.uid,
          email: firebaseUser.email || '',
          full_name: userData?.['fullName'] || firebaseUser.displayName || '',
          profilePicture: userData?.['profilePicture'] || null,
          patientId: userData?.['patientId'] || firebaseUser.uid,
          role: userData?.['role'] || 'patient'
        };

        // Create user for currentUserSubject
        const user: User = {
          id: userInfo.id || '',  // Ensure id is not undefined
          email: userInfo.email,
          displayName: userInfo.full_name,
          profileImage: userInfo.profilePicture !== undefined ? userInfo.profilePicture : null,
          patientId: userInfo.patientId
        };

        // Update storage and subject
        this.storage.setItem(this.userInfoKey, userInfo);
        this.currentUserSubject.next(user);

        return user;
      } catch (error) {
        console.error('Error fetching user data from Firestore:', error);
      }
    }

    // Finally, try to get from local storage
    const userInfo = this.getUserInfo();
    if (userInfo) {
      const user: User = {
        id: userInfo.id || '',  // Ensure id is not undefined
        email: userInfo.email,
        displayName: userInfo.full_name,
        profileImage: userInfo.profilePicture !== undefined ? userInfo.profilePicture : null,
        patientId: userInfo.patientId
      };
      return user;
    }

    return null;
  }

  getUserInfo(): UserInfo | null {
    // First try to get from localStorage
    const userInfo = this.storage.getItem<UserInfo>(this.userInfoKey);
    if (userInfo) {
      return userInfo;
    }

    // If not in localStorage, try to get from the BehaviorSubject
    const currentUser = this.currentUserSubject.getValue();
    if (currentUser) {
      return {
        id: currentUser.id || '',  // Ensure id is not undefined
        patientId: currentUser.patientId,
        full_name: currentUser.displayName,
        email: currentUser.email,
        profilePicture: currentUser.profileImage,
        role: 'patient'
      };
    }

    return null;
  }

  getCurrentUserInfo(): UserInfo | null {
    // First check if we have a user in the behavior subject
    const currentUser = this.currentUserSubject.value;
    if (currentUser) {
      // Convert User to UserInfo
      return {
        id: currentUser.id || '',  // Ensure id is not undefined
        email: currentUser.email,
        full_name: currentUser.displayName,
        profilePicture: currentUser.profileImage,
        patientId: currentUser.patientId,
        role: 'patient'
      };
    }

    // Finally, check local storage
    return this.storage.getItem<UserInfo>(this.userInfoKey);
  }

  getUserId(): string | null {
    return this.storage.getItem<string>(this.userIdKey) || this.getCurrentUserInfo()?.id || null;
  }

  getPatientId(): string | null {
    // Check both current and legacy keys
    return this.storage.getItem<string>(this.patientIdKey) ||
           this.storage.getItem<string>(this.legacyPatientIdKey) ||
           this.getCurrentUserInfo()?.patientId ||
           null;
  }

  saveUserInfo(userInfo: UserInfo): void {
    console.log('Saving user info:', userInfo);
    // Save IDs separately for easy access
    if (userInfo.id) {
      this.storage.setItem(this.userIdKey, userInfo.id);
    }

    if (userInfo.patientId) {
      this.storage.setItem(this.patientIdKey, userInfo.patientId);
    }

    this.storage.setItem(this.userInfoKey, userInfo);
    this.currentUserSubject.next({
      displayName: userInfo.full_name,
      profileImage: userInfo.profilePicture !== undefined ? userInfo.profilePicture : null,
      email: userInfo.email,
      id: userInfo.id || '',
      patientId: userInfo.patientId
    });
  }

  clearUserInfo(): void {
    this.storage.removeItem(this.userInfoKey);
    this.storage.removeItem(this.tokenKey);
    this.storage.removeItem(this.userIdKey);
    this.storage.removeItem(this.patientIdKey);
    this.storage.removeItem(this.legacyPatientIdKey);
    this.currentUserSubject.next(null);
  }

  register(registerData: RegisterRequest): Observable<RegisterResponse> {
    console.log('Registering with Firebase:', registerData.email);

    // Use Firebase authentication to create the user
    return from(createUserWithEmailAndPassword(this.auth, registerData.email, registerData.password))
      .pipe(
        switchMap((userCredential: UserCredential) => {
          console.log('Firebase registration successful:', userCredential);
          const firebaseUser = userCredential.user;

          // Update the user profile with display name
          const displayName = `${registerData.firstName} ${registerData.lastName}`;
          return from(updateProfile(firebaseUser, { displayName }))
            .pipe(
              switchMap(() => {
                // Create user document in Firestore
                const userData = {
                  firstName: registerData.firstName,
                  lastName: registerData.lastName,
                  email: registerData.email,
                  role: registerData.role.toUpperCase(),
                  createdAt: new Date().toISOString(),
                  profilePicture: null,
                  phoneNumber: '',
                  address: ''
                };

                const userDocRef = doc(this.firestore, 'users', firebaseUser.uid);

                // Create patient document if role is PATIENT
                let patientDocPromise = Promise.resolve<null>(null);
                let patientId = firebaseUser.uid;

                if (registerData.role.toUpperCase() === 'PATIENT') {
                  const patientData = {
                    userId: firebaseUser.uid,
                    firstName: registerData.firstName,
                    lastName: registerData.lastName,
                    email: registerData.email,
                    fullName: displayName,
                    createdAt: new Date().toISOString(),
                    medicalRecords: []
                  };

                  const patientDocRef = doc(this.firestore, 'patients', firebaseUser.uid);
                  patientDocPromise = setDoc(patientDocRef, patientData).then(() => null);
                }

                // Save user data to Firestore
                return from(Promise.all([
                  setDoc(userDocRef, userData),
                  patientDocPromise
                ])).pipe(
                  map(() => {
                    // Create register response
                    const response: RegisterResponse = {
                      message: 'Registration successful',
                      email: registerData.email,
                      id: firebaseUser.uid,
                      userId: firebaseUser.uid,
                      role: registerData.role.toUpperCase(),
                      name: displayName,
                      token: firebaseUser.refreshToken
                    };

                    // Set patient ID if applicable
                    if (registerData.role.toUpperCase() === 'PATIENT') {
                      response.patientId = patientId;
                    }

                    // Save user info to localStorage
                    const userInfo: UserInfo = {
                      id: firebaseUser.uid,
                      patientId: response.patientId,
                      full_name: displayName,
                      email: registerData.email,
                      profilePicture: null
                    };

                    this.storage.setItem(this.userInfoKey, userInfo);

                    // Update current user subject
                    this.currentUserSubject.next({
                      displayName: userInfo.full_name,
                      profileImage: null,
                      email: userInfo.email,
                      id: userInfo.id || '',
                      patientId: userInfo.patientId
                    });

                    // Save tokens for backward compatibility
                    this.storage.setItem(this.tokenKey, firebaseUser.refreshToken);
                    this.storage.setItem(this.userIdKey, firebaseUser.uid);
                    if (response.patientId) {
                      this.storage.setItem(this.patientIdKey, response.patientId);
                      this.storage.setItem(this.legacyPatientIdKey, response.patientId);
                    }

                    // Set current patient in DB service if role is patient
                    if (registerData.role.toUpperCase() === 'PATIENT' && response.id) {
                      console.log('Setting current patient in DB after registration:', response);
                      const patientUser: UserType = {
                        id: response.id,
                        email: response.email,
                        firstname: registerData.firstName,
                        lastname: registerData.lastName,
                        role: 'PATIENT',
                        password: '' // We don't store or need the password
                      };
                      this.db.setCurrentPatient(patientUser);
                      this.storage.setItem(CURRENT_PATIENT, patientUser);
                      console.log('Current patient set in DB after registration:', patientUser);
                    }

                    return response;
                  })
                );
              })
            );
        }),
        catchError((error) => {
          console.error('Firebase registration error:', error);

          // For network errors or if Firebase is unavailable, try legacy registration
          if (error.code === 'auth/network-request-failed') {
            console.log('Firebase network error, trying legacy registration');
            return this.legacyRegister(registerData);
          }

          // Map Firebase error codes to user-friendly messages
          const userFriendlyMessage = this.authErrorMapper.mapRegistrationError(error);
          return throwError(() => ({ error: { message: userFriendlyMessage } }));
        })
      );
  }

  /**
   * Legacy register method for backward compatibility
   */
  private legacyRegister(registerData: RegisterRequest): Observable<RegisterResponse> {
    const registerEndpoint = this.apiUrlService.getUrl('auth/register');

    console.log('Registering at endpoint:', registerEndpoint);

    return this.http.post<RegisterResponse>(registerEndpoint, registerData)
      .pipe(
        tap(response => {
          console.log('Registration response:', response);

          // Save the token if provided
          if (response.token) {
            this.storage.setItem(this.tokenKey, response.token);
          }

          // Save user ID from the response (could be id or userId)
          const userId = response.userId || response.id;
          if (userId) {
            this.storage.setItem(this.userIdKey, userId);
            console.log('User ID saved:', userId);
          }

          // For patient role, the patient ID is the same as the user ID
          if (response.role === 'PATIENT') {
            if (userId) {
              this.storage.setItem(this.patientIdKey, userId);
              console.log('Patient ID saved (from userId):', userId);
            } else if (response.patientId) {
              this.storage.setItem(this.patientIdKey, response.patientId);
              console.log('Patient ID saved (from patientId):', response.patientId);
            }
          }

          // Create a basic user info object
          const userInfo: UserInfo = {
            id: userId,
            patientId: response.role === 'PATIENT' ? (response.patientId || userId) : undefined,
            full_name: `${registerData.firstName} ${registerData.lastName}`,
            email: response.email || registerData.email,
            profilePicture: null
          };

          // Save the user info
          this.storage.setItem(this.userInfoKey, userInfo);

          // Update the current user subject
          this.currentUserSubject.next({
            displayName: userInfo.full_name,
            profileImage: null,
            email: userInfo.email,
            id: userInfo.id || '',
            patientId: userInfo.patientId
          });
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('Registration error:', error);

          // If there's a network error or the server is unavailable, try to use the local database
          if (error.status === 0) {
            console.log('Network error or server unavailable, trying local database registration');
            return this.registerWithLocalDb(registerData);
          }

          return this.handleError(error);
        })
      );
  }

  logout(): Observable<void> {
    console.log('Logging out user');

    // Clear local storage
    this.clearUserInfo();

    // Sign out from Firebase
    return from(signOut(this.auth))
      .pipe(
        tap(() => {
          console.log('Firebase logout successful');
        }),
        catchError(error => {
          console.error('Firebase logout error:', error);
          return of(undefined);
        })
      );
  }

  updateProfile(profileData: any): Observable<ProfileUpdateResponse> {
    const userInfo = this.getCurrentUserInfo();
    const isPatient = userInfo?.patientId !== undefined;
    const endpoint = isPatient
      ? this.apiUrlService.getUrl('patients/profile/update')
      : this.apiUrlService.getUrl('doctors/profile/update');

    console.log(`Updating profile for ${isPatient ? 'patient' : 'doctor'} at endpoint: ${endpoint}`);

    // Get the auth token
    const token = this.getAuthToken();
    if (!token) {
      return throwError(() => new Error('Authentication token not found'));
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // For patients, we can send JSON directly
    if (isPatient) {
      return this.http.post<ProfileUpdateResponse>(endpoint, profileData, { headers })
        .pipe(
          tap(response => {
            console.log('Profile update response:', response);
            if (response.user) {
              this.updateUserInfoFromProfile(response.user);
            }
          }),
          catchError((error: HttpErrorResponse) => {
            console.error('Profile update error:', error);
            if (error.error instanceof ErrorEvent) {
              return throwError(() => new Error('Network error occurred'));
            }
            return throwError(() => error.error?.message || 'Profile update failed');
          })
        );
    }

    // For doctors, we continue to use FormData
    return this.http.post<ProfileUpdateResponse>(endpoint, profileData)
      .pipe(
        tap(response => {
          console.log('Profile update response:', response);
          if (response.user) {
            this.updateUserInfoFromProfile(response.user);
          }
        }),
        catchError((error: HttpErrorResponse) => {
          console.error('Profile update error:', error);
          if (error.error instanceof ErrorEvent) {
            return throwError(() => new Error('Network error occurred'));
          }
          return throwError(() => error.error?.message || 'Profile update failed');
        })
      );
  }

  // Update user info from profile changes
  updateUserInfoFromProfile(profile: any): void {
    const currentUserInfo = this.getCurrentUserInfo();

    if (currentUserInfo) {
      // Extract first and last name from full name
      let firstName, lastName;

      if (profile.fullName) {
        const names = profile.fullName.split(' ');
        firstName = names[0];
        lastName = names.length > 1 ? names.slice(1).join(' ') : '';
      } else {
        firstName = profile.firstName || currentUserInfo.full_name.split(' ')[0];
        lastName = profile.lastName || (currentUserInfo.full_name.split(' ').length > 1 ?
                   currentUserInfo.full_name.split(' ').slice(1).join(' ') : '');
      }

      const updatedUserInfo: UserInfo = {
        ...currentUserInfo,
        full_name: `${firstName} ${lastName}`,
        profilePicture: profile.profilePicture || currentUserInfo.profilePicture,
        phoneNumber: profile.phoneNumber || currentUserInfo.phoneNumber,
        address: profile.address || currentUserInfo.address
      };

      // Save the updated user info
      this.storage.setItem(this.userInfoKey, updatedUserInfo);

      // Update the current user subject
      this.currentUserSubject.next({
        displayName: updatedUserInfo.full_name,
        profileImage: updatedUserInfo.profilePicture !== undefined ? updatedUserInfo.profilePicture : null,
        email: updatedUserInfo.email,
        id: updatedUserInfo.id || '',
        patientId: updatedUserInfo.patientId
      });

      console.log('User info updated from profile:', updatedUserInfo);
    }
  }

  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/change-password`, {
      currentPassword,
      newPassword
    }).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.error instanceof ErrorEvent) {
          return throwError(() => new Error('Network error occurred'));
        }
        return throwError(() => error.error.message || 'Password change failed');
      })
    );
  }

  verifyCurrentPassword(password: string): Observable<any> {
    return this.http.post(`${this.baseUrl}/verify-password`, { password })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          if (error.error instanceof ErrorEvent) {
            return throwError(() => new Error('Network error occurred'));
          }
          return throwError(() => error.error.message || 'Password verification failed');
        })
      );
  }

  isLoggedIn(): boolean {
    return this.currentUserSubject.value !== null;
  }

  // Sync user data with backend
  private syncUserDataWithBackend(
    firstName: string,
    lastName: string,
    email: string,
    phoneNumber: string,
    profilePicture?: string
  ): void {
    const token = this.getAuthToken();

    if (!token) {
      console.error('Cannot sync user data: No auth token. Please log in again.');
      return;
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });

    const userData = {
      firstName,
      lastName,
      email,
      phoneNumber,
      profilePicture
    };

    // Log the data we're sending for debugging
    console.log('Syncing user data with backend:', userData);

    // Use the patients/profile/update endpoint
    const syncEndpoint = this.apiUrlService.getUrl('patients/profile/update');
    console.log('Sync endpoint:', syncEndpoint);

    this.http.post(syncEndpoint, userData, { headers })
      .pipe(
        tap(response => console.log('Backend sync response:', response)),
        catchError((error: HttpErrorResponse) => {
          console.error('Failed to sync user data with backend', error);
          return of(null);
        })
      )
      .subscribe();
  }

  getAuthToken(): string | null {
    const token = this.storage.getItem<string>(this.tokenKey);
    console.log('Getting auth token:', token ? 'Token exists' : 'No token found');
    return token;
  }

  createPatientRecord(userId: string, firstName: string, lastName: string, email: string): Observable<any> {
    console.log('Creating patient record for user ID:', userId);

    const patientData = {
      userId: userId,
      firstName: firstName,
      lastName: lastName,
      email: email
    };

    const createPatientEndpoint = this.apiUrlService.getUrl('patients/create');
    console.log('Create patient endpoint:', createPatientEndpoint);

    return this.http.post<any>(createPatientEndpoint, patientData)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error creating patient record:', error);
          return throwError(() => error.error.message || 'Failed to create patient record');
        })
      );
  }

  fetchPatientIdByUserId(userId: string): Observable<string> {
    console.log('Fetching patient ID for user ID:', userId);

    const fetchPatientIdEndpoint = this.apiUrlService.getUrl(`patients/by-user/${userId}`);
    console.log('Fetch patient ID endpoint:', fetchPatientIdEndpoint);

    return this.http.get<any>(fetchPatientIdEndpoint)
      .pipe(
        map(response => response.patientId),
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching patient ID:', error);
          return throwError(() => error.error.message || 'Failed to fetch patient ID');
        })
      );
  }

  private handleError(error: HttpErrorResponse) {
    console.error('API Error:', error);

    if (error.error instanceof ErrorEvent || error.status === 0) {
      console.error('Client-side error:', error.error?.message || 'Network connection error');
      return throwError(() => new Error('Unable to connect to the server. Please check your internet connection and try again.'));
    }

    if (error.error && typeof error.error === 'object') {
      if (error.error.message?.toLowerCase().includes('email already exists') ||
          error.error.error?.toLowerCase().includes('email already exists')) {
        return throwError(() => ({ error: { message: 'Email already exists' } }));
      }

      return throwError(() => error);
    }

    return throwError(() => new Error('Something went wrong. Please try again later.'));
  }

  private loginWithLocalDb(email: string, password: string): Observable<LoginResponse> {
    console.log('Attempting login with local database');

    // Check if db service is available
    if (!this.db) {
      console.error('Db service not available for offline login');
      return throwError(() => new Error('Offline functionality not available'));
    }

    try {
      // Try to find the user in the local database
      const user = this.db.login(email, password);

      if (user) {
        console.log('Local database login successful:', user);

        // Create a response object similar to what the API would return
        const response: LoginResponse = {
          message: 'Login successful',
          token: 'med_secure_offline_token',  // Updated dummy token name for consistency
          name: `${user.firstname} ${user.lastname}`,
          email: user.email,
          id: user.id,
          userId: user.id,
          role: user.role,
          firstName: user.firstname,
          lastName: user.lastname,
          success: true
        };

        // If the user is a patient, set the patient ID
        if (user.role.toUpperCase() === 'PATIENT') {
          // Find the patient record for this user
          const patient = this.db.patientTable().find((p: any) => p.user_id === user.id);
          if (patient) {
            response.patientId = patient.id;
          } else {
            // For patients, if no explicit patientId, use the userId as patientId
            response.patientId = user.id;
          }
        }

        // Save user info to localStorage
        const userInfo: UserInfo = {
          id: user.id,
          patientId: response.patientId,
          full_name: response.name,
          email: user.email,
          profilePicture: null
        };

        this.storage.setItem(this.userInfoKey, userInfo);

        // Return the response as an Observable
        return of(response);
      } else {
        console.log('Local database login failed');
        return throwError(() => new Error('Invalid email or password'));
      }
    } catch (error) {
      console.error('Error during local database login:', error);
      return throwError(() => new Error('Login failed. Please try again.'));
    }
  }

  private registerWithLocalDb(registerData: RegisterRequest): Observable<RegisterResponse> {
    console.log('Attempting registration with local database');

    // Check if db service is available
    if (!this.db) {
      console.error('Db service not available for offline registration');
      return throwError(() => new Error('Offline functionality not available'));
    }

    // Check if email already exists
    const existingUser = this.db.userTable().find((u: UserType) => u.email === registerData.email);
    if (existingUser) {
      console.log('Email already exists in local database');
      return throwError(() => ({ error: { message: 'Email already exists' } }));
    }

    // Generate a new user ID
    const userId = this.db.generateId();

    // Create a new user in the local database
    const newUser: UserType = {
      id: userId,
      email: registerData.email,
      password: registerData.password,
      firstname: registerData.firstName,
      lastname: registerData.lastName,
      role: registerData.role.toUpperCase() as 'DOCTOR' | 'PATIENT'
    };

    // Add the user to the database
    const updatedUserTable = [...this.db.userTable(), newUser];
    this.db.userTable.set(updatedUserTable);
    this.db.storage.setItem('USER_TABLE', updatedUserTable);

    // If the user is a patient, create a patient record
    let patientId = userId;
    if (registerData.role.toUpperCase() === 'PATIENT') {
      patientId = this.db.generateId();
      const newPatient: PatientType = {
        id: patientId,
        user_id: userId,
        image: '',
        contact: ''
      };

      const updatedPatientTable = [...this.db.patientTable(), newPatient];
      this.db.patientTable.set(updatedPatientTable);
      this.db.storage.setItem('PATIENT_TABLE', updatedPatientTable);
    }

    // Create a response object similar to what the API would return
    const response: RegisterResponse = {
      message: 'Registration successful',
      email: registerData.email,
      id: userId,
      userId: userId,
      patientId: registerData.role.toUpperCase() === 'PATIENT' ? patientId : undefined,
      role: registerData.role.toUpperCase(),
      offline: true
    };

    // Save user info to localStorage
    const userInfo: UserInfo = {
      id: userId,
      patientId: registerData.role.toUpperCase() === 'PATIENT' ? patientId : undefined,
      full_name: `${registerData.firstName} ${registerData.lastName}`,
      email: registerData.email,
      profilePicture: null
    };

    this.storage.setItem(this.userInfoKey, userInfo);

    // Return the response as an Observable
    return of(response);
  }
}
