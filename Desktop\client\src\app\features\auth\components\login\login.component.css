.error-message {
  background: #fef2f2;
  color: #dc2626;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  margin: 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message i {
  position: static;
  transform: none;
}

.spin {
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  padding: 2rem;
}

.login-card {
  width: 100%;
  max-width: 480px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  padding: 3rem 2.5rem;
  position: relative;
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-card i.bi-person {
  font-size: 2.5rem;
  color: #199a8e;
  margin-bottom: 1rem;
}

.bi-person {
  font-size: 50px;
  width: 70px;
  height: 70px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #E6F7F5;
  color: #199A8E;
  padding: 15px;
  margin: 0 auto 1.5rem;
}

.login-card h2 {
  color: #111827;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.login-card p {
  color: #6b7280;
  margin-bottom: 2rem;
}

.input-container {
  position: relative;
  width: 100%;
}

.form-group {
  position: relative;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
}

.form-group i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #199a8e;
  font-size: 1.1rem;
  z-index: 1;
  pointer-events: none;
}

.form-group input {
  width: 100%;
  padding: 0.875rem 1rem 0.875rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  color: #111827;
  transition: all 0.2s;
  background-color: #f9fafb;
  height: 48px;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #199a8e;
  box-shadow: 0 0 0 3px rgba(25, 154, 142, 0.1);
  background-color: white;
}

.form-group input::placeholder {
  color: #6b7280;
}

.validation-error {
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
}

.remember-forgot label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
}

.remember-forgot input[type="checkbox"] {
  accent-color: #199a8e;
}

.remember-forgot a {
  color: #199a8e;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.remember-forgot a:hover {
  color: #147d73;
}

.login-btn {
  width: 100%;
  padding: 0.875rem;
  background: linear-gradient(90deg, #199a8e, #1fb5a6);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 154, 142, 0.15);
}

.login-btn .spinner {
  animation: spin 1s linear infinite;
}

.admin-login-btn {
  width: 100%;
  padding: 0.875rem;
  background: white;
  color: #199a8e;
  border: 2px solid #199a8e;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.admin-login-btn:hover {
  background: rgba(25, 154, 142, 0.05);
}

.register-link {
  text-align: center;
  margin-top: 1.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.register-link a {
  color: #199a8e;
  text-decoration: none;
  font-weight: 500;
  margin-left: 0.25rem;
}

.register-link a:hover {
  text-decoration: underline;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Responsive Design */
@media screen and (max-width: 1200px) {
  .login-card {
    max-width: 440px;
    padding: 2.5rem 2rem;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    padding: 1.5rem;
  }

  .login-card {
    max-width: 400px;
    padding: 2rem 1.5rem;
  }

  .bi-person {
    font-size: 40px;
    width: 60px;
    height: 60px;
    padding: 12px;
    margin-bottom: 1.25rem;
  }

  .login-card h2 {
    font-size: 1.4rem;
  }

  .login-card p {
    font-size: 0.95rem;
    margin-bottom: 1.75rem;
  }

  .form-group {
    margin-bottom: 1.25rem;
  }

  .form-group input {
    padding: 0.75rem 1rem 0.75rem 2.25rem;
    height: 44px;
    font-size: 0.85rem;
  }

  .form-group i {
    font-size: 1rem;
  }

  .remember-forgot {
    font-size: 0.8rem;
    margin-bottom: 1.25rem;
  }

  .login-btn {
    height: 44px;
    font-size: 0.95rem;
    padding: 0.75rem;
  }
}

@media screen and (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    max-width: 100%;
    padding: 1.75rem 1.25rem;
  }

  .bi-person {
    font-size: 35px;
    width: 55px;
    height: 55px;
    padding: 10px;
    margin-bottom: 1rem;
  }

  .login-card h2 {
    font-size: 1.3rem;
  }

  .login-card p {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group input {
    padding: 0.7rem 1rem 0.7rem 2.25rem;
    height: 42px;
    font-size: 0.8rem;
  }

  .remember-forgot {
    flex-direction: column;
    gap: 0.75rem;
    align-items: flex-start;
    margin-bottom: 1.5rem;
  }

  .error-message {
    font-size: 0.8rem;
    padding: 0.6rem 0.8rem;
  }
}

@media screen and (max-width: 320px) {
  .login-card {
    padding: 1.5rem 1rem;
  }

  .bi-person {
    font-size: 30px;
    width: 50px;
    height: 50px;
    padding: 8px;
  }

  .login-card h2 {
    font-size: 1.2rem;
  }

  .login-card p {
    font-size: 0.85rem;
  }

  .form-group input {
    padding: 0.65rem 1rem 0.65rem 2rem;
    height: 40px;
  }

  .login-btn {
    height: 40px;
    font-size: 0.9rem;
    padding: 0.65rem;
  }
}
