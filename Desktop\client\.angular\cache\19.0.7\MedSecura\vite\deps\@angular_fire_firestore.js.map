{"version": 3, "sources": ["../../../../../../node_modules/rxfire/firestore/index.esm.js", "../../../../../../node_modules/@angular/fire/fesm2022/angular-fire-firestore.mjs"], "sourcesContent": ["import { onSnapshot, refEqual, getCountFromServer } from 'firebase/firestore';\nimport { Observable, from, pipe } from 'rxjs';\nimport { map, scan, distinctUntilChanged, filter, startWith, pairwise } from 'rxjs/operators';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\n\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/**\n * @license\n * Copyright 2018 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar DEFAULT_OPTIONS = {\n  includeMetadataChanges: false\n};\nfunction fromRef(ref, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  /* eslint-enable @typescript-eslint/no-explicit-any */\n  return new Observable(function (subscriber) {\n    var unsubscribe = onSnapshot(ref, options, {\n      next: subscriber.next.bind(subscriber),\n      error: subscriber.error.bind(subscriber),\n      complete: subscriber.complete.bind(subscriber)\n    });\n    return {\n      unsubscribe: unsubscribe\n    };\n  });\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction doc(ref) {\n  return fromRef(ref, {\n    includeMetadataChanges: true\n  });\n}\n/**\n * Returns a stream of a document, mapped to its data payload and optionally the document ID\n * @param query\n * @param options\n */\nfunction docData(ref, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return doc(ref).pipe(map(function (snap) {\n    return snapToData(snap, options);\n  }));\n}\nfunction snapToData(snapshot, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var data = snapshot.data(options);\n  // match the behavior of the JS SDK when the snapshot doesn't exist\n  // it's possible with data converters too that the user didn't return an object\n  if (!snapshot.exists() || typeof data !== 'object' || data === null || !options.idField) {\n    return data;\n  }\n  return __assign(__assign({}, data), (_a = {}, _a[options.idField] = snapshot.id, _a));\n}\n\n/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar ALL_EVENTS = ['added', 'modified', 'removed'];\n/**\n * Create an operator that determines if a the stream of document changes\n * are specified by the event filter. If the document change type is not\n * in specified events array, it will not be emitted.\n */\nvar filterEvents = function (events) {\n  return filter(function (changes) {\n    var hasChange = false;\n    for (var i = 0; i < changes.length; i++) {\n      var change = changes[i];\n      if (events && events.indexOf(change.type) >= 0) {\n        hasChange = true;\n        break;\n      }\n    }\n    return hasChange;\n  });\n};\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount) {\n  var args = [];\n  for (var _i = 3; _i < arguments.length; _i++) {\n    args[_i - 3] = arguments[_i];\n  }\n  var returnArray = original.slice();\n  returnArray.splice.apply(returnArray, __spreadArray([start, deleteCount], args, false));\n  return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * @param combined\n * @param change\n */\nfunction processIndividualChange(combined, change) {\n  switch (change.type) {\n    case 'added':\n      if (combined[change.newIndex] && refEqual(combined[change.newIndex].doc.ref, change.doc.ref)) ;else {\n        return sliceAndSplice(combined, change.newIndex, 0, change);\n      }\n      break;\n    case 'modified':\n      if (combined[change.oldIndex] == null || refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n        // When an item changes position we first remove it\n        // and then add it's new position\n        if (change.oldIndex !== change.newIndex) {\n          var copiedArray = combined.slice();\n          copiedArray.splice(change.oldIndex, 1);\n          copiedArray.splice(change.newIndex, 0, change);\n          return copiedArray;\n        } else {\n          return sliceAndSplice(combined, change.newIndex, 1, change);\n        }\n      }\n      break;\n    case 'removed':\n      if (combined[change.oldIndex] && refEqual(combined[change.oldIndex].doc.ref, change.doc.ref)) {\n        return sliceAndSplice(combined, change.oldIndex, 1);\n      }\n      break;\n  }\n  return combined;\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n * @param current\n * @param changes\n * @param events\n */\nfunction processDocumentChanges(current, changes, events) {\n  if (events === void 0) {\n    events = ALL_EVENTS;\n  }\n  changes.forEach(function (change) {\n    // skip unwanted change types\n    if (events.indexOf(change.type) > -1) {\n      current = processIndividualChange(current, change);\n    }\n  });\n  return current;\n}\n/**\n * Create an operator that allows you to compare the current emission with\n * the prior, even on first emission (where prior is undefined).\n */\nvar windowwise = function () {\n  return pipe(startWith(undefined), pairwise());\n};\n/**\n * Given two snapshots does their metadata match?\n * @param a\n * @param b\n */\nvar metaDataEquals = function (a, b) {\n  return JSON.stringify(a.metadata) === JSON.stringify(b.metadata);\n};\n/**\n * Create an operator that filters out empty changes. We provide the\n * ability to filter on events, which means all changes can be filtered out.\n * This creates an empty array and would be incorrect to emit.\n */\nvar filterEmptyUnlessFirst = function () {\n  return pipe(windowwise(), filter(function (_a) {\n    var prior = _a[0],\n      current = _a[1];\n    return current.length > 0 || prior === undefined;\n  }), map(function (_a) {\n    var current = _a[1];\n    return current;\n  }));\n};\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n * @param query\n */\nfunction collectionChanges(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return fromRef(query, {\n    includeMetadataChanges: true\n  }).pipe(windowwise(), map(function (_a) {\n    var priorSnapshot = _a[0],\n      currentSnapshot = _a[1];\n    var docChanges = currentSnapshot.docChanges();\n    if (priorSnapshot && !metaDataEquals(priorSnapshot, currentSnapshot)) {\n      // the metadata has changed, docChanges() doesn't return metadata events, so let's\n      // do it ourselves by scanning over all the docs and seeing if the metadata has changed\n      // since either this docChanges() emission or the prior snapshot\n      currentSnapshot.docs.forEach(function (currentDocSnapshot, currentIndex) {\n        var currentDocChange = docChanges.find(function (c) {\n          return refEqual(c.doc.ref, currentDocSnapshot.ref);\n        });\n        if (currentDocChange) {\n          // if the doc is in the current changes and the metadata hasn't changed this doc\n          if (metaDataEquals(currentDocChange.doc, currentDocSnapshot)) {\n            return;\n          }\n        } else {\n          // if there is a prior doc and the metadata hasn't changed skip this doc\n          var priorDocSnapshot = priorSnapshot === null || priorSnapshot === void 0 ? void 0 : priorSnapshot.docs.find(function (d) {\n            return refEqual(d.ref, currentDocSnapshot.ref);\n          });\n          if (priorDocSnapshot && metaDataEquals(priorDocSnapshot, currentDocSnapshot)) {\n            return;\n          }\n        }\n        docChanges.push({\n          oldIndex: currentIndex,\n          newIndex: currentIndex,\n          type: 'modified',\n          doc: currentDocSnapshot\n        });\n      });\n    }\n    return docChanges;\n  }), filterEvents(options.events || ALL_EVENTS), filterEmptyUnlessFirst());\n}\n/**\n * Return a stream of document snapshots on a query. These results are in sort order.\n * @param query\n */\nfunction collection(query) {\n  return fromRef(query, {\n    includeMetadataChanges: true\n  }).pipe(map(function (changes) {\n    return changes.docs;\n  }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n * @param query\n */\nfunction sortedChanges(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collectionChanges(query, options).pipe(scan(function (current, changes) {\n    return processDocumentChanges(current, changes, options.events);\n  }, []), distinctUntilChanged());\n}\n/**\n * Create a stream of changes as they occur it time. This method is similar\n * to docChanges() but it collects each event in an array over time.\n */\nfunction auditTrail(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collectionChanges(query, options).pipe(scan(function (current, action) {\n    return __spreadArray(__spreadArray([], current, true), action, true);\n  }, []));\n}\n/**\n * Returns a stream of documents mapped to their data payload, and optionally the document ID\n * @param query\n * @param options\n */\nfunction collectionData(query, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return collection(query).pipe(map(function (arr) {\n    return arr.map(function (snap) {\n      return snapToData(snap, options);\n    });\n  }));\n}\nfunction collectionCountSnap(query) {\n  return from(getCountFromServer(query));\n}\nfunction collectionCount(query) {\n  return collectionCountSnap(query).pipe(map(function (snap) {\n    return snap.data().count;\n  }));\n}\nexport { auditTrail, collection, collectionChanges, collectionCount, collectionCountSnap, collectionData, doc, docData, fromRef, snapToData, sortedChanges };\n", "import { ɵgetAllInstancesOf as _getAllInstancesOf, ɵgetDefaultInstanceOf as _getDefaultInstanceOf, VERSION, ɵAngularFireSchedulers as _AngularFireSchedulers, ɵzoneWrap as _zoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, NgModule, makeEnvironmentProviders, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { AppCheckInstances } from '@angular/fire/app-check';\nimport { AuthInstances } from '@angular/fire/auth';\nimport { registerVersion } from 'firebase/app';\nimport { auditTrail as auditTrail$1, collection as collection$1, collectionChanges as collectionChanges$1, collectionCount as collectionCount$1, collectionCountSnap as collectionCountSnap$1, collectionData as collectionData$1, doc as doc$1, docData as docData$1, fromRef as fromRef$1, snapToData as snapToData$1, sortedChanges as sortedChanges$1 } from 'rxfire/firestore';\nimport { addDoc as addDoc$1, aggregateFieldEqual as aggregateFieldEqual$1, aggregateQuerySnapshotEqual as aggregateQuerySnapshotEqual$1, and as and$1, clearIndexedDbPersistence as clearIndexedDbPersistence$1, collection as collection$2, collectionGroup as collectionGroup$1, connectFirestoreEmulator as connectFirestoreEmulator$1, deleteAllPersistentCacheIndexes as deleteAllPersistentCacheIndexes$1, deleteDoc as deleteDoc$1, deleteField as deleteField$1, disableNetwork as disableNetwork$1, disablePersistentCacheIndexAutoCreation as disablePersistentCacheIndexAutoCreation$1, doc as doc$2, documentId as documentId$1, enableIndexedDbPersistence as enableIndexedDbPersistence$1, enableMultiTabIndexedDbPersistence as enableMultiTabIndexedDbPersistence$1, enableNetwork as enableNetwork$1, enablePersistentCacheIndexAutoCreation as enablePersistentCacheIndexAutoCreation$1, endAt as endAt$1, endBefore as endBefore$1, getAggregateFromServer as getAggregateFromServer$1, getCountFromServer as getCountFromServer$1, getDoc as getDoc$1, getDocFromCache as getDocFromCache$1, getDocFromServer as getDocFromServer$1, getDocs as getDocs$1, getDocsFromCache as getDocsFromCache$1, getDocsFromServer as getDocsFromServer$1, getFirestore as getFirestore$1, getPersistentCacheIndexManager as getPersistentCacheIndexManager$1, increment as increment$1, initializeFirestore as initializeFirestore$1, limit as limit$1, limitToLast as limitToLast$1, loadBundle as loadBundle$1, namedQuery as namedQuery$1, onSnapshot as onSnapshot$1, onSnapshotsInSync as onSnapshotsInSync$1, or as or$1, orderBy as orderBy$1, query as query$1, queryEqual as queryEqual$1, refEqual as refEqual$1, runTransaction as runTransaction$1, setDoc as setDoc$1, setIndexConfiguration as setIndexConfiguration$1, setLogLevel as setLogLevel$1, snapshotEqual as snapshotEqual$1, startAfter as startAfter$1, startAt as startAt$1, sum as sum$1, terminate as terminate$1, updateDoc as updateDoc$1, vector as vector$1, waitForPendingWrites as waitForPendingWrites$1, where as where$1, writeBatch as writeBatch$1 } from 'firebase/firestore';\nexport * from 'firebase/firestore';\nclass Firestore {\n  constructor(firestore) {\n    return firestore;\n  }\n}\nconst FIRESTORE_PROVIDER_NAME = 'firestore';\nclass FirestoreInstances {\n  constructor() {\n    return _getAllInstancesOf(FIRESTORE_PROVIDER_NAME);\n  }\n}\nconst firestoreInstance$ = timer(0, 300).pipe(concatMap(() => from(_getAllInstancesOf(FIRESTORE_PROVIDER_NAME))), distinct());\nconst PROVIDED_FIRESTORE_INSTANCES = new InjectionToken('angularfire2.firestore-instances');\nfunction defaultFirestoreInstanceFactory(provided, defaultApp) {\n  const defaultFirestore = _getDefaultInstanceOf(FIRESTORE_PROVIDER_NAME, provided, defaultApp);\n  return defaultFirestore && new Firestore(defaultFirestore);\n}\nfunction firestoreInstanceFactory(fn) {\n  return (zone, injector) => {\n    const firestore = zone.runOutsideAngular(() => fn(injector));\n    return new Firestore(firestore);\n  };\n}\nconst FIRESTORE_INSTANCES_PROVIDER = {\n  provide: FirestoreInstances,\n  deps: [[new Optional(), PROVIDED_FIRESTORE_INSTANCES]]\n};\nconst DEFAULT_FIRESTORE_INSTANCE_PROVIDER = {\n  provide: Firestore,\n  useFactory: defaultFirestoreInstanceFactory,\n  deps: [[new Optional(), PROVIDED_FIRESTORE_INSTANCES], FirebaseApp]\n};\nclass FirestoreModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'fst');\n  }\n  static ɵfac = function FirestoreModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || FirestoreModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: FirestoreModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_FIRESTORE_INSTANCE_PROVIDER, FIRESTORE_INSTANCES_PROVIDER]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(FirestoreModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_FIRESTORE_INSTANCE_PROVIDER, FIRESTORE_INSTANCES_PROVIDER]\n    }]\n  }], () => [], null);\n})();\nfunction provideFirestore(fn, ...deps) {\n  registerVersion('angularfire', VERSION.full, 'fst');\n  return makeEnvironmentProviders([DEFAULT_FIRESTORE_INSTANCE_PROVIDER, FIRESTORE_INSTANCES_PROVIDER, {\n    provide: PROVIDED_FIRESTORE_INSTANCES,\n    useFactory: firestoreInstanceFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, _AngularFireSchedulers, FirebaseApps,\n    // Firestore+Auth work better if Auth is loaded first\n    [new Optional(), AuthInstances], [new Optional(), AppCheckInstances], ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst auditTrail = _zoneWrap(auditTrail$1, true);\nconst collectionSnapshots = _zoneWrap(collection$1, true);\nconst collectionChanges = _zoneWrap(collectionChanges$1, true);\nconst collectionCount = _zoneWrap(collectionCount$1, true);\nconst collectionCountSnap = _zoneWrap(collectionCountSnap$1, true);\nconst collectionData = _zoneWrap(collectionData$1, true);\nconst docSnapshots = _zoneWrap(doc$1, true);\nconst docData = _zoneWrap(docData$1, true);\nconst fromRef = _zoneWrap(fromRef$1, true);\nconst snapToData = _zoneWrap(snapToData$1, true);\nconst sortedChanges = _zoneWrap(sortedChanges$1, true);\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst addDoc = _zoneWrap(addDoc$1, true, 2);\nconst aggregateFieldEqual = _zoneWrap(aggregateFieldEqual$1, true, 2);\nconst aggregateQuerySnapshotEqual = _zoneWrap(aggregateQuerySnapshotEqual$1, true, 2);\nconst and = _zoneWrap(and$1, true, 2);\nconst clearIndexedDbPersistence = _zoneWrap(clearIndexedDbPersistence$1, true);\nconst collection = _zoneWrap(collection$2, true, 2);\nconst collectionGroup = _zoneWrap(collectionGroup$1, true, 2);\nconst connectFirestoreEmulator = _zoneWrap(connectFirestoreEmulator$1, true);\nconst deleteAllPersistentCacheIndexes = _zoneWrap(deleteAllPersistentCacheIndexes$1, true);\nconst deleteDoc = _zoneWrap(deleteDoc$1, true, 2);\nconst deleteField = _zoneWrap(deleteField$1, true, 2);\nconst disableNetwork = _zoneWrap(disableNetwork$1, true);\nconst disablePersistentCacheIndexAutoCreation = _zoneWrap(disablePersistentCacheIndexAutoCreation$1, true);\nconst doc = _zoneWrap(doc$2, true, 2);\nconst documentId = _zoneWrap(documentId$1, true, 2);\nconst enableIndexedDbPersistence = _zoneWrap(enableIndexedDbPersistence$1, true);\nconst enableMultiTabIndexedDbPersistence = _zoneWrap(enableMultiTabIndexedDbPersistence$1, true);\nconst enableNetwork = _zoneWrap(enableNetwork$1, true);\nconst enablePersistentCacheIndexAutoCreation = _zoneWrap(enablePersistentCacheIndexAutoCreation$1, true);\nconst endAt = _zoneWrap(endAt$1, true, 2);\nconst endBefore = _zoneWrap(endBefore$1, true, 2);\nconst getAggregateFromServer = _zoneWrap(getAggregateFromServer$1, true);\nconst getCountFromServer = _zoneWrap(getCountFromServer$1, true);\nconst getDoc = _zoneWrap(getDoc$1, true);\nconst getDocFromCache = _zoneWrap(getDocFromCache$1, true);\nconst getDocFromServer = _zoneWrap(getDocFromServer$1, true);\nconst getDocs = _zoneWrap(getDocs$1, true);\nconst getDocsFromCache = _zoneWrap(getDocsFromCache$1, true);\nconst getDocsFromServer = _zoneWrap(getDocsFromServer$1, true);\nconst getFirestore = _zoneWrap(getFirestore$1, true);\nconst getPersistentCacheIndexManager = _zoneWrap(getPersistentCacheIndexManager$1, true);\nconst increment = _zoneWrap(increment$1, true, 2);\nconst initializeFirestore = _zoneWrap(initializeFirestore$1, true);\nconst limit = _zoneWrap(limit$1, true, 2);\nconst limitToLast = _zoneWrap(limitToLast$1, true, 2);\nconst loadBundle = _zoneWrap(loadBundle$1, true);\nconst namedQuery = _zoneWrap(namedQuery$1, true, 2);\nconst onSnapshot = _zoneWrap(onSnapshot$1, true);\nconst onSnapshotsInSync = _zoneWrap(onSnapshotsInSync$1, true);\nconst or = _zoneWrap(or$1, true, 2);\nconst orderBy = _zoneWrap(orderBy$1, true, 2);\nconst query = _zoneWrap(query$1, true, 2);\nconst queryEqual = _zoneWrap(queryEqual$1, true, 2);\nconst refEqual = _zoneWrap(refEqual$1, true, 2);\nconst runTransaction = _zoneWrap(runTransaction$1, true);\nconst setDoc = _zoneWrap(setDoc$1, true, 2);\nconst setIndexConfiguration = _zoneWrap(setIndexConfiguration$1, true);\nconst setLogLevel = _zoneWrap(setLogLevel$1, true);\nconst snapshotEqual = _zoneWrap(snapshotEqual$1, true, 2);\nconst startAfter = _zoneWrap(startAfter$1, true, 2);\nconst startAt = _zoneWrap(startAt$1, true, 2);\nconst sum = _zoneWrap(sum$1, true, 2);\nconst terminate = _zoneWrap(terminate$1, true);\nconst updateDoc = _zoneWrap(updateDoc$1, true, 2);\nconst vector = _zoneWrap(vector$1, true, 2);\nconst waitForPendingWrites = _zoneWrap(waitForPendingWrites$1, true);\nconst where = _zoneWrap(where$1, true, 2);\nconst writeBatch = _zoneWrap(writeBatch$1, true, 2);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Firestore, FirestoreInstances, FirestoreModule, addDoc, aggregateFieldEqual, aggregateQuerySnapshotEqual, and, auditTrail, clearIndexedDbPersistence, collection, collectionChanges, collectionCount, collectionCountSnap, collectionData, collectionGroup, collectionSnapshots, connectFirestoreEmulator, deleteAllPersistentCacheIndexes, deleteDoc, deleteField, disableNetwork, disablePersistentCacheIndexAutoCreation, doc, docData, docSnapshots, documentId, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence, enableNetwork, enablePersistentCacheIndexAutoCreation, endAt, endBefore, firestoreInstance$, fromRef, getAggregateFromServer, getCountFromServer, getDoc, getDocFromCache, getDocFromServer, getDocs, getDocsFromCache, getDocsFromServer, getFirestore, getPersistentCacheIndexManager, increment, initializeFirestore, limit, limitToLast, loadBundle, namedQuery, onSnapshot, onSnapshotsInSync, or, orderBy, provideFirestore, query, queryEqual, refEqual, runTransaction, setDoc, setIndexConfiguration, setLogLevel, snapToData, snapshotEqual, sortedChanges, startAfter, startAt, sum, terminate, updateDoc, vector, waitForPendingWrites, where, writeBatch };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAI,WAAW,WAAY;AACzB,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC/C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AACA,SAAS,cAAc,IAAIC,OAAM,MAAM;AACrC,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACnF,QAAI,MAAM,EAAE,KAAKA,QAAO;AACtB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAKA,OAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAIA,MAAK,CAAC;AAAA,IAChB;AAAA,EACF;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAKA,KAAI,CAAC;AACzD;AAsBA,IAAI,kBAAkB;AAAA,EACpB,wBAAwB;AAC1B;AACA,SAAS,QAAQ,KAAK,SAAS;AAC7B,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAEA,SAAO,IAAI,WAAW,SAAU,YAAY;AAC1C,QAAI,cAAc,WAAW,KAAK,SAAS;AAAA,MACzC,MAAM,WAAW,KAAK,KAAK,UAAU;AAAA,MACrC,OAAO,WAAW,MAAM,KAAK,UAAU;AAAA,MACvC,UAAU,WAAW,SAAS,KAAK,UAAU;AAAA,IAC/C,CAAC;AACD,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAkBA,SAASC,KAAI,KAAK;AAChB,SAAO,QAAQ,KAAK;AAAA,IAClB,wBAAwB;AAAA,EAC1B,CAAC;AACH;AAMA,SAAS,QAAQ,KAAK,SAAS;AAC7B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAOA,KAAI,GAAG,EAAE,KAAK,IAAI,SAAU,MAAM;AACvC,WAAO,WAAW,MAAM,OAAO;AAAA,EACjC,CAAC,CAAC;AACJ;AACA,SAAS,WAAW,UAAU,SAAS;AACrC,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,MAAI,OAAO,SAAS,KAAK,OAAO;AAGhC,MAAI,CAAC,SAAS,OAAO,KAAK,OAAO,SAAS,YAAY,SAAS,QAAQ,CAAC,QAAQ,SAAS;AACvF,WAAO;AAAA,EACT;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,GAAG,GAAG,QAAQ,OAAO,IAAI,SAAS,IAAI,GAAG;AACtF;AAkBA,IAAI,aAAa,CAAC,SAAS,YAAY,SAAS;AAMhD,IAAI,eAAe,SAAU,QAAQ;AACnC,SAAO,OAAO,SAAU,SAAS;AAC/B,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,UAAI,SAAS,QAAQ,CAAC;AACtB,UAAI,UAAU,OAAO,QAAQ,OAAO,IAAI,KAAK,GAAG;AAC9C,oBAAY;AACZ;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AACH;AAKA,SAAS,eAAe,UAAU,OAAO,aAAa;AACpD,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC5C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,cAAc,SAAS,MAAM;AACjC,cAAY,OAAO,MAAM,aAAa,cAAc,CAAC,OAAO,WAAW,GAAG,MAAM,KAAK,CAAC;AACtF,SAAO;AACT;AAMA,SAAS,wBAAwB,UAAU,QAAQ;AACjD,UAAQ,OAAO,MAAM;AAAA,IACnB,KAAK;AACH,UAAI,SAAS,OAAO,QAAQ,KAAK,SAAS,SAAS,OAAO,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG,EAAG;AAAA,WAAM;AAClG,eAAO,eAAe,UAAU,OAAO,UAAU,GAAG,MAAM;AAAA,MAC5D;AACA;AAAA,IACF,KAAK;AACH,UAAI,SAAS,OAAO,QAAQ,KAAK,QAAQ,SAAS,SAAS,OAAO,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG,GAAG;AAGpG,YAAI,OAAO,aAAa,OAAO,UAAU;AACvC,cAAI,cAAc,SAAS,MAAM;AACjC,sBAAY,OAAO,OAAO,UAAU,CAAC;AACrC,sBAAY,OAAO,OAAO,UAAU,GAAG,MAAM;AAC7C,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,eAAe,UAAU,OAAO,UAAU,GAAG,MAAM;AAAA,QAC5D;AAAA,MACF;AACA;AAAA,IACF,KAAK;AACH,UAAI,SAAS,OAAO,QAAQ,KAAK,SAAS,SAAS,OAAO,QAAQ,EAAE,IAAI,KAAK,OAAO,IAAI,GAAG,GAAG;AAC5F,eAAO,eAAe,UAAU,OAAO,UAAU,CAAC;AAAA,MACpD;AACA;AAAA,EACJ;AACA,SAAO;AACT;AAQA,SAAS,uBAAuB,SAAS,SAAS,QAAQ;AACxD,MAAI,WAAW,QAAQ;AACrB,aAAS;AAAA,EACX;AACA,UAAQ,QAAQ,SAAU,QAAQ;AAEhC,QAAI,OAAO,QAAQ,OAAO,IAAI,IAAI,IAAI;AACpC,gBAAU,wBAAwB,SAAS,MAAM;AAAA,IACnD;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAKA,IAAI,aAAa,WAAY;AAC3B,SAAO,KAAK,UAAU,MAAS,GAAG,SAAS,CAAC;AAC9C;AAMA,IAAI,iBAAiB,SAAU,GAAG,GAAG;AACnC,SAAO,KAAK,UAAU,EAAE,QAAQ,MAAM,KAAK,UAAU,EAAE,QAAQ;AACjE;AAMA,IAAI,yBAAyB,WAAY;AACvC,SAAO,KAAK,WAAW,GAAG,OAAO,SAAU,IAAI;AAC7C,QAAI,QAAQ,GAAG,CAAC,GACd,UAAU,GAAG,CAAC;AAChB,WAAO,QAAQ,SAAS,KAAK,UAAU;AAAA,EACzC,CAAC,GAAG,IAAI,SAAU,IAAI;AACpB,QAAI,UAAU,GAAG,CAAC;AAClB,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;AAMA,SAAS,kBAAkBC,QAAO,SAAS;AACzC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO,QAAQA,QAAO;AAAA,IACpB,wBAAwB;AAAA,EAC1B,CAAC,EAAE,KAAK,WAAW,GAAG,IAAI,SAAU,IAAI;AACtC,QAAI,gBAAgB,GAAG,CAAC,GACtB,kBAAkB,GAAG,CAAC;AACxB,QAAI,aAAa,gBAAgB,WAAW;AAC5C,QAAI,iBAAiB,CAAC,eAAe,eAAe,eAAe,GAAG;AAIpE,sBAAgB,KAAK,QAAQ,SAAU,oBAAoB,cAAc;AACvE,YAAI,mBAAmB,WAAW,KAAK,SAAU,GAAG;AAClD,iBAAO,SAAS,EAAE,IAAI,KAAK,mBAAmB,GAAG;AAAA,QACnD,CAAC;AACD,YAAI,kBAAkB;AAEpB,cAAI,eAAe,iBAAiB,KAAK,kBAAkB,GAAG;AAC5D;AAAA,UACF;AAAA,QACF,OAAO;AAEL,cAAI,mBAAmB,kBAAkB,QAAQ,kBAAkB,SAAS,SAAS,cAAc,KAAK,KAAK,SAAU,GAAG;AACxH,mBAAO,SAAS,EAAE,KAAK,mBAAmB,GAAG;AAAA,UAC/C,CAAC;AACD,cAAI,oBAAoB,eAAe,kBAAkB,kBAAkB,GAAG;AAC5E;AAAA,UACF;AAAA,QACF;AACA,mBAAW,KAAK;AAAA,UACd,UAAU;AAAA,UACV,UAAU;AAAA,UACV,MAAM;AAAA,UACN,KAAK;AAAA,QACP,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT,CAAC,GAAG,aAAa,QAAQ,UAAU,UAAU,GAAG,uBAAuB,CAAC;AAC1E;AAKA,SAASC,YAAWD,QAAO;AACzB,SAAO,QAAQA,QAAO;AAAA,IACpB,wBAAwB;AAAA,EAC1B,CAAC,EAAE,KAAK,IAAI,SAAU,SAAS;AAC7B,WAAO,QAAQ;AAAA,EACjB,CAAC,CAAC;AACJ;AAKA,SAAS,cAAcA,QAAO,SAAS;AACrC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO,kBAAkBA,QAAO,OAAO,EAAE,KAAK,KAAK,SAAU,SAAS,SAAS;AAC7E,WAAO,uBAAuB,SAAS,SAAS,QAAQ,MAAM;AAAA,EAChE,GAAG,CAAC,CAAC,GAAG,qBAAqB,CAAC;AAChC;AAKA,SAAS,WAAWA,QAAO,SAAS;AAClC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO,kBAAkBA,QAAO,OAAO,EAAE,KAAK,KAAK,SAAU,SAAS,QAAQ;AAC5E,WAAO,cAAc,cAAc,CAAC,GAAG,SAAS,IAAI,GAAG,QAAQ,IAAI;AAAA,EACrE,GAAG,CAAC,CAAC,CAAC;AACR;AAMA,SAAS,eAAeA,QAAO,SAAS;AACtC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAOC,YAAWD,MAAK,EAAE,KAAK,IAAI,SAAU,KAAK;AAC/C,WAAO,IAAI,IAAI,SAAU,MAAM;AAC7B,aAAO,WAAW,MAAM,OAAO;AAAA,IACjC,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AACA,SAAS,oBAAoBA,QAAO;AAClC,SAAO,KAAK,mBAAmBA,MAAK,CAAC;AACvC;AACA,SAAS,gBAAgBA,QAAO;AAC9B,SAAO,oBAAoBA,MAAK,EAAE,KAAK,IAAI,SAAU,MAAM;AACzD,WAAO,KAAK,KAAK,EAAE;AAAA,EACrB,CAAC,CAAC;AACJ;;;AC/VA,IAAM,YAAN,MAAgB;AAAA,EACd,YAAY,WAAW;AACrB,WAAO;AAAA,EACT;AACF;AACA,IAAM,0BAA0B;AAChC,IAAM,qBAAN,MAAyB;AAAA,EACvB,cAAc;AACZ,WAAO,mBAAmB,uBAAuB;AAAA,EACnD;AACF;AACA,IAAM,qBAAqB,MAAM,GAAG,GAAG,EAAE,KAAK,UAAU,MAAM,KAAK,mBAAmB,uBAAuB,CAAC,CAAC,GAAG,SAAS,CAAC;AAC5H,IAAM,+BAA+B,IAAI,eAAe,kCAAkC;AAC1F,SAAS,gCAAgC,UAAU,YAAY;AAC7D,QAAM,mBAAmB,sBAAsB,yBAAyB,UAAU,UAAU;AAC5F,SAAO,oBAAoB,IAAI,UAAU,gBAAgB;AAC3D;AACA,SAAS,yBAAyB,IAAI;AACpC,SAAO,CAAC,MAAM,aAAa;AACzB,UAAM,YAAY,KAAK,kBAAkB,MAAM,GAAG,QAAQ,CAAC;AAC3D,WAAO,IAAI,UAAU,SAAS;AAAA,EAChC;AACF;AACA,IAAM,+BAA+B;AAAA,EACnC,SAAS;AAAA,EACT,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,4BAA4B,CAAC;AACvD;AACA,IAAM,sCAAsC;AAAA,EAC1C,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,MAAM,CAAC,CAAC,IAAI,SAAS,GAAG,4BAA4B,GAAG,WAAW;AACpE;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,cAAc;AACZ,oBAAgB,eAAe,QAAQ,MAAM,KAAK;AAAA,EACpD;AAAA,EACA,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,WAAW,CAAC,qCAAqC,4BAA4B;AAAA,EAC/E,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC,qCAAqC,4BAA4B;AAAA,IAC/E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AACH,SAAS,iBAAiB,OAAO,MAAM;AACrC,kBAAgB,eAAe,QAAQ,MAAM,KAAK;AAClD,SAAO,yBAAyB,CAAC,qCAAqC,8BAA8B;AAAA,IAClG,SAAS;AAAA,IACT,YAAY,yBAAyB,EAAE;AAAA,IACvC,OAAO;AAAA,IACP,MAAM;AAAA,MAAC;AAAA,MAAQ;AAAA,MAAU;AAAA,MAAwB;AAAA;AAAA,MAEjD,CAAC,IAAI,SAAS,GAAG,aAAa;AAAA,MAAG,CAAC,IAAI,SAAS,GAAG,iBAAiB;AAAA,MAAG,GAAG;AAAA,IAAI;AAAA,EAC/E,CAAC,CAAC;AACJ;AAGA,IAAME,cAAa,UAAU,YAAc,IAAI;AAC/C,IAAM,sBAAsB,UAAUC,aAAc,IAAI;AACxD,IAAMC,qBAAoB,UAAU,mBAAqB,IAAI;AAC7D,IAAMC,mBAAkB,UAAU,iBAAmB,IAAI;AACzD,IAAMC,uBAAsB,UAAU,qBAAuB,IAAI;AACjE,IAAMC,kBAAiB,UAAU,gBAAkB,IAAI;AACvD,IAAM,eAAe,UAAUC,MAAO,IAAI;AAC1C,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,cAAa,UAAU,YAAc,IAAI;AAC/C,IAAMC,iBAAgB,UAAU,eAAiB,IAAI;AAGrD,IAAMC,UAAS,UAAU,QAAU,MAAM,CAAC;AAC1C,IAAMC,uBAAsB,UAAU,qBAAuB,MAAM,CAAC;AACpE,IAAMC,+BAA8B,UAAU,6BAA+B,MAAM,CAAC;AACpF,IAAMC,OAAM,UAAU,KAAO,MAAM,CAAC;AACpC,IAAMC,6BAA4B,UAAU,2BAA6B,IAAI;AAC7E,IAAMd,cAAa,UAAU,YAAc,MAAM,CAAC;AAClD,IAAMe,mBAAkB,UAAU,iBAAmB,MAAM,CAAC;AAC5D,IAAMC,4BAA2B,UAAU,0BAA4B,IAAI;AAC3E,IAAMC,mCAAkC,UAAU,iCAAmC,IAAI;AACzF,IAAMC,aAAY,UAAU,WAAa,MAAM,CAAC;AAChD,IAAMC,eAAc,UAAU,aAAe,MAAM,CAAC;AACpD,IAAMC,kBAAiB,UAAU,gBAAkB,IAAI;AACvD,IAAMC,2CAA0C,UAAU,yCAA2C,IAAI;AACzG,IAAMhB,OAAM,UAAU,KAAO,MAAM,CAAC;AACpC,IAAMiB,cAAa,UAAU,YAAc,MAAM,CAAC;AAClD,IAAMC,8BAA6B,UAAU,4BAA8B,IAAI;AAC/E,IAAMC,sCAAqC,UAAU,oCAAsC,IAAI;AAC/F,IAAMC,iBAAgB,UAAU,eAAiB,IAAI;AACrD,IAAMC,0CAAyC,UAAU,wCAA0C,IAAI;AACvG,IAAMC,SAAQ,UAAU,OAAS,MAAM,CAAC;AACxC,IAAMC,aAAY,UAAU,WAAa,MAAM,CAAC;AAChD,IAAMC,0BAAyB,UAAU,wBAA0B,IAAI;AACvE,IAAMC,sBAAqB,UAAU,oBAAsB,IAAI;AAC/D,IAAMC,UAAS,UAAU,QAAU,IAAI;AACvC,IAAMC,mBAAkB,UAAU,iBAAmB,IAAI;AACzD,IAAMC,oBAAmB,UAAU,kBAAoB,IAAI;AAC3D,IAAMC,WAAU,UAAU,SAAW,IAAI;AACzC,IAAMC,oBAAmB,UAAU,kBAAoB,IAAI;AAC3D,IAAMC,qBAAoB,UAAU,mBAAqB,IAAI;AAC7D,IAAMC,gBAAe,UAAU,cAAgB,IAAI;AACnD,IAAMC,kCAAiC,UAAU,gCAAkC,IAAI;AACvF,IAAMC,aAAY,UAAU,WAAa,MAAM,CAAC;AAChD,IAAMC,uBAAsB,UAAU,qBAAuB,IAAI;AACjE,IAAMC,SAAQ,UAAU,OAAS,MAAM,CAAC;AACxC,IAAMC,eAAc,UAAU,aAAe,MAAM,CAAC;AACpD,IAAMC,cAAa,UAAU,YAAc,IAAI;AAC/C,IAAMC,cAAa,UAAU,YAAc,MAAM,CAAC;AAClD,IAAMC,cAAa,UAAU,YAAc,IAAI;AAC/C,IAAMC,qBAAoB,UAAU,mBAAqB,IAAI;AAC7D,IAAMC,MAAK,UAAU,IAAM,MAAM,CAAC;AAClC,IAAMC,WAAU,UAAU,SAAW,MAAM,CAAC;AAC5C,IAAMC,SAAQ,UAAU,OAAS,MAAM,CAAC;AACxC,IAAMC,cAAa,UAAU,YAAc,MAAM,CAAC;AAClD,IAAMC,YAAW,UAAU,UAAY,MAAM,CAAC;AAC9C,IAAMC,kBAAiB,UAAU,gBAAkB,IAAI;AACvD,IAAMC,UAAS,UAAU,QAAU,MAAM,CAAC;AAC1C,IAAMC,yBAAwB,UAAU,uBAAyB,IAAI;AACrE,IAAMC,eAAc,UAAU,aAAe,IAAI;AACjD,IAAMC,iBAAgB,UAAU,eAAiB,MAAM,CAAC;AACxD,IAAMC,cAAa,UAAU,YAAc,MAAM,CAAC;AAClD,IAAMC,WAAU,UAAU,SAAW,MAAM,CAAC;AAC5C,IAAMC,OAAM,UAAU,KAAO,MAAM,CAAC;AACpC,IAAMC,aAAY,UAAU,WAAa,IAAI;AAC7C,IAAMC,aAAY,UAAU,WAAa,MAAM,CAAC;AAChD,IAAMC,UAAS,UAAU,QAAU,MAAM,CAAC;AAC1C,IAAMC,wBAAuB,UAAU,sBAAwB,IAAI;AACnE,IAAMC,SAAQ,UAAU,OAAS,MAAM,CAAC;AACxC,IAAMC,cAAa,UAAU,YAAc,MAAM,CAAC;", "names": ["__assign", "from", "doc", "query", "collection", "auditTrail", "collection", "collectionChanges", "collectionCount", "collectionCountSnap", "collectionData", "doc", "docData", "fromRef", "snapToData", "sortedChanges", "addDoc", "aggregateFieldEqual", "aggregateQuerySnapshotEqual", "and", "clearIndexedDbPersistence", "collectionGroup", "connectFirestoreEmulator", "deleteAllPersistentCacheIndexes", "deleteDoc", "deleteField", "disableNetwork", "disablePersistentCacheIndexAutoCreation", "documentId", "enableIndexedDbPersistence", "enableMultiTabIndexedDbPersistence", "enableNetwork", "enablePersistentCacheIndexAutoCreation", "endAt", "endBefore", "getAggregateFromServer", "getCountFromServer", "getDoc", "getDocFromCache", "getDocFromServer", "getDocs", "getDocsFromCache", "getDocsFromServer", "getFirestore", "getPersistentCacheIndexManager", "increment", "initializeFirestore", "limit", "limitToLast", "loadBundle", "<PERSON><PERSON><PERSON><PERSON>", "onSnapshot", "onSnapshotsInSync", "or", "orderBy", "query", "queryEqual", "refEqual", "runTransaction", "setDoc", "setIndexConfiguration", "setLogLevel", "snapshotEqual", "startAfter", "startAt", "sum", "terminate", "updateDoc", "vector", "waitForPendingWrites", "where", "writeBatch"]}