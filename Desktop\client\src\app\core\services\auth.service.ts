import { Injectable, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ApiService } from './api.service';
import { BehaviorSubject, Observable, from, of, throwError, Subscription } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import {
  Auth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  UserCredential,
  user,
  authState,
  updateProfile,
  onAuthStateChanged
} from '@angular/fire/auth';
import {
  Firestore,
  collection,
  doc,
  setDoc,
  getDoc,
  updateDoc,
  DocumentReference,
  collectionData,
  query,
  where
} from '@angular/fire/firestore';
import { AuthErrorMapperService } from './auth-error-mapper.service';

export interface User {
  uid?: string;
  id?: number;
  email: string;
  role: 'doctor' | 'patient' | 'receptionist' | 'admin';
  firstName: string;
  lastName: string;
  profilePicture?: string | null;
  specialization?: string;
  phoneNumber?: string;
  address?: string;
  affiliatedInstitution?: string;
}

export interface AuthResponse {
  message: string;
  token: string;
  name: string;
  email: string;
  profilePicture?: string | null;
  specialization?: string;
  phoneNumber?: string;
  address?: string;
  affiliatedInstitution?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest extends LoginRequest {
  firstName: string;
  lastName: string;
  role: User['role'];
}

@Injectable({
  providedIn: 'root'
})
export class AuthService implements OnDestroy {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  currentUser$ = this.currentUserSubject.asObservable();
  private authSubscription: Subscription | null = null;

  constructor(
    private apiService: ApiService,
    private auth: Auth,
    private firestore: Firestore,
    private ngZone: NgZone,
    private authErrorMapper: AuthErrorMapperService
  ) {
    this.loadStoredUser();
    this.initAuthStateListener();
  }

  private loadStoredUser(): void {
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        this.currentUserSubject.next(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user data:', error);
        localStorage.removeItem('user');
      }
    } else {
      this.currentUserSubject.next(null);
    }
  }

  private initAuthStateListener(): void {
    // Run outside Angular zone to avoid warnings
    this.ngZone.runOutsideAngular(() => {
      this.authSubscription = new Subscription();

      const unsubscribe = onAuthStateChanged(this.auth, (firebaseUser) => {
        // Run UI updates inside Angular zone
        this.ngZone.run(() => {
          if (firebaseUser) {
            // User is signed in, fetch user profile from Firestore
            this.getUserData(firebaseUser.uid).subscribe(userData => {
              if (userData) {
                this.currentUserSubject.next(userData);
                localStorage.setItem('user', JSON.stringify(userData));
              }
            });
          } else {
            // User is signed out
            this.currentUserSubject.next(null);
            localStorage.removeItem('user');
          }
        });
      });

      this.authSubscription.add(() => unsubscribe());
    });
  }

  private getUserData(uid: string): Observable<User | null> {
    const userDocRef = doc(this.firestore, `users/${uid}`);
    return from(getDoc(userDocRef)).pipe(
      map(docSnap => {
        if (docSnap.exists()) {
          return { uid, ...docSnap.data() } as User;
        } else {
          return null;
        }
      }),
      catchError(error => {
        console.error('Error fetching user data:', error);
        return of(null);
      })
    );
  }

  login(credentials: LoginRequest): Observable<User> {
    return from(signInWithEmailAndPassword(
      this.auth,
      credentials.email,
      credentials.password
    )).pipe(
      switchMap(userCredential => this.getUserData(userCredential.user.uid)),
      map(user => {
        if (!user) {
          throw new Error('User data not found');
        }
        return user;
      }),
      tap(user => {
        localStorage.setItem('user', JSON.stringify(user));
        this.currentUserSubject.next(user);
      }),
      catchError(error => {
        console.error('Login error:', error);
        const userFriendlyMessage = this.authErrorMapper.mapFirebaseError(error);
        return throwError(() => new Error(userFriendlyMessage));
      })
    );
  }

  register(userData: RegisterRequest): Observable<User> {
    return from(createUserWithEmailAndPassword(
      this.auth,
      userData.email,
      userData.password
    )).pipe(
      switchMap(userCredential => {
        const user: User = {
          uid: userCredential.user.uid,
          email: userData.email,
          role: userData.role,
          firstName: userData.firstName,
          lastName: userData.lastName
        };

        // Store user data in Firestore
        const userDocRef = doc(this.firestore, `users/${userCredential.user.uid}`);
        return from(setDoc(userDocRef, user)).pipe(
          map(() => user)
        );
      }),
      tap(user => {
        localStorage.setItem('user', JSON.stringify(user));
        this.currentUserSubject.next(user);
      }),
      catchError(error => {
        console.error('Registration error:', error);
        const userFriendlyMessage = this.authErrorMapper.mapRegistrationError(error);
        return throwError(() => new Error(userFriendlyMessage));
      })
    );
  }

  logout(): Observable<void> {
    return from(signOut(this.auth)).pipe(
      tap(() => {
        localStorage.removeItem('user');
        this.currentUserSubject.next(null);
      }),
      catchError(error => {
        console.error('Logout error:', error);
        return throwError(() => new Error('Logout failed: ' + error.message));
      })
    );
  }

  updateUserProfile(uid: string, userData: Partial<User>): Observable<void> {
    const userDocRef = doc(this.firestore, `users/${uid}`);
    return from(updateDoc(userDocRef, { ...userData })).pipe(
      tap(() => {
        // Update the current user subject if this is the logged-in user
        const currentUser = this.currentUserSubject.value;
        if (currentUser && currentUser.uid === uid) {
          const updatedUser = { ...currentUser, ...userData };
          this.currentUserSubject.next(updatedUser);
          localStorage.setItem('user', JSON.stringify(updatedUser));
        }
      }),
      catchError(error => {
        console.error('Update profile error:', error);
        return throwError(() => new Error('Update profile failed: ' + error.message));
      })
    );
  }

  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }

  hasRole(role: User['role']): boolean {
    return this.currentUserSubject.value?.role === role;
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when service is destroyed
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }
}
