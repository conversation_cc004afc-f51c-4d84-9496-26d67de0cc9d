import { Injectable, inject } from '@angular/core';
import { Observable, from, forkJoin, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { Storage } from '../../storage';
import { FirebaseDataService } from './firebase-data.service';
import {
  USER_TABLE,
  DOCTOR_TABLE,
  PATIENT_TABLE,
  APPOINTMENT_TABLE,
  MEDICALRECORD_TABLE,
  AVAILABILITY_TABLE,
  DOCTORPATIENT_TABLE,
  CURRENT_DOCTOR,
  CURRENT_PATIENT
} from '../../constant';
import {
  UserType,
  DoctorType,
  PatientType,
  AppointmentType,
  MedicalRecordType,
  AvailabilityType,
  DoctorPatient
} from '../../type';

export interface MigrationResult {
  success: boolean;
  message: string;
  migratedCounts: {
    users: number;
    doctors: number;
    patients: number;
    appointments: number;
    medicalRecords: number;
    availability: number;
    doctorPatientRelations: number;
  };
  errors: string[];
}

/**
 * Service to migrate data from localStorage to Firebase
 */
@Injectable({
  providedIn: 'root'
})
export class FirebaseMigrationService {
  private storage = inject(Storage);
  private firebaseDataService = inject(FirebaseDataService);

  constructor() { }

  /**
   * Migrate all data from localStorage to Firebase
   */
  migrateAllData(): Observable<MigrationResult> {
    console.log('Starting migration from localStorage to Firebase...');

    const result: MigrationResult = {
      success: true,
      message: '',
      migratedCounts: {
        users: 0,
        doctors: 0,
        patients: 0,
        appointments: 0,
        medicalRecords: 0,
        availability: 0,
        doctorPatientRelations: 0
      },
      errors: []
    };

    // Get all data from localStorage
    const users = this.storage.getItem<UserType[]>(USER_TABLE) || [];
    const doctors = this.storage.getItem<DoctorType[]>(DOCTOR_TABLE) || [];
    const patients = this.storage.getItem<PatientType[]>(PATIENT_TABLE) || [];
    const appointments = this.storage.getItem<AppointmentType[]>(APPOINTMENT_TABLE) || [];
    const medicalRecords = this.storage.getItem<MedicalRecordType[]>(MEDICALRECORD_TABLE) || [];
    const availability = this.storage.getItem<AvailabilityType[]>(AVAILABILITY_TABLE) || [];
    const doctorPatientRelations = this.storage.getItem<DoctorPatient[]>(DOCTORPATIENT_TABLE) || [];

    console.log('Found data to migrate:', {
      users: users.length,
      doctors: doctors.length,
      patients: patients.length,
      appointments: appointments.length,
      medicalRecords: medicalRecords.length,
      availability: availability.length,
      doctorPatientRelations: doctorPatientRelations.length
    });

    // Create migration observables
    const migrationObservables: Observable<any>[] = [];

    // Migrate users
    if (users.length > 0) {
      const userMigrations = users.map(user => 
        this.firebaseDataService.registerUser(user).pipe(
          tap(() => result.migratedCounts.users++),
          catchError(error => {
            result.errors.push(`Failed to migrate user ${user.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...userMigrations);
    }

    // Migrate doctors
    if (doctors.length > 0) {
      const doctorMigrations = doctors.map(doctor => 
        this.firebaseDataService.addDoctor(doctor).pipe(
          tap(() => result.migratedCounts.doctors++),
          catchError(error => {
            result.errors.push(`Failed to migrate doctor ${doctor.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...doctorMigrations);
    }

    // Migrate patients
    if (patients.length > 0) {
      const patientMigrations = patients.map(patient => 
        this.firebaseDataService.addPatient(patient).pipe(
          tap(() => result.migratedCounts.patients++),
          catchError(error => {
            result.errors.push(`Failed to migrate patient ${patient.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...patientMigrations);
    }

    // Migrate appointments
    if (appointments.length > 0) {
      const appointmentMigrations = appointments.map(appointment => 
        this.firebaseDataService.addAppointment(appointment).pipe(
          tap(() => result.migratedCounts.appointments++),
          catchError(error => {
            result.errors.push(`Failed to migrate appointment ${appointment.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...appointmentMigrations);
    }

    // Migrate medical records
    if (medicalRecords.length > 0) {
      const recordMigrations = medicalRecords.map(record => 
        this.firebaseDataService.addMedicalRecord(record).pipe(
          tap(() => result.migratedCounts.medicalRecords++),
          catchError(error => {
            result.errors.push(`Failed to migrate medical record ${record.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...recordMigrations);
    }

    // Migrate availability
    if (availability.length > 0) {
      const availabilityMigrations = availability.map(slot => 
        this.firebaseDataService.addAvailability(slot).pipe(
          tap(() => result.migratedCounts.availability++),
          catchError(error => {
            result.errors.push(`Failed to migrate availability ${slot.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...availabilityMigrations);
    }

    // Migrate doctor-patient relations
    if (doctorPatientRelations.length > 0) {
      const relationMigrations = doctorPatientRelations.map(relation => 
        this.firebaseDataService.addDoctorPatientRelation(relation).pipe(
          tap(() => result.migratedCounts.doctorPatientRelations++),
          catchError(error => {
            result.errors.push(`Failed to migrate doctor-patient relation ${relation.id}: ${error.message}`);
            return of(null);
          })
        )
      );
      migrationObservables.push(...relationMigrations);
    }

    // Execute all migrations
    if (migrationObservables.length === 0) {
      result.message = 'No data found to migrate';
      return of(result);
    }

    return forkJoin(migrationObservables).pipe(
      map(() => {
        // Set current user states if they exist
        const currentDoctor = this.storage.getItem<UserType>(CURRENT_DOCTOR);
        const currentPatient = this.storage.getItem<UserType>(CURRENT_PATIENT);

        if (currentDoctor) {
          this.firebaseDataService.setCurrentDoctor(currentDoctor);
        }

        if (currentPatient) {
          this.firebaseDataService.setCurrentPatient(currentPatient);
        }

        result.success = result.errors.length === 0;
        result.message = result.success 
          ? 'Migration completed successfully' 
          : `Migration completed with ${result.errors.length} errors`;

        return result;
      }),
      catchError(error => {
        result.success = false;
        result.message = `Migration failed: ${error.message}`;
        result.errors.push(error.message);
        return of(result);
      })
    );
  }

  /**
   * Check if localStorage has data to migrate
   */
  hasDataToMigrate(): boolean {
    const users = this.storage.getItem<UserType[]>(USER_TABLE) || [];
    const doctors = this.storage.getItem<DoctorType[]>(DOCTOR_TABLE) || [];
    const patients = this.storage.getItem<PatientType[]>(PATIENT_TABLE) || [];
    const appointments = this.storage.getItem<AppointmentType[]>(APPOINTMENT_TABLE) || [];
    const medicalRecords = this.storage.getItem<MedicalRecordType[]>(MEDICALRECORD_TABLE) || [];
    const availability = this.storage.getItem<AvailabilityType[]>(AVAILABILITY_TABLE) || [];
    const doctorPatientRelations = this.storage.getItem<DoctorPatient[]>(DOCTORPATIENT_TABLE) || [];

    return users.length > 0 || 
           doctors.length > 0 || 
           patients.length > 0 || 
           appointments.length > 0 || 
           medicalRecords.length > 0 || 
           availability.length > 0 || 
           doctorPatientRelations.length > 0;
  }

  /**
   * Clear localStorage after successful migration
   */
  clearLocalStorageData(): void {
    console.log('Clearing localStorage data after successful migration...');
    
    this.storage.removeItem(USER_TABLE);
    this.storage.removeItem(DOCTOR_TABLE);
    this.storage.removeItem(PATIENT_TABLE);
    this.storage.removeItem(APPOINTMENT_TABLE);
    this.storage.removeItem(MEDICALRECORD_TABLE);
    this.storage.removeItem(AVAILABILITY_TABLE);
    this.storage.removeItem(DOCTORPATIENT_TABLE);
    this.storage.removeItem(CURRENT_DOCTOR);
    this.storage.removeItem(CURRENT_PATIENT);

    console.log('localStorage data cleared successfully');
  }

  /**
   * Get migration summary
   */
  getMigrationSummary(): { [key: string]: number } {
    const users = this.storage.getItem<UserType[]>(USER_TABLE) || [];
    const doctors = this.storage.getItem<DoctorType[]>(DOCTOR_TABLE) || [];
    const patients = this.storage.getItem<PatientType[]>(PATIENT_TABLE) || [];
    const appointments = this.storage.getItem<AppointmentType[]>(APPOINTMENT_TABLE) || [];
    const medicalRecords = this.storage.getItem<MedicalRecordType[]>(MEDICALRECORD_TABLE) || [];
    const availability = this.storage.getItem<AvailabilityType[]>(AVAILABILITY_TABLE) || [];
    const doctorPatientRelations = this.storage.getItem<DoctorPatient[]>(DOCTORPATIENT_TABLE) || [];

    return {
      users: users.length,
      doctors: doctors.length,
      patients: patients.length,
      appointments: appointments.length,
      medicalRecords: medicalRecords.length,
      availability: availability.length,
      doctorPatientRelations: doctorPatientRelations.length
    };
  }
}
