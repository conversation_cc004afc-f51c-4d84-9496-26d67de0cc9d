import{Cb as T,Fb as F,Ib as P,Ic as E,Jb as R,Jc as j,Kc as x,Ra as D,Tb as C,Zb as S,a as c,ab as U,b as d,ec as A,fc as $,h as v,i as b,j as m,k as l,l as h,o as u,t as _,v as f,x as w,z as g}from"./chunk-YV65XDJO.js";var y=function(a){return a[a.State=0]="State",a[a.Transition=1]="Transition",a[a.Sequence=2]="Sequence",a[a.Group=3]="Group",a[a.Animate=4]="Animate",a[a.Keyframes=5]="Keyframes",a[a.Style=6]="Style",a[a.Trigger=7]="Trigger",a[a.Reference=8]="Reference",a[a.AnimateChild=9]="AnimateChild",a[a.AnimateRef=10]="AnimateRef",a[a.Query=11]="Query",a[a.<PERSON>agger=12]="Stagger",a}(y||{}),Y="*";function H(a,t){return{type:y.Trigger,name:a,definitions:t,options:{}}}function M(a,t=null){return{type:y.Animate,styles:t,timings:a}}function G(a,t=null){return{type:y.Sequence,steps:a,options:t}}function Q(a){return{type:y.Style,styles:a,offset:null}}function W(a,t,e=null){return{type:y.Transition,expr:a,animation:t,options:e}}var L=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,e=0){this.totalTime=t+e}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(n=>n()),e.length=0}},q=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let e=0,n=0,i=0,o=this.players.length;o==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(r=>{r.onDone(()=>{++e==o&&this._onFinish()}),r.onDestroy(()=>{++n==o&&this._onDestroy()}),r.onStart(()=>{++i==o&&this._onStart()})}),this.totalTime=this.players.reduce((r,s)=>Math.max(r,s.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let e=t*this.totalTime;this.players.forEach(n=>{let i=n.totalTime?Math.min(1,e/n.totalTime):1;n.setPosition(i)})}getPosition(){let t=this.players.reduce((e,n)=>e===null||n.totalTime>e.totalTime?n:e,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(n=>n()),e.length=0}},J="!";var N=class a{constructor(t,e,n,i,o,r){this.http=t;this.router=e;this.db=n;this.auth=i;this.firestore=o;this.authErrorMapper=r}USER_INFO_KEY="med_secure_user_info";TOKEN_KEY="med_secure_token";userInfoSubject=new v(null);isAuthenticated$=this.userInfoSubject.pipe(h(t=>!!t));register(t){return this.db.userTable().find(n=>n.email===t.email)?l(()=>({message:"Email already exists",success:!1})):b(F(this.auth,t.email,t.password)).pipe(_(n=>{let i=n.user.uid,o={id:i,firstname:t.firstName,lastname:t.lastName,email:t.email,password:t.password,role:t.role.toUpperCase()};this.db.register(o),console.log("Registration successful with local DB:",o);let r={uid:i,firstName:t.firstName,lastName:t.lastName,email:t.email,role:t.role.toLowerCase(),createdAt:new Date().toISOString(),profileComplete:!1,bio:"",phoneNumber:"",hospitalAffiliations:[],qualifications:[],services:[],profilePicture:null},s={name:`${t.firstName} ${t.lastName}`,email:t.email,role:t.role.toLowerCase(),id:i,firstName:t.firstName,lastName:t.lastName,bio:"",phoneNumber:"",hospitalAffiliations:[],qualifications:[],services:[],profilePicture:null};if(t.role.toLowerCase()==="doctor"){Object.assign(r,{specialization:t.specialty||"",medicalLicenseNumber:t.medicalLicenseNumber||""}),s.doctorId=i,s.specialization=t.specialty;let O={id:i,bio:"",image:"",hospitalname:"",qualification:t.medicalLicenseNumber||"",specialisation:t.specialty||"",contact:"",paymentPlan:"",user_id:i};this.db.addNewDoctor(O)}this.saveUserInfo(s),this.userInfoSubject.next(s);let p=S(this.firestore,`users/${i}`);return b(A(p,r)).pipe(h(()=>({userId:i,message:"Registration successful",success:!0})))}),u(n=>{console.error("Firebase registration error:",n);let i=this.authErrorMapper.mapRegistrationError(n);return l(()=>({message:i,success:!1}))}))}login(t,e){return b(P(this.auth,t,e)).pipe(_(n=>{let i=n.user.uid,o=this.db.login(t,e);if(o){let r={userId:i,name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),success:!0};if(o.role==="DOCTOR"){let s=this.db.doctorTable().find(p=>p.user_id===o.id);if(s){let p={name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),id:i,doctorId:s.id,firstName:o.firstname,lastName:o.lastname,specialization:s.specialisation,bio:s.bio,phoneNumber:s.contact,hospitalAffiliations:s.hospitalname,profilePicture:s.image||null};this.saveUserInfo(p)}}else{let s={name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),id:i,firstName:o.firstname,lastName:o.lastname};this.saveUserInfo(s)}return m(r)}else{let r=t.split("@")[0].split("."),s=r[0]||"User",p=r.length>1?r[1]:"",I={id:i,firstname:s,lastname:p,email:t,password:e,role:"DOCTOR"};this.db.register(I);let O={name:`${s} ${p}`,email:t,role:"doctor",id:i,doctorId:i,firstName:s,lastName:p};return this.saveUserInfo(O),m({userId:i,name:`${s} ${p}`,email:t,role:"doctor",success:!0})}}),u(n=>{console.error("Firebase login error:",n);let i=this.db.login(t,e);if(i)return this.migrateLocalUserToFirebase(i,e);let o=this.authErrorMapper.mapFirebaseError(n);return l(()=>({message:o,success:!1}))}))}migrateLocalUserToFirebase(t,e){return b(F(this.auth,t.email,e)).pipe(_(n=>{let i=n.user.uid;t.id=i;let o={uid:i,firstName:t.firstname,lastName:t.lastname,email:t.email,role:t.role.toLowerCase(),createdAt:new Date().toISOString()},r=S(this.firestore,`users/${i}`);return b(A(r,o)).pipe(h(()=>{let s={name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),id:i,firstName:t.firstname,lastName:t.lastname};return this.saveUserInfo(s),{userId:i,name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),success:!0}}))}),u(n=>{console.error("Migration to Firebase failed:",n);let i={name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),id:t.id,firstName:t.firstname,lastName:t.lastname};return this.saveUserInfo(i),m({userId:t.id,name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),success:!0})}))}saveUserInfo(t){localStorage.setItem(this.USER_INFO_KEY,JSON.stringify(t)),t.token&&localStorage.setItem(this.TOKEN_KEY,t.token),this.userInfoSubject.next(t),console.log("User info saved and updated:",t)}getUserInfo(){return this.userInfoSubject.getValue()}isAuthenticated(){return!!this.getUserInfo()}logout(){R(this.auth).then(()=>{localStorage.removeItem(this.USER_INFO_KEY),localStorage.removeItem(this.TOKEN_KEY),this.userInfoSubject.next(null),this.router.navigate(["/login"])}).catch(t=>{console.error("Logout error:",t)})}updateUserInfo(t){let e=this.getUserInfo();if(e){let n=c(c({},e),t);this.saveUserInfo(n)}}getDoctorId(){let t=this.getUserInfo();return t?t.role&&t.role.toLowerCase()==="doctor"?t.doctorId||t.id||null:t.doctorId||null:null}updateProfile(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("Doctor ID not found"));let n=this.db.doctorTable().find(s=>s.id===e);if(!n)return l(()=>new Error("Doctor profile not found"));let i=c({},n);t.bio&&(i.bio=t.bio),t.hospitalname&&(i.hospitalname=t.hospitalname),t.specialization&&(i.specialisation=t.specialization),t.contact&&(i.contact=t.contact),t.experience&&(i.hospitalname=t.experience),t.education&&(i.qualification=t.education),this.db.doctorTable.update(s=>s.map(p=>p.id===e?i:p)),this.db.storage.setItem("DOCTOR_TABLE",this.db.doctorTable());let o={bio:t.bio||"",hospitalAffiliations:t.experience||t.hospitalname||"",specialization:t.specialization||"",phoneNumber:t.contactNumber||t.contact||"",qualifications:t.education||"",services:t.services||"",updatedAt:new Date().toISOString()},r=S(this.firestore,`users/${e}`);return b($(r,o)).pipe(h(()=>{let s=this.getUserInfo();if(s){let p=d(c({},s),{specialization:t.specialization,bio:t.bio,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services});this.saveUserInfo(p)}return{message:"Profile updated successfully in both local DB and Firestore",id:e,bio:t.bio,specialization:t.specialization,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services}}),u(s=>{console.error("Error updating profile in Firestore:",s);let p=this.getUserInfo();if(p){let I=d(c({},p),{specialization:t.specialization,bio:t.bio,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services});this.saveUserInfo(I)}return m({message:"Profile updated in local DB only (Firestore update failed)",id:e,bio:t.bio,specialization:t.specialization,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname})}))}updateProfilePicture(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("Doctor ID not found"));let n=S(this.firestore,`users/${e}`);return b($(n,{profilePicture:t,updatedAt:new Date().toISOString()})).pipe(f(()=>{console.log("Profile picture updated successfully in Firestore");let i=this.getUserInfo();if(i){let o=d(c({},i),{profilePicture:t});this.saveUserInfo(o)}}),u(i=>(console.warn("Error updating profile picture in Firestore, creating document:",i),b(A(n,{profilePicture:t,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{merge:!0})).pipe(f(()=>console.log("Created new user document in Firestore with profile picture")),u(o=>(console.error("Could not create user document in Firestore:",o),m(null)))))),_(()=>{let i=this.db.doctorTable().find(r=>r.id===e);if(i){let r=d(c({},i),{image:t});this.db.doctorTable.update(s=>s.map(p=>p.id===e?r:p))}else{console.log("Doctor profile not found in local DB, creating it");let r=this.getUserInfo(),s=r?.id||e,p={id:e,bio:r?.bio||"",image:t,hospitalname:r?.hospitalAffiliations?.toString()||"",qualification:r?.qualifications?.toString()||"",specialisation:r?.specialization||"",contact:r?.phoneNumber||"",paymentPlan:"Free",user_id:s};this.db.doctorTable.update(I=>[...I,p]),i=p}this.db.storage.setItem("DOCTOR_TABLE",this.db.doctorTable());let o=this.getUserInfo();if(o){let r=d(c({},o),{profilePicture:t});this.saveUserInfo(r);let s=this.db.current_doctor();s&&s.id&&this.db.storage.setItem("CURRENT_DOCTOR",s)}return m({message:"Profile picture updated successfully",profilePicture:t})}))}getDoctor(t){let e=this.db.doctorTable().find(i=>i.id===t);if(!e)return l(()=>new Error("Doctor not found"));let n=this.db.userTable().find(i=>i.id===e.user_id);return n?m({id:e.id,firstName:n.firstname,lastName:n.lastname,email:n.email,bio:e.bio,specialization:e.specialisation,phoneNumber:e.contact,hospitalAffiliations:e.hospitalname,profilePicture:e.image}):l(()=>new Error("User not found for doctor"))}clearUserInfo(){localStorage.removeItem(this.USER_INFO_KEY),localStorage.removeItem(this.TOKEN_KEY),this.userInfoSubject.next(null)}verifyCurrentPassword(t){let e=this.getUserInfo();if(!e||!e.email)return l(()=>new Error("User not logged in"));let n=this.db.userTable().find(i=>i.email===e.email);return n?n.password===t?m({verified:!0}):l(()=>new Error("Current password is incorrect")):l(()=>new Error("User not found"))}changePassword(t,e){let n=this.getUserInfo();if(!n||!n.email)return l(()=>new Error("User not logged in"));let i=this.db.userTable().find(r=>r.email===n.email);if(!i)return l(()=>new Error("User not found"));if(i.password!==t)return l(()=>new Error("Current password is incorrect"));let o=d(c({},i),{password:e});return this.db.userTable.update(r=>r.map(s=>s.id===i.id?o:s)),this.db.storage.setItem("USER_TABLE",this.db.userTable()),m({message:"Password changed successfully"})}static \u0275fac=function(e){return new(e||a)(g(D),g(U),g(E),g(T),g(C),g(j))};static \u0275prov=w({token:a,factory:a.\u0275fac,providedIn:"root"})};var z=class a{constructor(t,e,n){this.http=t;this.authService=e;this.db=n;this.authService.isAuthenticated$.subscribe(i=>{i?this.initializeAppointments():(this.appointmentsSubject.next([]),this.alertsSubject.next([]))}),this.authService.isAuthenticated()&&this.initializeAppointments()}apiUrl=`${x.apiUrl}/api/appointments`;appointments=new v([]);alerts=new v([]);appointmentError$=new v("");appointmentsSubject=new v([]);appointments$=this.appointmentsSubject.asObservable();alertsSubject=new v([]);alerts$=this.alertsSubject.asObservable();getDoctorId(){return this.authService.getDoctorId()}loadAppointments(){let t=this.getDoctorId();if(!t)return console.warn("No doctor ID found - Please ensure you are logged in as a doctor"),this.appointmentError$.next("No doctor ID found - Please ensure you are logged in as a doctor"),this.appointments.next([]),m([]);let e=parseInt(t,10);return isNaN(e)?(console.error("Invalid doctor ID format:",t),this.appointmentError$.next("Invalid doctor ID format"),m([])):(console.log("Loading appointments for doctor ID:",e),this.http.get(`${this.apiUrl}/doctor/${e}`).pipe(h(n=>{let i=n.map(o=>d(c({},o),{appointmentDate:new Date(o.appointmentDate)}));return console.log("Loaded appointments:",i),this.appointments.next(i),i}),u(n=>{console.error("Error loading appointments:",n);let i=n.error?.message||"Failed to load appointments";return this.appointmentError$.next(i),l(()=>new Error(i))})))}getAppointments(){return this.appointments.asObservable()}getAppointmentsByDateRange(t,e,n){let i=e.toISOString().split("T")[0],o=n.toISOString().split("T")[0];return this.http.get(`${this.apiUrl}/doctor/${t}/date-range?startDate=${i}&endDate=${o}`).pipe(h(r=>r.map(s=>d(c({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(r=>{this.appointments.next(r)}),u(r=>(console.error("Error loading appointments by date range:",r),m([]))))}getAppointment(t){return this.http.get(`${this.apiUrl}/${t}`).pipe(h(e=>d(c({},e),{appointmentDate:new Date(e.appointmentDate)})),u(e=>{console.error("Error fetching appointment by id:",e);let n=this.appointments.getValue().find(i=>i.appointmentId===t);return m(n)}))}createDoctorAvailabilitySlot(t,e,n,i){let o=t||this.getDoctorId();if(!o)return l(()=>new Error("No doctor ID found"));let r=e.toISOString().split("T")[0];return this.http.post(`${this.apiUrl}/doctor/${o}/availability`,{},{params:{date:r,startTime:n,durationMinutes:i.toString()}}).pipe(h(s=>d(c({},s),{appointmentDate:new Date(s.appointmentDate)})),f(s=>{let p=this.appointments.getValue();this.appointments.next([...p,s]),this.addAlert({id:Date.now().toString(),message:"New availability slot created successfully",type:"info",time:"just now"})}),u(s=>(console.error("Error creating availability slot:",s),l(()=>new Error("Failed to create availability slot")))))}createDoctorAvailabilityBatch(t,e,n,i){let o=e.map(r=>r.toISOString().split("T")[0]);return this.http.post(`${this.apiUrl}/doctor/${t}/availability/batch`,{},{params:{dates:o,startTimes:n,durationMinutes:i.toString()}}).pipe(h(r=>r.map(s=>d(c({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(r=>{let s=this.appointments.getValue();this.appointments.next([...s,...r]),this.addAlert({id:Date.now().toString(),message:`Created ${r.length} availability slots successfully`,type:"info",time:"just now"})}),u(r=>(console.error("Error adding batch availability slots:",r),l(()=>new Error("Failed to create availability slots")))))}createRecurringAvailability(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("No doctor ID found"));let n=t.weekdays.map((r,s)=>r?s:-1).filter(r=>r!==-1),i=t.startDate.toISOString().split("T")[0],o=t.endDate.toISOString().split("T")[0];return this.http.post(`${this.apiUrl}/doctor/${e}/availability/recurring`,{},{params:{startDate:i,endDate:o,daysOfWeek:n,startTime:t.startTime,endTime:t.endTime,slotDuration:t.slotDuration.toString()}}).pipe(h(r=>r.map(s=>d(c({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(r=>{let s=this.appointments.getValue();this.appointments.next([...s,...r]),this.addAlert({id:Date.now().toString(),message:`Created ${r.length} recurring availability slots`,type:"info",time:"just now"})}),u(r=>(console.error("Error creating recurring availability:",r),l(()=>new Error("Failed to create recurring availability slots")))))}updateAppointment(t){return this.http.put(`${this.apiUrl}/${t.appointmentId}`,t).pipe(f(e=>{this.updateAppointmentInList(e)}),u(e=>(console.error("Error updating appointment:",e),l(()=>new Error("Failed to update appointment")))))}updateAppointmentInList(t){let n=this.appointments.getValue().map(i=>i.appointmentId===t.appointmentId?d(c({},t),{appointmentDate:new Date(t.appointmentDate)}):i);this.appointments.next(n)}deleteAppointmentSlot(t){return this.http.delete(`${this.apiUrl}/${t}/availability`).pipe(f(()=>{let e=this.appointments.getValue();this.appointments.next(e.filter(n=>n.appointmentId!==t)),this.addAlert({id:Date.now().toString(),message:"Availability slot was deleted",type:"info",time:"just now"})}),u(e=>(console.error("Error deleting availability slot:",e),l(()=>new Error("Failed to delete availability slot")))))}getDoctorAvailabilitySlots(t){return this.http.get(`${this.apiUrl}/doctor/${t}/availability`).pipe(h(e=>e.map(n=>d(c({},n),{appointmentDate:new Date(n.appointmentDate)}))),f(e=>{let n=this.appointments.getValue(),i=new Set(n.map(r=>r.appointmentId)),o=e.filter(r=>!i.has(r.appointmentId));o.length>0&&this.appointments.next([...n,...o])}),u(e=>(console.error("Error fetching doctor availability slots:",e),m([]))))}getDoctorUpcomingAppointments(t){return this.http.get(`${this.apiUrl}/doctor/${t}/upcoming`).pipe(h(e=>e.map(n=>d(c({},n),{appointmentDate:new Date(n.appointmentDate)}))),f(e=>{this.appointments.next(e)}),u(e=>(console.error("Error fetching doctor upcoming appointments:",e),m([]))))}getDoctorPendingAppointments(t){return this.http.get(`${this.apiUrl}/doctor/${t}/pending`).pipe(h(e=>e.map(n=>d(c({},n),{appointmentDate:new Date(n.appointmentDate)}))),u(e=>(console.error("Error fetching pending appointments:",e),m([]))))}approveAppointment(t,e){return this.http.put(`${this.apiUrl}/${t}/approve`,{},{params:{doctorNotes:e||""}}).pipe(f(n=>{this.updateAppointmentInList(n),this.addAlert({id:Date.now().toString(),message:`Appointment with ${n.patientName} approved`,type:"info",time:"just now"})}),u(n=>(console.error("Error approving appointment:",n),l(()=>new Error("Failed to approve appointment")))))}rejectAppointment(t,e){return this.http.put(`${this.apiUrl}/${t}/reject`,{},{params:{doctorNotes:e||""}}).pipe(f(n=>{this.updateAppointmentInList(n),this.addAlert({id:Date.now().toString(),message:`Appointment with ${n.patientName} rejected`,type:"error",time:"just now"})}),u(n=>(console.error("Error rejecting appointment:",n),l(()=>new Error("Failed to reject appointment")))))}getAlerts(){return this.alerts.asObservable()}getCurrentAppointments(){return this.appointments.getValue()}getCurrentAlerts(){return this.alerts.getValue()}getTodaysAppointments(){let t=new Date;return t.setHours(0,0,0,0),this.appointments.getValue().filter(e=>{let n=new Date(e.appointmentDate);return n.setHours(0,0,0,0),n.getTime()===t.getTime()&&e.status==="Approved"})}getPendingConsultations(){return this.appointments.getValue().filter(t=>t.status==="Pending"&&!t.isDoctorAvailabilitySlot).length}generateAlerts(t){let e=[],n=new Date,i=this.getPendingConsultations();i>0&&e.push({id:"pending-alert",type:"info",message:`You have ${i} pending appointment requests`,time:"just now"});let o=this.getTodaysAppointments().length;o>0&&e.push({id:"today-alert",type:"warning",message:`You have ${o} appointments scheduled for today`,time:"just now"}),this.alerts.next(e)}getTotalPatients(){return new Set(this.appointments.getValue().filter(e=>e.patientId).map(e=>e.patientId)).size}getAppointmentErrors(){return this.appointmentError$.asObservable()}clearAppointmentErrors(){this.appointmentError$.next("")}refreshAppointments(){this.loadAppointments()}addAlert(t){let e=this.alerts.getValue();this.alerts.next([t,...e])}initializeAppointments(){let t=this.authService.getDoctorId();if(!t){console.error("No doctor ID found");return}let e=this.db.appointmentTable().filter(i=>i.doctor_id===t).map(i=>{let o=this.db.patientTable().find(s=>s.id===i.patient_id),r=o?this.db.userTable().find(s=>s.id===o.user_id):null;return d(c({},i),{patientName:r?`${r.firstname} ${r.lastname}`:"Unknown Patient"})});this.appointmentsSubject.next(e);let n=e.filter(i=>i.status==="Pending").map(i=>({type:"info",message:`New appointment request from ${i.patientName}`,time:new Date(i.date).toLocaleDateString()}));this.alertsSubject.next(n)}static \u0275fac=function(e){return new(e||a)(g(D),g(N),g(E))};static \u0275prov=w({token:a,factory:a.\u0275fac,providedIn:"root"})};export{y as a,Y as b,H as c,M as d,G as e,Q as f,W as g,L as h,q as i,J as j,N as k,z as l};
