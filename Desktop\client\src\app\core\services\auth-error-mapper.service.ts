import { Injectable } from '@angular/core';

/**
 * Service to map Firebase authentication error codes to user-friendly messages
 */
@Injectable({
  providedIn: 'root'
})
export class AuthErrorMapperService {

  /**
   * Maps Firebase authentication error codes to user-friendly messages
   * @param error - The Firebase error object or error message
   * @returns User-friendly error message
   */
  mapFirebaseError(error: any): string {
    // Handle different error formats
    let errorCode = '';
    let errorMessage = '';

    if (typeof error === 'string') {
      errorMessage = error;
      // Try to extract error code from string
      const codeMatch = error.match(/auth\/([a-z-]+)/);
      if (codeMatch) {
        errorCode = `auth/${codeMatch[1]}`;
      }
    } else if (error?.code) {
      errorCode = error.code;
      errorMessage = error.message || '';
    } else if (error?.message) {
      errorMessage = error.message;
      // Try to extract error code from message
      const codeMatch = errorMessage.match(/auth\/([a-z-]+)/);
      if (codeMatch) {
        errorCode = `auth/${codeMatch[1]}`;
      }
    }

    // Map Firebase error codes to user-friendly messages
    switch (errorCode) {
      case 'auth/invalid-credential':
      case 'auth/invalid-login-credentials':
      case 'auth/wrong-password':
        return 'The email or password you entered is incorrect. Please check your credentials and try again.';
      
      case 'auth/user-not-found':
        return 'No account found with this email address. Please check your email or create a new account.';
      
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      
      case 'auth/user-disabled':
        return 'This account has been disabled. Please contact support for assistance.';
      
      case 'auth/too-many-requests':
        return 'Too many failed login attempts. Please wait a few minutes before trying again.';
      
      case 'auth/network-request-failed':
        return 'Network connection error. Please check your internet connection and try again.';
      
      case 'auth/email-already-in-use':
        return 'An account with this email address already exists. Please use a different email or try logging in.';
      
      case 'auth/weak-password':
        return 'Password is too weak. Please choose a stronger password with at least 6 characters.';
      
      case 'auth/requires-recent-login':
        return 'For security reasons, please log in again to complete this action.';
      
      case 'auth/invalid-verification-code':
        return 'The verification code is invalid. Please check and try again.';
      
      case 'auth/invalid-verification-id':
        return 'The verification ID is invalid. Please try again.';
      
      case 'auth/missing-email':
        return 'Please enter your email address.';
      
      case 'auth/missing-password':
        return 'Please enter your password.';
      
      case 'auth/internal-error':
        return 'An internal error occurred. Please try again later.';
      
      default:
        // Check for common error patterns in the message
        if (errorMessage.toLowerCase().includes('invalid-credential') || 
            errorMessage.toLowerCase().includes('invalid credential')) {
          return 'The email or password you entered is incorrect. Please check your credentials and try again.';
        }
        
        if (errorMessage.toLowerCase().includes('user not found') || 
            errorMessage.toLowerCase().includes('user-not-found')) {
          return 'No account found with this email address. Please check your email or create a new account.';
        }
        
        if (errorMessage.toLowerCase().includes('wrong password') || 
            errorMessage.toLowerCase().includes('wrong-password')) {
          return 'The password you entered is incorrect. Please try again.';
        }
        
        if (errorMessage.toLowerCase().includes('email already') || 
            errorMessage.toLowerCase().includes('email-already-in-use')) {
          return 'An account with this email address already exists. Please use a different email or try logging in.';
        }
        
        if (errorMessage.toLowerCase().includes('network') || 
            errorMessage.toLowerCase().includes('connection')) {
          return 'Network connection error. Please check your internet connection and try again.';
        }
        
        // If no specific mapping found, return a generic user-friendly message
        return 'Login failed. Please check your email and password and try again.';
    }
  }

  /**
   * Maps registration-specific errors to user-friendly messages
   * @param error - The Firebase error object or error message
   * @returns User-friendly error message
   */
  mapRegistrationError(error: any): string {
    const mappedError = this.mapFirebaseError(error);
    
    // If it's a generic login error, make it registration-specific
    if (mappedError === 'Login failed. Please check your email and password and try again.') {
      return 'Registration failed. Please check your information and try again.';
    }
    
    return mappedError;
  }

  /**
   * Checks if an error indicates the user is not in the database
   * @param error - The error object or message
   * @returns True if the error indicates user not found
   */
  isUserNotFoundError(error: any): boolean {
    const errorCode = error?.code || '';
    const errorMessage = (error?.message || error || '').toLowerCase();
    
    return errorCode === 'auth/user-not-found' || 
           errorMessage.includes('user not found') ||
           errorMessage.includes('user-not-found') ||
           errorMessage.includes('no account found');
  }

  /**
   * Checks if an error indicates invalid credentials
   * @param error - The error object or message
   * @returns True if the error indicates invalid credentials
   */
  isInvalidCredentialsError(error: any): boolean {
    const errorCode = error?.code || '';
    const errorMessage = (error?.message || error || '').toLowerCase();
    
    return errorCode === 'auth/invalid-credential' ||
           errorCode === 'auth/invalid-login-credentials' ||
           errorCode === 'auth/wrong-password' ||
           errorMessage.includes('invalid-credential') ||
           errorMessage.includes('invalid credential') ||
           errorMessage.includes('wrong password');
  }
}
