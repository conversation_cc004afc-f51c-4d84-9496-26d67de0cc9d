import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, of, throwError, from, firstValueFrom } from 'rxjs';
import { catchError, tap, map, switchMap } from 'rxjs/operators';

// Firebase imports
import { Firestore, collection, doc, getDoc, getDocs, query, where,
  orderBy, addDoc, updateDoc, deleteDoc, Timestamp, collectionData, DocumentData } from '@angular/fire/firestore';

import { AuthService } from '../../services/auth.service';
import { LocalStorageService } from '../../services/local-storage.service';
import { Db } from '../../../../../app/db';
import { SecuraService } from '../../../../../app/secura.service';
import { AppointmentType, DoctorType, UserType, AppointmentStatusType } from '../../../../../app/type';
import { FirebaseAppointmentService } from '../../../../../app/features/shared/services/firebase-appointment.service';

export interface AppointmentPatient {
  appointmentId: string;
  doctorId: string;
  doctorName: string;
  specialization?: string;
  appointmentDate: Date;
  appointmentTime: string;
  durationMinutes: number;
  reasonForVisit: string;
  status: 'Pending' | 'Approved' | 'Rejected' | 'Completed' | 'Cancelled' | 'Available';
  doctorNotes?: string;
}

export interface Doctor {
  doctorId: string;
  name: string;
  specialization: string;
  profilePicture?: string;
  email?: string;
  rating?: number;
  reviewCount?: number;
}

export interface DoctorSchedule {
  id: number;
  doctorId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  slotDurationMinutes: number;
  isRecurring: boolean;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AppointmentService {
  private apiUrl = "";
  private currentAppointmentSubject = new BehaviorSubject<AppointmentPatient | null>(null);
  private appointmentsSubject = new BehaviorSubject<AppointmentPatient[]>([]);
  private availableSlotsSubject = new BehaviorSubject<AppointmentPatient[]>([]);
  private doctorSchedulesSubject = new BehaviorSubject<DoctorSchedule[]>([]);
  private selectedDoctorSubject = new BehaviorSubject<Doctor | null>(null);

  private db = inject(Db);
  private securaService = inject(SecuraService);
  private authService = inject(AuthService);
  private firestore = inject(Firestore);
  private storage = inject(LocalStorageService);
  private firebaseAppointmentService = inject(FirebaseAppointmentService);

  // Expose as observables
  public currentAppointment$ = this.currentAppointmentSubject.asObservable();
  public appointments$ = this.appointmentsSubject.asObservable();
  public availableSlots$ = this.availableSlotsSubject.asObservable();
  public doctorSchedules$ = this.doctorSchedulesSubject.asObservable();
  public selectedDoctor$ = this.selectedDoctorSubject.asObservable();

  constructor(private http: HttpClient) {
    this.loadInitialData();
  }

  private loadInitialData() {
    this.loadAppointmentsForCurrentUser();
    this.loadAvailableSlots();
    this.syncAvailabilityWithFirebase();
  }

  loadAppointmentsForCurrentUser() {
    console.log('Loading appointments for current user from Firebase');

    // Get patient ID from auth service
    const patientId = this.authService.getPatientId();

    if (patientId) {
      console.log('Patient ID found, loading appointments from Firebase:', patientId);
      this.loadAppointmentsFromFirestore(patientId);
    } else {
      console.warn('No patient ID available from auth service, trying to get from user info');

      // Try to get from user info
      const userInfo = this.authService.getUserInfo();
      if (userInfo && (userInfo.patientId || userInfo.id)) {
        const id = userInfo.patientId || userInfo.id;
        console.log('Using ID from user info for Firebase query:', id);
        if (id) {
          this.storage.setItem('patient_id', id);
          this.loadAppointmentsFromFirestore(id);
        } else {
          console.error('ID is undefined, cannot load appointments from Firebase');
          this.appointmentsSubject.next([]);
        }
      } else {
        console.error('No patient ID available in user info either, cannot load appointments from Firebase');
        this.appointmentsSubject.next([]);
      }
    }
  }

  private loadAppointmentsFromFirestore(patientId: string) {
    console.log('Loading appointments from Firestore for patient ID:', patientId);

    // Create a query to get all appointments for this patient
    const appointmentsRef = collection(this.firestore, 'appointments');
    console.log('Querying Firestore collection: appointments');

    // We have two potential field names in Firestore
    console.log('Creating query for patientId field...');
    const patientIdQuery = query(
      appointmentsRef,
      where('patientId', '==', patientId)
    );

    console.log('Creating query for patient_id field...');
    const patient_idQuery = query(
      appointmentsRef,
      where('patient_id', '==', patientId)
    );

    // Get appointments from both queries (handling both field names)
    console.log('Executing both queries...');
    from(Promise.all([getDocs(patientIdQuery), getDocs(patient_idQuery)]))
      .pipe(
        map(([snapshot1, snapshot2]) => {
          console.log('Query results received from Firestore');
          console.log(`First query returned ${snapshot1.size} documents`);
          console.log(`Second query returned ${snapshot2.size} documents`);

          const appointments: AppointmentPatient[] = [];
          const processedIds = new Set<string>();

          // Process both query results, avoiding duplicates
          [snapshot1, snapshot2].forEach((querySnapshot, index) => {
            if (querySnapshot.empty) {
              console.log(`Query ${index + 1} returned no results`);
              return;
            }

            querySnapshot.forEach(doc => {
              // Skip if we've already processed this document
              if (processedIds.has(doc.id)) {
                console.log(`Skipping duplicate document: ${doc.id}`);
                return;
              }
              processedIds.add(doc.id);

              const data = doc.data();
              console.log(`Processing appointment: ${doc.id}`);

              // Get doctorId with fallback options
              const doctorId = data['doctorId'] || data['doctor_id'] || '';

              // Handle different date formats/storage
              let appointmentDate: Date;
              if (data['appointmentDate'] instanceof Timestamp) {
                appointmentDate = data['appointmentDate'].toDate();
                console.log(`Timestamp date found: ${appointmentDate}`);
              } else if (data['date']) {
                appointmentDate = new Date(data['date']);
                console.log(`String date found: ${data['date']}`);
              } else if (typeof data['appointmentDate'] === 'string') {
                appointmentDate = new Date(data['appointmentDate']);
                console.log(`String appointmentDate found: ${data['appointmentDate']}`);
              } else {
                // Default to current date if no valid date found
                appointmentDate = new Date();
                console.warn('No valid date format found for appointment', doc.id);
              }

              // Get appointment time with fallbacks
              const appointmentTime = data['appointmentTime'] || data['time'] || '';

              // Map the status to ensure it matches the enum
              let status: AppointmentPatient['status'] = 'Pending';
              const dataStatus = (data['status'] || 'Pending').toString();
              console.log(`Appointment status: ${dataStatus}`);

              if (['Approved', 'Rejected', 'Completed', 'Cancelled', 'Available', 'Pending'].includes(dataStatus)) {
                status = dataStatus as AppointmentPatient['status'];
              }

              const appointment: AppointmentPatient = {
                appointmentId: doc.id,
                doctorId: doctorId,
                doctorName: data['doctorName'] || 'Unknown Doctor',
                specialization: data['specialization'] || '',
                appointmentDate: appointmentDate,
                appointmentTime: appointmentTime,
                durationMinutes: data['durationMinutes'] || 30,
                reasonForVisit: data['reasonForVisit'] || '',
                status: status,
                doctorNotes: data['doctorNotes'] || ''
              };

              appointments.push(appointment);
              console.log(`Added appointment with ID: ${doc.id}, status: ${status}`);
            });
          });

          console.log(`Loaded ${appointments.length} appointments from Firebase for patient ${patientId}`);

          if (appointments.length > 0) {
            // Log first appointment for debugging
            console.log('First appointment:', JSON.stringify(appointments[0]));
          }

          // Sort appointments by date and time
          return this.sortAppointments(appointments);
        }),
        catchError(error => {
          console.error('Error loading appointments from Firestore:', error);
          console.log('Attempting to load from legacy API...');
          this.loadAppointmentsFromAPI(patientId);
          return of([]);
        })
      )
      .subscribe(appointments => {
        console.log(`Pushing ${appointments.length} appointments to subject`);
        this.appointmentsSubject.next(appointments);

        // Check if we have a current appointment
        const now = new Date();
        const currentAppointment = appointments.find(appointment => {
          const appointmentDate = new Date(appointment.appointmentDate);
          const appointmentTime = this.parseTime(appointment.appointmentTime);
          const appointmentDateTime = new Date(
            appointmentDate.getFullYear(),
            appointmentDate.getMonth(),
            appointmentDate.getDate(),
            appointmentTime.getHours(),
            appointmentTime.getMinutes()
          );

          const appointmentEndTime = new Date(appointmentDateTime);
          appointmentEndTime.setMinutes(appointmentEndTime.getMinutes() + appointment.durationMinutes);

          return now >= appointmentDateTime && now <= appointmentEndTime && appointment.status === 'Approved';
        });

        if (currentAppointment) {
          console.log('Found current active appointment:', currentAppointment.appointmentId);
          this.currentAppointmentSubject.next(currentAppointment);
        }

        // Update local storage for offline access
        this.updateLocalAppointments(appointments);
      });
  }

  // Helper method to sort appointments by date and time
  private sortAppointments(appointments: AppointmentPatient[]): AppointmentPatient[] {
    return [...appointments].sort((a, b) => {
      // First sort by date
      const dateA = new Date(a.appointmentDate);
      const dateB = new Date(b.appointmentDate);

      const dateCompare = dateA.getTime() - dateB.getTime();
      if (dateCompare !== 0) return dateCompare;

      // Then by time if dates are the same
      const timeA = this.parseTime(a.appointmentTime);
      const timeB = this.parseTime(b.appointmentTime);
      return timeA.getTime() - timeB.getTime();
    });
  }

  // Helper to update local storage with appointments from Firebase
  private updateLocalAppointments(appointments: AppointmentPatient[]): void {
    // Convert to local appointment format with correct type mapping
    const localAppointments = appointments.map(appointment => {
      // Convert the status to match AppointmentStatusType
      let mappedStatus: any = appointment.status;
      // Ensure 'Rejected' is mapped to a compatible status if needed
      if (appointment.status === 'Rejected') {
        mappedStatus = 'Cancelled'; // Map to a compatible status
      }

      return {
        id: appointment.appointmentId,
        patient_id: this.authService.getPatientId() || '',
        doctor_id: appointment.doctorId,
        date: appointment.appointmentDate.toISOString().split('T')[0],
        time: appointment.appointmentTime,
        status: mappedStatus,
        patientName: this.authService.getUserInfo()?.full_name || '',
        reasonForVisit: appointment.reasonForVisit,
        doctorNotes: appointment.doctorNotes
      };
    });

    // Replace existing appointments in local storage
    this.db.appointmentTable.set(localAppointments as any);
    this.storage.setItem('APPOINTMENT_TABLE', localAppointments);
  }

  private fetchDoctorDetailsFromFirestore(doctorId: string): Observable<any> {
    if (!doctorId) {
      return of(null);
    }

    return from(getDoc(doc(this.firestore, 'doctors', doctorId))).pipe(
      map(docSnapshot => {
        if (docSnapshot.exists()) {
          return { id: docSnapshot.id, ...docSnapshot.data() };
        }
        return null;
      }),
      catchError(error => {
        console.error(`Error fetching doctor details for ID ${doctorId}:`, error);
        return of(null);
      })
    );
  }

  private loadAppointments() {
    const patientId = this.authService.getPatientId();

    if (!patientId) {
      console.warn('No patient ID available, skipping appointment loading');
      this.appointmentsSubject.next([]);
      return;
    }

    console.log('Loading appointments from legacy API for patient ID:', patientId);

    this.http.get<AppointmentPatient[]>(`${this.apiUrl}/appointments/patient/${patientId}`)
      .pipe(
        map(appointments => {
          console.log('Appointments loaded from API:', appointments);
          return appointments.map(app => {
            // Find doctor info for this appointment
            const doctor = this.db.userTable().find(user => user.id === app.doctorId);
            const doctorDetails = doctor ? this.db.doctorTable().find(d => d.user_id === doctor.id) : null;

            return {
              ...app,
              appointmentDate: new Date(app.appointmentDate),
              doctorName: doctor ? `${doctor.firstname} ${doctor.lastname}` : 'Unknown Doctor',
              specialization: doctorDetails?.specialisation || ''
            };
          });
        }),
        catchError(error => {
          console.error('Error loading appointments from API:', error);
          return of([]);
        })
      )
      .subscribe(appointments => {
        this.appointmentsSubject.next(appointments);
      });
  }

  private fetchPatientIdFromProfile() {
    // Get the auth token
    const token = this.storage.getItem<string>('med_secure_token');
    if (!token) {
      console.warn('No auth token available, cannot fetch profile');
      return;
    }

    const headers = { 'Authorization': `Bearer ${token}` };

    // Fetch the patient profile to get the ID
    this.http.get<any>(`${this.apiUrl}/patients/profile/info`, { headers }).pipe(
      catchError(error => {
        console.error('Error fetching patient profile:', error);
        return of(null);
      })
    ).subscribe(response => {
      if (response && response.success && response.data) {
        const patientId = response.data.id || response.data.patientId;
        if (patientId) {
          console.log('Retrieved patient ID from profile:', patientId);
          // Save the patient ID using storage service
          this.storage.setItem('patient_id', patientId);
          // Now load appointments with the retrieved ID
          this.loadAppointments();
        }
      }
    });
  }

  private loadAvailableSlots() {
    console.log('Loading available appointment slots from Firebase');

    // First try to get the user info for doctorId
    const doctorInfo = this.storage.getItem<any>('current_doctor');

    if (!doctorInfo || !doctorInfo.id) {
      console.warn('No doctor info available for loading slots');
      this.availableSlotsSubject.next([]);
      return;
    }

    const doctorId = doctorInfo.id;
    console.log('Loading available slots for doctor ID:', doctorId);

    // Use Firebase to load appointments with status "Available"
    const appointmentsRef = collection(this.firestore, 'appointments');
    const availableSlotsQuery = query(
      appointmentsRef,
      where('doctorId', '==', doctorId),
      where('status', '==', 'Available'),
      orderBy('appointmentDate', 'asc')
    );

    from(getDocs(availableSlotsQuery))
      .pipe(
        map(querySnapshot => {
          const slots: AppointmentPatient[] = [];

          querySnapshot.forEach(doc => {
            const data = doc.data();

            // Convert Firestore Timestamp to JS Date if needed
            const appointmentDate = data['appointmentDate'] instanceof Timestamp
              ? data['appointmentDate'].toDate()
              : new Date(data['appointmentDate']);

            slots.push({
              appointmentId: doc.id,
              doctorId: data['doctorId'] || data['doctor_id'] || '',
              doctorName: data['doctorName'] || 'Available Slot',
              appointmentDate: appointmentDate,
              appointmentTime: data['appointmentTime'] || data['time'] || '',
              durationMinutes: data['durationMinutes'] || 30,
              reasonForVisit: '',
              status: 'Available'
            });
          });

          console.log('Loaded available slots from Firebase:', slots);
          return slots;
        }),
        catchError(error => {
          console.error('Error loading available slots from Firebase:', error);

          // Fallback to local storage if Firebase fails
          console.log('Falling back to local storage for available slots');

          try {
            const availableAppointments = this.db.appointmentTable()
              .filter(app => app.status === 'Available' as AppointmentStatusType);

            if (availableAppointments.length > 0) {
              console.log('Found available slots in local storage:', availableAppointments.length);

              const slots = availableAppointments.map(app => ({
                appointmentId: app.id,
                doctorId: app.doctor_id,
                doctorName: 'Available Slot',
                appointmentDate: new Date(app.date),
                appointmentTime: app.time,
                durationMinutes: 30,
                reasonForVisit: '',
                status: 'Available' as const
              }));

              return of(slots);
            }
          } catch (err) {
            console.error('Error parsing local storage appointments:', err);
          }

          return of([]);
        })
      )
      .subscribe(slots => {
        this.availableSlotsSubject.next(slots);
      });
  }

  private getCurrentDoctor(): Doctor | null {
    return this.selectedDoctorSubject.getValue();
  }

  public getDoctorDetails(doctorId: string): Observable<Doctor> {
    return this.http.get<Doctor>(`${this.apiUrl}/doctors/${doctorId}`).pipe(
      tap(doctor => {
        // Store the doctor details in the subject
        this.selectedDoctorSubject.next(doctor);
        // Also store in localStorage for persistence
        this.storage.setItem('currentDoctor', doctor);
      }),
      catchError(error => {
        console.error('Error loading doctor details:', error);
        return throwError(() => new Error('Failed to load doctor details'));
      })
    );
  }

  public setCurrentDoctor(doctor: Doctor) {
    this.selectedDoctorSubject.next(doctor);
    this.storage.setItem('currentDoctor', doctor);
    // Load doctor's schedules when setting current doctor
    this.loadAvailableSlots();
  }

  private getAppointmentsWithAvailableSlotsByDate(date: Date): AppointmentPatient[] {
    // Get appointments
    const appointments = this.appointmentsSubject.getValue();
    // Also get available slots
    const availableSlots = this.availableSlotsSubject.getValue();

    // Combine them
    const combinedAppointments = [...appointments, ...availableSlots];

    // Filter by date
    return combinedAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return appointmentDate.toDateString() === date.toDateString();
    });
  }

  public getAvailableSlotsByDate(date: Date): AppointmentPatient[] {
    // Use only available slots, not all appointments
    const availableSlots = this.availableSlotsSubject.getValue();

    return availableSlots.filter(slot => {
      const slotDate = new Date(slot.appointmentDate);
      return slotDate.toDateString() === date.toDateString();
    });
  }

  public getAvailableSlotsByDoctorAndDate(doctorId: string, date: Date): AppointmentPatient[] {
    // Get all available slots
    const availableSlots = this.availableSlotsSubject.getValue();

    // Filter by both doctor ID and date
    return availableSlots.filter(slot => {
      const slotDate = new Date(slot.appointmentDate);
      return slot.doctorId === doctorId && slotDate.toDateString() === date.toDateString();
    });
  }

  public bookAppointment(slot: AppointmentPatient, reason: string): Observable<AppointmentPatient> {
    const patientId = this.authService.getPatientId();

    if (!patientId) {
      return throwError(() => new Error('No patient ID available'));
    }

    // Get user info for patient name
    const userInfo = this.authService.getUserInfo();
    const patientName = userInfo ? userInfo.full_name : 'Unknown Patient';

    // Create new appointment object
    const appointmentData = {
      patientId: patientId,
      patientName: patientName,
      doctorId: slot.doctorId,
      doctorName: slot.doctorName,
      specialization: slot.specialization || '',
      appointmentDate: slot.appointmentDate,
      appointmentTime: slot.appointmentTime,
      durationMinutes: slot.durationMinutes || 30,
      reasonForVisit: reason,
      status: 'Pending' as AppointmentPatient['status'], // Cast as the correct type
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('Booking appointment in Firestore:', appointmentData);

    // Add to Firestore
    const appointmentsCollection = collection(this.firestore, 'appointments');

    return from(addDoc(appointmentsCollection, appointmentData)).pipe(
      map(docRef => {
        const newAppointment: AppointmentPatient = {
          appointmentId: docRef.id,
          doctorId: slot.doctorId,
          doctorName: slot.doctorName,
          specialization: slot.specialization,
          appointmentDate: slot.appointmentDate,
          appointmentTime: slot.appointmentTime,
          durationMinutes: slot.durationMinutes,
          reasonForVisit: reason,
          status: 'Pending'
        };

        // Update the appointments list
        const currentAppointments = this.appointmentsSubject.getValue();
        this.appointmentsSubject.next([...currentAppointments, newAppointment]);

        console.log('Appointment booked successfully:', newAppointment);
        return newAppointment;
      }),
      catchError(error => {
        console.error('Error booking appointment in Firestore:', error);
        // Fall back to legacy method
        return this.bookAppointmentLegacy(slot, reason);
      })
    );
  }

  private bookAppointmentLegacy(slot: AppointmentPatient, reason: string): Observable<AppointmentPatient> {
    const patientId = this.authService.getPatientId();

    if (!patientId) {
      return throwError(() => new Error('No patient ID available'));
    }

    const bookingData = {
      patientId: patientId,
      doctorId: slot.doctorId,
      appointmentDate: new Date(slot.appointmentDate).toISOString(),
      appointmentTime: slot.appointmentTime,
      reasonForVisit: reason
    };

    return this.http.post<AppointmentPatient>(`${this.apiUrl}/appointments/book`, bookingData).pipe(
      tap(newAppointment => {
        // Update the appointments list
        const currentAppointments = this.appointmentsSubject.getValue();
        this.appointmentsSubject.next([...currentAppointments, {
          ...newAppointment,
          appointmentDate: new Date(newAppointment.appointmentDate)
        }]);

        console.log('Appointment booked successfully via API:', newAppointment);
      }),
      catchError(error => {
        console.error('Error booking appointment via API:', error);
        return throwError(() => new Error('Failed to book appointment'));
      })
    );
  }

  public cancelAppointment(appointmentId: string, reason: string): Observable<void> {
    console.log('Cancelling appointment in Firestore:', appointmentId);

    const appointmentRef = doc(this.firestore, 'appointments', appointmentId);

    return from(updateDoc(appointmentRef, {
      status: 'Cancelled' as AppointmentPatient['status'],
      cancellationReason: reason,
      updatedAt: new Date()
    })).pipe(
      map(() => {
        // Update the appointments list
        const currentAppointments = this.appointmentsSubject.getValue();
        const updatedAppointments = currentAppointments.map(appointment => {
          if (appointment.appointmentId === appointmentId) {
            return { ...appointment, status: 'Cancelled' as AppointmentPatient['status'] };
          }
          return appointment;
        });

        this.appointmentsSubject.next(updatedAppointments);
        console.log('Appointment cancelled successfully in Firestore');
        return;
      }),
      catchError(error => {
        console.error('Error cancelling appointment in Firestore:', error);
        // Fall back to legacy method
        return this.cancelAppointmentLegacy(appointmentId, reason);
      })
    );
  }

  private cancelAppointmentLegacy(appointmentId: string, reason: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/appointments/${appointmentId}/cancel`, { reason }).pipe(
      tap(() => {
        // Update the appointments list
        const currentAppointments = this.appointmentsSubject.getValue();
        const updatedAppointments = currentAppointments.map(appointment => {
          if (appointment.appointmentId === appointmentId) {
            return { ...appointment, status: 'Cancelled' as AppointmentPatient['status'] };
          }
          return appointment;
        });

        this.appointmentsSubject.next(updatedAppointments);
        console.log('Appointment cancelled successfully via API');
      }),
      catchError(error => {
        console.error('Error cancelling appointment via API:', error);
        return throwError(() => new Error('Failed to cancel appointment'));
      })
    );
  }

  // Helper method to parse time string
  private parseTime(timeString: string): Date {
    const time = new Date();
    const timeParts = timeString.split(':');

    if (timeParts.length >= 2) {
      const hours = parseInt(timeParts[0], 10);
      const minutes = parseInt(timeParts[1], 10);

      time.setHours(hours, minutes, 0, 0);
    }

    return time;
  }

  public getCurrentAppointment(): AppointmentPatient | null {
    return this.currentAppointmentSubject.getValue();
  }

  public setCurrentAppointment(appointment: AppointmentPatient | null): void {
    this.currentAppointmentSubject.next(appointment);
  }

  public getUpcomingAppointments(): AppointmentPatient[] {
    const now = new Date();
    const allAppointments = this.appointmentsSubject.getValue();

    return allAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return (
        appointmentDate >= now &&
        (appointment.status === 'Approved' || appointment.status === 'Pending')
      );
    }).sort((a, b) => {
      const dateA = new Date(a.appointmentDate);
      const dateB = new Date(b.appointmentDate);
      return dateA.getTime() - dateB.getTime();
    }).slice(0, 5); // Get the next 5 upcoming appointments
  }

  public getAllAppointments(): AppointmentPatient[] {
    // Return current appointments from the subject (which are loaded from Firebase)
    return this.appointmentsSubject.getValue();
  }

  public getAppointmentsByStatus(status: AppointmentPatient['status']): AppointmentPatient[] {
    const allAppointments = this.appointmentsSubject.getValue();
    return allAppointments.filter(appointment => appointment.status === status);
  }

  public getAppointmentsByDate(date: Date): AppointmentPatient[] {
    const allAppointments = this.appointmentsSubject.getValue();
    const dateString = date.toISOString().split('T')[0];

    return allAppointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointmentDate);
      return (
        appointmentDate.toISOString().split('T')[0] === dateString
      );
    }).sort((a, b) => {
      const timeA = this.parseTime(a.appointmentTime).getTime();
      const timeB = this.parseTime(b.appointmentTime).getTime();
      return timeA - timeB;
    });
  }

  private addAppointmentsFromSecura(patientId: string, status: 'Approved' | 'Completed' | 'Cancelled'): void {
    // Get appointments from SecuraService with the specified status
    const securaAppointments = this.securaService.patientAppointments(patientId, status);

    // Update local database with appointments of the specified status
    securaAppointments.forEach((app) => {
      if (app.status === status && app.patient_id === patientId) {
        const existingApp = this.db.appointmentTable().find(a => a.id === app.id);
        if (existingApp) {
          existingApp.status = status;
          this.db.saveToLocalStorage();
        } else {
          this.db.addAppointment(app);
        }
      }
    });
  }

  public refreshAppointments(): Observable<AppointmentPatient[]> {
    console.log('Refreshing appointments from Firebase...');
    // Load appointments for current user and return the observable
    return new Observable<AppointmentPatient[]>(observer => {
      // Get patient ID from auth service
      const patientId = this.authService.getPatientId();

      if (patientId) {
        console.log('Refreshing appointments for patient ID:', patientId);
        // Create a subscription to the Firebase query
        this.loadAppointmentsFromFirestore(patientId);

        // Listen for the next emission from the appointments subject
        const subscription = this.appointmentsSubject.subscribe(appointments => {
          console.log(`Refreshed ${appointments.length} appointments from Firebase`);
          observer.next(appointments);
          observer.complete();
          subscription.unsubscribe(); // Clean up to prevent memory leaks
        });
      } else {
        console.warn('No patient ID available for refreshing appointments');
        observer.next([]);
        observer.complete();
      }
    });
  }

  public getDoctorSchedules(doctorId: string): Observable<DoctorSchedule[]> {
    return this.http.get<DoctorSchedule[]>(`${this.apiUrl}/api/schedules/doctor/${doctorId}`).pipe(
      map(schedules => schedules.filter(s => s.isActive)),
      tap(schedules => {
        this.doctorSchedulesSubject.next(schedules);
      }),
      catchError(error => {
        console.error('Error loading doctor schedules:', error);
        return of([]);
      })
    );
  }

  public formatTimeForDisplay(time: string): string {
    if (!time) return '';

    const [hours, minutes] = time.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHour = hours % 12 || 12;
    return `${displayHour}:${minutes.toString().padStart(2, '0')} ${period}`;
  }

  public getDayName(dayIndex: number): string {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return days[dayIndex] || '';
  }

  public getCompletedAppointments(): AppointmentPatient[] {
    return this.appointmentsSubject.getValue()
      .filter(appointment => appointment.status === 'Completed')
      .sort((a, b) => {
        // Sort completed appointments by date (most recent first)
        const dateA = new Date(a.appointmentDate).getTime();
        const dateB = new Date(b.appointmentDate).getTime();
        return dateB - dateA; // Reverse order for completed appointments
      });
  }

  public getCancelledAppointments(): AppointmentPatient[] {
    return this.appointmentsSubject.getValue()
      .filter(appointment => ['Cancelled', 'Rejected'].includes(appointment.status))
      .sort((a, b) => {
        const dateA = new Date(a.appointmentDate).getTime();
        const dateB = new Date(b.appointmentDate).getTime();
        return dateB - dateA; // Most recently cancelled first
      });
  }

  /**
   * Ensure availability data is synchronized between Firebase and localStorage
   */
  private syncAvailabilityWithFirebase(): void {
    console.log('Initializing availability synchronization with Firebase');

    // First check if we're a doctor (only doctors can see availability)
    const currentDoctor = this.storage.getItem<any>('current_doctor');
    if (!currentDoctor || !currentDoctor.id) {
      console.log('Not logged in as a doctor, skipping availability sync');
      return;
    }

    const doctorId = currentDoctor.id;
    console.log(`Starting availability sync for doctor ID: ${doctorId}`);

    // Use the enhanced sync method from FirebaseAppointmentService
    this.firebaseAppointmentService.syncAvailabilityData(doctorId)
      .subscribe({
        next: () => {
          console.log('Successfully synchronized availability data from mobile service');
        },
        error: (error) => {
          console.error('Error synchronizing availability data from mobile service:', error);
        }
      });
  }

  // Add the missing loadAppointmentsFromAPI method
  private loadAppointmentsFromAPI(patientId: string) {
    console.log('Falling back to API for appointments with patient ID:', patientId);
    this.loadAppointments();
  }
}
