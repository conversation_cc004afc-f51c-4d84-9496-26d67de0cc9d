import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {

  constructor() { }

  /**
   * Set an item in localStorage with JSON stringification
   */
  setItem<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error(`Error saving to localStorage: ${key}`, error);
    }
  }

  /**
   * Get an item from localStorage with JSON parsing
   */
  getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        return null;
      }

      try {
        // Try to parse as JSON first
        return JSON.parse(item) as T;
      } catch (parseError) {
        // For certain keys that might be stored as plain strings, return the raw value
        if (
          key === 'med_secure_patient_id' ||
          key === 'patient_id' ||
          key === 'patient_user_id'
        ) {
          console.log(`Returning raw string value for ${key}:`, item);
          return item as unknown as T;
        }

        // For other keys, log the error and return null
        console.error(`Error parsing JSON from localStorage for ${key}:`, parseError);
        return null;
      }
    } catch (error) {
      console.error(`Error retrieving from localStorage: ${key}`, error);
      return null;
    }
  }

  /**
   * Remove an item from localStorage
   */
  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`Error removing from localStorage: ${key}`, error);
    }
  }

  /**
   * Clear all items from localStorage
   */
  clear(): void {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage', error);
    }
  }
}
