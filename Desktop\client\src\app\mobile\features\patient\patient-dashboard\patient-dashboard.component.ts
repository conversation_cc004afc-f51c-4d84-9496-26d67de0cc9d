import { Component, type OnInit, ViewChild, type ElementRef, inject, On<PERSON><PERSON>roy } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Router } from "@angular/router";
import { AuthService } from "../../services/auth.service";
import { Slide } from "../../../model/cancel-reason.model";
import { HttpClientModule } from '@angular/common/http';
import { AppointmentService, AppointmentPatient } from '../../appointments/service/appointment.service';
import { NavbarComponent } from "../../shared/navbar/navbar.component";
import { PatientProfileService } from '../services/patient-profile.service';
import { Subscription } from 'rxjs';
import { Storage } from '../../../../storage';
import { FirebaseResetComponent } from '../../../../features/shared/components/firebase-reset/firebase-reset.component';
import { FirebaseResetButtonComponent } from '../../../../features/shared/components/firebase-reset-button/firebase-reset-button.component';

interface DashboardDate {
  day: number;
  weekday: string;
  fullDate: string;
}

@Component({
  selector: "app-patient-dashboard",
  standalone: true,
  imports: [CommonModule, HttpClientModule, NavbarComponent, FirebaseResetComponent, FirebaseResetButtonComponent],
  templateUrl: "./patient-dashboard.component.html",
  styleUrls: ["./patient-dashboard.component.css"],
})
export class PatientDashboardComponent implements OnInit, OnDestroy {
  @ViewChild("datesContainer") datesContainer!: ElementRef;

  private authService = inject(AuthService);
  userName = "";
  profileImage: string | null = null;
  private profileSubscription: Subscription | undefined;
  private authSubscription: Subscription | undefined;

  currentSlideIndex = 0;
  slides: Slide[] = [
    {
      title: "Book your appointment",
      description: "Convenient, accessible, efficient healthcare scheduling online platform!",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Vc0Pzs3DNZSgP5C6q7kjk8g6nRLyPJ.png",
      buttonText: "Book Now",
    },
    {
      title: "Mental Health Awareness",
      description: "Your Health, Your Wealth Regular Checkups Save Lives!",
      image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Vc0Pzs3DNZSgP5C6q7kjk8g6nRLyPJ.png",
      buttonText: "Book Now",
    },
  ];

  dates: DashboardDate[] = [];
  selectedDate: DashboardDate | null = null;
  currentMonth = "";
  currentAppointment: AppointmentPatient | null = null;
  upcomingAppointments: AppointmentPatient[] = [];
  appointmentsMap: { [key: string]: AppointmentPatient[] } = {};
  allAppointments: AppointmentPatient[] = []; // All booked appointments

  times: string[] = ["09:00 AM", "10:00 AM", "11:00 AM", "12:00 PM", "13:00 PM", "14:00 PM", "15:00 PM", "16:00 PM","17:00 PM"];

  constructor(
    private appointmentService: AppointmentService,
    private router: Router,
    private profileService: PatientProfileService,
    private storage: Storage
  ) {
    this.currentMonth = new Date().toLocaleString("default", { month: "long" });
  }

  async loadUserProfile() {
    try {
      // First try to get user data from Firebase through AuthService
      const user = await this.authService.getCurrentUser();
      if (user) {
        console.log('User data from Firebase:', user);
        this.userName = user.displayName || '';
        this.profileImage = user.profileImage;

        // Update local storage for other components to use
        if (user.displayName) {
          this.storage.setItem('user_displayname', user.displayName);
        }
        return;
      }

      // If Firebase data is not available, try profile service
      const profileData = this.profileService.getCurrentProfile();
      if (profileData) {
        console.log('Profile data from profile service:', profileData);
        this.userName = profileData.fullName;
        this.profileImage = profileData.profilePicture || null;
        return;
      }

      // As a last resort, try getting username from storage
      const storedUsername = this.storage.getItem<string>('user_displayname');
      if (storedUsername) {
        console.log('Username from storage:', storedUsername);
        this.userName = storedUsername;
      } else {
        console.warn('No user data found from any source');
        // Redirect to login if no user data is found
        this.router.navigate(['/mobile/login']);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  ngOnInit(): void {
    // First load the user profile to show user info immediately
    this.loadUserProfile();

    // Then load other data
    this.generateDates();
    this.loadAppointments();
    this.startSlideshow();

    // Set up subscriptions
    this.appointmentService.appointments$.subscribe(() => {
      this.loadAppointments();
    });

    this.profileSubscription = this.profileService.currentProfile$.subscribe(profile => {
      if (profile) {
        console.log('Profile updated in dashboard:', profile);
        this.userName = profile.fullName;
        this.profileImage = profile.profilePicture || null;
      }
    });

    // Also subscribe to auth service for Firebase user changes
    this.authSubscription = this.authService.currentUser$.subscribe(user => {
      if (user) {
        console.log('Firebase user updated:', user);
        this.userName = user.displayName;
        this.profileImage = user.profileImage;

        // Update local storage
        if (user.displayName) {
          this.storage.setItem('user_displayname', user.displayName);
        }
      }
    });
  }

  ngOnDestroy(): void {
    if (this.profileSubscription) {
      this.profileSubscription.unsubscribe();
    }

    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  private startSlideshow() {
    setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide() {
    this.currentSlideIndex = (this.currentSlideIndex + 1) % this.slides.length;
  }

  previousSlide() {
    this.currentSlideIndex = (this.currentSlideIndex - 1 + this.slides.length) % this.slides.length;
  }

  navigateToBooking() {
    this.router.navigate(['/mobile/appointment-booking']);
  }

  navigateToAppointmentDetails(appointment: AppointmentPatient) {
    this.router.navigate(['/mobile/appointment-details'], { state: { appointment } });
  }

  generateDates() {
    const today = new Date();
    for (let i = -3; i < 4; i++) {
      const newDate = new Date(today);
      newDate.setDate(today.getDate() + i);
      this.dates.push({
        day: newDate.getDate(),
        weekday: newDate.toLocaleString("en-US", { weekday: "short" }),
        fullDate: newDate.toISOString().split("T")[0],
      });
    }
  }

  loadAppointments(): void {
    console.log('Loading appointments for patient dashboard from Firebase...');

    // Force a refresh to get the latest data from Firebase and subscribe to the results
    this.appointmentService.refreshAppointments().subscribe({
      next: (appointments) => {
        console.log(`Successfully loaded ${appointments.length} appointments from Firebase`);

        // Get current appointment (if any)
        this.currentAppointment = this.appointmentService.getCurrentAppointment();

        // Get upcoming appointments
        this.upcomingAppointments = this.appointmentService.getUpcomingAppointments();
        console.log('Upcoming appointments loaded:', this.upcomingAppointments.length);

        // Load ALL appointments for the comprehensive view
        this.allAppointments = appointments; // Use the appointments we just loaded
        console.log('All appointments loaded from Firebase:', this.allAppointments.length);

        if (this.allAppointments.length === 0) {
          console.log('No appointments found in Firebase for this patient.');
        } else {
          console.log('Appointment statuses from Firebase:', this.allAppointments.map(a => a.status).join(', '));
          console.log('First appointment details:', JSON.stringify(this.allAppointments[0]));
        }

        // Sort allAppointments by date, with the most recent first
        this.allAppointments.sort((a, b) => {
          const dateA = new Date(a.appointmentDate);
          const dateB = new Date(b.appointmentDate);
          return dateB.getTime() - dateA.getTime(); // Reverse chronological order
        });

        // Populate appointments map for calendar view
        this.populateAppointmentsMap();

        // Select current date in the calendar
        this.selectCurrentDate();
      },
      error: (error) => {
        console.error('Error refreshing appointments from Firebase:', error);
        // Try to load appointments directly as a fallback
        this.allAppointments = this.appointmentService.getAllAppointments();
        console.log('Fallback loaded', this.allAppointments.length, 'appointments');

        // Continue with the rest of the loading process
        this.populateAppointmentsMap();
        this.selectCurrentDate();
      }
    });
  }

  // Add a separate method to populate the appointments map
  private populateAppointmentsMap(): void {
    this.appointmentsMap = {};
    this.dates.forEach((date) => {
      const appointmentsForDate = this.appointmentService.getAppointmentsByDate(new Date(date.fullDate));
      if (appointmentsForDate.length > 0) {
        console.log(`Found ${appointmentsForDate.length} appointments for ${date.fullDate}`);
        this.appointmentsMap[date.fullDate] = appointmentsForDate.sort((a, b) => {
          const timeA = this.parseTime(a.appointmentTime).getTime();
          const timeB = this.parseTime(b.appointmentTime).getTime();
          return timeA - timeB;
        });
      }
    });
  }

  // Add a method to select the current date or date with appointment
  private selectCurrentDate(): void {
    if (this.currentAppointment) {
      const appointmentDate = this.currentAppointment.appointmentDate;
      this.selectedDate = this.dates.find((date) =>
        new Date(date.fullDate).getDate() === new Date(appointmentDate).getDate()
      ) || null;
    } else {
      this.selectedDate = this.dates.find((date) =>
        new Date(date.fullDate).getDate() === new Date().getDate()
      ) || null;
    }
  }

  getGroupedAppointments(date: string): { time: string; appointments: AppointmentPatient[] }[] {
    const appointments = this.appointmentsMap[date] || [];
    const groupedAppointments = new Map<string, AppointmentPatient[]>();

    appointments.forEach((appointment) => {
      const existingGroup = groupedAppointments.get(appointment.appointmentTime) || [];
      groupedAppointments.set(appointment.appointmentTime, [...existingGroup, appointment]);
    });

    return Array.from(groupedAppointments.entries())
      .map(([time, apps]) => ({
        time,
        appointments: apps,
      }))
      .sort((a, b) => {
        const timeA = this.parseTime(a.time).getTime();
        const timeB = this.parseTime(b.time).getTime();
        return timeA - timeB;
      });
  }

  selectDate(date: DashboardDate): void {
    this.selectedDate = date;
  }

  hasAppointmentsForDate(dateString: string): boolean {
    return !!(this.appointmentsMap[dateString] && this.appointmentsMap[dateString].length > 0);
  }

  private parseTime(timeString: string): Date {
    const time = new Date();

    // Handle time strings with AM/PM
    if (timeString.includes('AM') || timeString.includes('PM')) {
      const [timePart, period] = timeString.split(' ');
      let [hours, minutes] = timePart.split(':').map(Number);

      if (period === 'PM' && hours < 12) {
        hours += 12;
      } else if (period === 'AM' && hours === 12) {
        hours = 0;
      }

      time.setHours(hours, minutes, 0, 0);
      return time;
    }

    // Handle 24-hour format
    const [hours, minutes] = timeString.split(':').map(Number);
    time.setHours(hours, minutes, 0, 0);
    return time;
  }

  hasAppointmentAt(time: string): boolean {
    if (!this.selectedDate) return false;
    return this.appointmentsMap[this.selectedDate.fullDate]?.some(
      (appointment) => appointment.appointmentTime === time
    ) || false;
  }

  getAppointmentAt(time: string): AppointmentPatient | null {
    if (!this.selectedDate) return null;
    return this.appointmentsMap[this.selectedDate.fullDate]?.find(
      (appointment) => appointment.appointmentTime === time
    ) || null;
  }

  formatTime(time: string): string {
    if (!time) return '';

    // Check if time is already in 12-hour format with AM/PM
    if (time.includes('AM') || time.includes('PM')) {
      return time;
    }

    // Convert 24-hour format to 12-hour format
    const [hours, minutes] = time.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHour = hours % 12 || 12;
    return `${displayHour}:${minutes.toString().padStart(2, '0')} ${period}`;
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Pending': return '#eab308';
      case 'Approved': return '#22c55e';
      case 'Rejected': return '#ef4444';
      case 'Completed': return '#199a8e';
      case 'Cancelled': return '#6b7280';
      default: return '#6b7280';
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'Approved':
        return 'status-approved';
      case 'Pending':
        return 'status-pending';
      case 'Completed':
        return 'status-completed';
      case 'Cancelled':
      case 'Rejected':
        return 'status-cancelled';
      case 'Available':
        return 'status-available';
      default:
        return '';
    }
  }

  getMonthShort(date: Date | string): string {
    const d = new Date(date);
    return d.toLocaleString('default', { month: 'short' });
  }

  getDayOfMonth(date: Date | string): number {
    const d = new Date(date);
    return d.getDate();
  }
}
