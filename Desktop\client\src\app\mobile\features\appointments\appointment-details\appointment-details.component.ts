import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { AppointmentPatient, AppointmentService } from '../service/appointment.service';
import { BackButtonComponent } from "../../shared/back-button/back-button.component";
import { RouterLink } from '@angular/router';
import { NavbarComponent } from '../../shared/navbar/navbar.component';

@Component({
  selector: 'app-appointment-details',
  standalone: true,
  imports: [CommonModule, BackButtonComponent, RouterLink, NavbarComponent],
  templateUrl: './appointment-details.component.html',
  styleUrls: ['./appointment-details.component.css']
})
export class AppointmentDetails implements OnInit {
  appointment: AppointmentPatient | null = null;
  doctorImage = 'https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Mask%20group-ADQ5qIcUBxrq7579C7xyRng7wHXK2p.png';
  isLoading = false;
  error: string | null = null;

  constructor(
    private appointmentService: AppointmentService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.loadAppointment();
  }

  public loadAppointment() {
    // First check if appointment was passed via router state
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras.state as { appointment?: AppointmentPatient };

    if (state?.appointment) {
      console.log('Appointment from navigation state:', state.appointment);
      this.appointment = state.appointment;
      // Also set as current in the service so we can access it later if needed
      this.appointmentService.setCurrentAppointment(this.appointment);
      return;
    }

    // If not from navigation, try to get from service
    this.appointment = this.appointmentService.getCurrentAppointment();

    if (!this.appointment) {
      console.log('No appointment found in state or service, redirecting to booking');
      this.router.navigate(['/mobile/appointment-booking']);
    } else {
      console.log('Loaded appointment from service:', this.appointment);
    }
  }

  formatDate(date: Date | string): string {
    if (!date) return 'No date available';

    try {
      const dateObj = date instanceof Date ? date : new Date(date);
      return dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  formatTime(time: string): string {
    if (!time) return 'No time available';

    try {
      // Check if time is already in 12-hour format with AM/PM
      if (time.includes('AM') || time.includes('PM')) {
        return time;
      }

      // Convert 24-hour format to 12-hour format
      const [hours, minutes] = time.split(':').map(Number);
      const period = hours >= 12 ? 'PM' : 'AM';
      const displayHour = hours % 12 || 12;
      return `${displayHour}:${minutes.toString().padStart(2, '0')} ${period}`;
    } catch (error) {
      console.error('Error formatting time:', error);
      return time; // Return original if parsing fails
    }
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Pending': return '#eab308'; // Yellow
      case 'Approved': return '#22c55e'; // Green
      case 'Rejected':
      case 'Cancelled': return '#ef4444'; // Red
      case 'Completed': return '#3b82f6'; // Blue
      default: return '#6b7280'; // Gray
    }
  }

  cancelAppointment() {
    if (!this.appointment) {
      this.error = 'Cannot cancel: No appointment details available';
      return;
    }

    if (this.appointment.status !== 'Pending' && this.appointment.status !== 'Approved') {
      this.error = `Cannot cancel appointment with status: ${this.appointment.status}`;
      return;
    }

    this.isLoading = true;
    this.error = null;

    console.log(`Cancelling appointment with ID: ${this.appointment.appointmentId}`);

    this.appointmentService.cancelAppointment(
      this.appointment.appointmentId,
      'Cancelled by patient'
    ).subscribe({
      next: () => {
        console.log('Appointment cancelled successfully');
        this.isLoading = false;
        // Update the local appointment status
        if (this.appointment) {
          this.appointment.status = 'Cancelled';
        }
        // Navigate back to dashboard after a short delay to show the cancelled status
        setTimeout(() => {
          this.router.navigate(['/mobile/patient-dashboard']);
        }, 1500);
      },
      error: (error) => {
        console.error('Error cancelling appointment:', error);
        this.error = 'Failed to cancel appointment. Please try again.';
        this.isLoading = false;
      }
    });
  }
}
