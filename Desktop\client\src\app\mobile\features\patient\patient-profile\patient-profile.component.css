.profile-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  min-height: 100vh;
  padding: clamp(15px, 4vw, 30px);
  box-sizing: border-box;
  font-family: 'Segoe UI', Arial, sans-serif;
} 

header {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;
  width: 100%;
  justify-content: center;
}

h1 {
  flex-grow: 1;
  text-align: center;
  font-size: clamp(16px, 4vw, 20px);
  margin: 0;
  font-weight: 600;
  color: #333;
  justify-content: center;
  margin-top: 30px;
}

.profile-image-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  margin-top: 30px;
}

.image-preview {
  width: clamp(120px, 30vw, 150px);
  height: clamp(120px, 30vw, 150px);
  border-radius: 50%;
  border: 2px dashed #199A8E;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.image-preview.has-image {
  border: 2px solid #199A8E;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #E6F7F5;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
  font-size: clamp(12px, 3vw, 14px);
}

.upload-placeholder i {
  font-size: clamp(24px, 6vw, 32px);
}

.edit-icon {
  position: absolute;
  bottom: 5px;
  right: calc(50% - clamp(50px, 12vw, 65px));
  background: #199A8E;
  color: white;
  border: none;
  border-radius: 50%;
  width: clamp(28px, 7vw, 35px);
  height: clamp(28px, 7vw, 35px);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s;
}

.edit-icon:hover {
  transform: scale(1.1);
}

#file-upload {
  display: none;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(25, 154, 142, 0.3);
  border-radius: 50%;
  border-top-color: #199A8E;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: clamp(10px, 2.5vh, 15px);
  margin-top: clamp(15px, 4vh, 30px);
  padding: 0 clamp(5px, 1.5vw, 10px);
}

.menu-item {
  display: flex;
  align-items: center;
  background: white;
  border: none;
  padding: clamp(12px, 3vh, 15px);
  cursor: pointer;
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s;
}

.menu-item:hover {
  transform: translateX(5px);
}

.menu-icon {
  background-color: #199A8E;
  color: white;
  width: clamp(32px, 8vw, 40px);
  height: clamp(32px, 8vw, 40px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: clamp(10px, 2.5vw, 15px);
}

.menu-icon i {
  font-size: clamp(14px, 3.5vw, 18px);
}

.menu-item span {
  flex-grow: 1;
  text-align: left;
  font-size: clamp(14px, 3.5vw, 16px);
  color: #333;
  font-weight: 500;
}

.chevron {
  color: #199A8E;
  font-size: clamp(12px, 3vw, 14px);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  z-index: 1000;
}

.modal {
  background: white;
  width: 100%;
  max-width: 500px;
  padding: clamp(20px, 5vw, 30px);
  border-radius: clamp(20px, 5vw, 27px) clamp(20px, 5vw, 27px) 0 0;
  text-align: center;
  animation: slideUp 0.3s ease;
  margin-bottom: 80px;
}

.modal h2 {
  font-size: clamp(16px, 4vw, 20px);
  margin-bottom: clamp(8px, 2vh, 10px);
  color: #333;
}

.modal p {
  color: #666;
  margin-bottom: clamp(20px, 5vh, 25px);
  font-size: clamp(14px, 3.5vw, 16px);
}

.modal-buttons {
  display: flex;
  gap: clamp(10px, 2.5vw, 15px);
  justify-content: center;
  flex-wrap: wrap;
}

.cancel-button,
.logout-button {
  padding: clamp(10px, 2.5vh, 12px) clamp(20px, 5vw, 30px);
  border-radius: 25px;
  font-size: clamp(14px, 3.5vw, 16px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: clamp(100px, 25vw, 120px);
  font-weight: 500;
}

.cancel-button {
  border: 1px solid #199A8E;
  background: white;
  color: #199A8E;
}

.cancel-button:hover {
  background: #f0f9f8;
}

.logout-button {
  border: 1px solid #199A8E;
  background: #199A8E;
  color: white;
}

.logout-button:hover {
  background: #148277;
}

.bottom-nav {
  position: fixed;
  bottom: clamp(15px, 4vh, 20px);
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;
  padding: clamp(8px, 2vh, 10px);
  background-color: white;
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: clamp(6px, 1.5vw, 8px);
  background: none;
  border: none;
  color: #c0c0c0;
  font-size: clamp(10px, 2.5vw, 12px);
  cursor: pointer;
  padding: clamp(6px, 1.5vh, 8px) clamp(12px, 3vw, 16px);
  border-radius: 20px;
}

.nav-button i {
  font-size: clamp(16px, 4vw, 20px);
}

.nav-button.active {
  color: #00A693;
  background-color: #e6f7f5;
}

.nav-label {
  font-size: clamp(12px, 3vw, 14px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@media (min-width: 768px) {
  .profile-container {
    padding: 30px;
    margin-top: 20px;
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .menu-items {
    max-width: 600px;
    margin: 30px auto;
  }

  .modal {
    margin-bottom: 100px;
  }
}

@media (max-height: 600px) {
  .profile-image-container {
    margin-bottom: 15px;
  }

  .menu-items {
    gap: 8px;
  }

  .bottom-nav {
    padding: 6px;
  }
}
