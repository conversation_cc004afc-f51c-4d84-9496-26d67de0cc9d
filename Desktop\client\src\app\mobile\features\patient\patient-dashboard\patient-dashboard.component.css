.dashboard-container {
  padding: 20px;
  font-family: "Raleway", sans-serif;
  background-color: #ffffff;
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  flex-direction: row-reverse;
}

.welcome-text {
  font-weight: 150;
  font-size: 20px;
  color: black;
  margin: 0;
  margin-bottom: 8px;
}

.patient-name {
  font-weight: 400;
  font-size: 14px;
  color: #40b3a2;
  margin: 0;
}

.profile-pic-container {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: rgb(239, 235, 235);
}

.profile-pic {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-pic-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(239, 235, 235);
  border-radius: 50%;
  color: #40b3a2;
}

.profile-pic-placeholder i {
  font-size: 24px;
}

.slideshow-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 15px;
  margin: 20px 0;
}

.slides {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  background: #40b3a2;
  padding: 24px;
  color: white;
  height: 180px;
  border-radius: 15px;
}

.slide-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  position: relative;
}

.slide-info {
  max-width: 70%;
  z-index: 2;
}

.slide h5 {
  font-size: 13px;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.slide-text {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 15px;
  line-height: 1.4;
  max-width: 90%;
}

.slide-button {
  background: white;
  color: #40b3a2;
  border: none;
  border-radius: 25px;
  padding: 8px 20px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.slide-button:hover {
  background: rgba(255, 255, 255, 0.9);
}

.slide-image {
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  height: 180px;
  object-fit: contain;
  z-index: 1;
}

.schedule-section {
  background-color: rgba(64, 179, 162, 0.05);
  border-radius: 20px;
  padding: 20px;
  margin-top: 20px;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.schedule-title {
  color: #40b3a2;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.schedule-month {
  color: #666;
  font-size: 14px;
}

 .dates-wrapper {
  background-color: white;
  border-radius: 15px;
  padding: 10px;
  margin-bottom: 15px;
}

.dates-scrollable {
  display: flex;
  gap: 15px;
  padding: 5px 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dates-scrollable::-webkit-scrollbar {
  display: none;
}

.date-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  min-width: 45px;
  cursor: pointer;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.date-item .date-day {
  font-size: 16px;
  font-weight: 500;
}

.date-item .date-weekday {
  font-size: 12px;
  color: #666;
}

.date-item.active {
  background: #40b3a2;
  color: white;
}

.date-item.active .date-weekday {
  color: rgba(255, 255, 255, 0.8);
}

.date-item.has-appointment::after {
  content: "";
  display: block;
  width: 4px;
  height: 4px;
  background: #40b3a2;
  border-radius: 50%;
  margin-top: 4px;
}

.schedule-grid {
  background-color: white;
  border-radius: 15px;
  padding: 15px;
}

.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.time-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-header {
  font-size: 16px;
  font-weight: 500;
  color: #40b3a2;
  padding: 0 4px;
}

.appointments-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.appointment-card {
  background: rgba(64, 179, 162, 0.1);
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.doctor-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.appointment-status {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.appointment-status.confirmed {
  background: rgba(52, 211, 153, 0.2);
  color: rgb(52, 211, 153);
}

.appointment-status.pending {
  background: rgba(251, 191, 36, 0.2);
  color: rgb(251, 191, 36);
}

.appointment-status.cancelled {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
}

.no-appointments {
  color: #28b2a7;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

/* All Booked Appointments Section */
.all-appointments-section {
  background-color: rgba(64, 179, 162, 0.05);
  border-radius: 20px;
  padding: 20px;
  margin-top: 30px;
  margin-bottom: 70px; /* Space for bottom navbar */
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  color: #30b3a2;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
}

.view-all-btn {
  background: rgba(64, 179, 162, 0.2);
  border: none;
  color: #40b3a2;
  border-radius: 20px;
  padding: 7px 14px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-btn:hover {
  background: rgba(64, 179, 162, 0.3);
}

.appointments-container {
  background-color: white;
  border-radius: 15px;
  padding: 5px;
}

.appointment-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.booked-appointment-card {
  position: relative;
  border-radius: 16px;
  background: white;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease;
}

.booked-appointment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  border-color: rgba(64, 179, 162, 0.3);
}

.appointment-date-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f9fafb;
  border-radius: 12px;
  min-width: 60px;
  min-height: 60px;
  margin-right: 16px;
  padding: 8px;
}

.month {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
}

.day {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
}

.appointment-details {
  flex: 1;
  min-width: 0;
}

.appointment-details .doctor-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 5px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.appointment-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 4px;
}

.appointment-info .time,
.appointment-info .specialization,
.appointment-info .reason {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.reason {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.appointment-info i {
  font-size: 12px;
  color: #40b3a2;
}

.appointment-status-container {
  display: flex;
  align-items: center;
}

.appointment-status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
  text-align: center;
  min-width: 80px;
}

/* Status colors */
.status-pending {
  background-color: #fef3c7;
  color: #d97706;
}

.status-approved {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-completed {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-cancelled, .status-rejected {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-available {
  background-color: #f3f4f6;
  color: #4b5563;
}

.no-appointments-container {
  padding: 30px 20px;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
}

.empty-state p {
  color: #888;
  margin: 0;
}

.book-now-btn {
  margin-top: 15px;
  background: #40b3a2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.book-now-btn:hover {
  background: #389e8f;
}

/* Status classes for the calendar view */
.appointment-status.pending {
  background: rgba(251, 191, 36, 0.2);
  color: rgb(251, 191, 36);
}

.appointment-status.approved {
  background: rgba(52, 211, 153, 0.2);
  color: rgb(52, 211, 153);
}

.appointment-status.completed {
  background: rgba(25, 154, 142, 0.2);
  color: rgb(25, 154, 142);
}

.appointment-status.cancelled {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
}

.appointment-status.rejected {
  background: rgba(239, 68, 68, 0.2);
  color: rgb(239, 68, 68);
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .booked-appointment-card {
    padding: 10px;
    gap: 10px;
  }

  .appointment-date-badge {
    min-width: 40px;
    height: 40px;
  }

  .appointment-info {
    flex-direction: column;
    gap: 2px;
  }
}

.appointment-count {
  font-size: 14px;
  color: #666;
  background-color: rgba(64, 179, 162, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
}
