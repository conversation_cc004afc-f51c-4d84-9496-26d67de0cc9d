.medical-records-container {
  padding: 16px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

header {
  margin-bottom: 24px;
}

h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin-top: 16px;
  margin-bottom: 0;
}

.search-filters {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  background-color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-filters span {
  color: #666;
  margin-right: 12px;
  font-size: 0.9rem;
}

.filter-buttons {
  display: flex;
  gap: 8px;
}

.filter-buttons button {
  background-color: #f0f0f0;
  border: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-buttons button.active {
  background-color: #4a6cf7;
  color: white;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.record-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.record-content {
  display: flex;
  padding: 16px;
  position: relative;
}

.record-bullet {
  width: 12px;
  height: 12px;
  background-color: #4a6cf7;
  border-radius: 50%;
  margin-right: 12px;
  margin-top: 6px;
  flex-shrink: 0;
}

.record-details {
  flex: 1;
}

.record-details h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

.record-info {
  margin-bottom: 12px;
}

.info-item {
  margin-bottom: 8px;
}

.info-item strong {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 2px;
}

.info-item p {
  margin: 0;
  color: #333;
  font-size: 0.95rem;
  line-height: 1.4;
}

.record-date {
  display: block;
  font-size: 0.8rem;
  color: #888;
  margin-top: 8px;
}

.download-button {
  background: none;
  border: none;
  color: #4a6cf7;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 12px;
  right: 12px;
  transition: background-color 0.2s ease;
}

.download-button:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.no-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 24px;
}

.no-records svg {
  margin-bottom: 16px;
  color: #14B8A6;
}

.no-records p {
  margin: 0;
  color: #333;
  font-weight: 500;
  font-size: 1.1rem;
}

.no-records .sub-text {
  margin-top: 8px;
  color: #666;
  font-size: 0.9rem;
  font-weight: normal;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 24px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4a6cf7;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 16px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-top: 24px;
}

.error-state svg {
  margin-bottom: 16px;
  color: #e53935;
}

.error-state p {
  margin: 0 0 16px 0;
  color: #333;
  font-weight: 500;
  font-size: 1rem;
}

.retry-button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #3a5ce5;
}

.bottom-nav {
  position: fixed;
  bottom: clamp(15px, 4vh, 20px);
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-around;
  padding: clamp(8px, 2vh, 10px) clamp(15px, 4vw, 20px);
  background-color: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: clamp(6px, 1.5vw, 8px);
  background: none;
  border: none;
  color: #c0c0c0;
  font-size: clamp(10px, 2.5vw, 12px);
  cursor: pointer;
  padding: clamp(6px, 1.5vh, 8px) clamp(12px, 3vw, 16px);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.nav-button i {
  font-size: clamp(16px, 4vw, 20px);
}

.nav-button.active {
  color: #00A693;
  background-color: #e6f7f5;
}

.nav-label {
  font-size: clamp(12px, 3vw, 14px);
}

@media (min-width: 768px) {
  .medical-records-container {
    padding: 30px;
    margin-top: 20px;
  }

  .records-list {
    max-width: 700px;
    margin: 0 auto;
  }

  .record-card {
    padding: 20px;
  }

  .filter-buttons {
    justify-content: flex-start;
  }
}

@media (max-width: 360px) {
  .search-filters {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .no-records svg {
    margin-bottom: 8px;
  }
  
  .no-records p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  .no-records .sub-text {
    font-size: 14px;
    color: #999;
  }
  
}

@media (max-width: 480px) {
  .medical-records-container {
    padding: 16px;
  }
  
  .header {
    margin-bottom: 20px;
  }
  
  h1 {
    font-size: 20px;
  }
}
