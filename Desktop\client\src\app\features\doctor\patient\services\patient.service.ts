import { Injectable, NgZone, inject } from '@angular/core';
import { Observable, of, throwError, from, fork<PERSON>oin } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { map, catchError, switchMap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  collectionData,
  Timestamp,
  setDoc
} from '@angular/fire/firestore';
import { Storage } from '@angular/fire/storage';
import { AuthService, User } from '../../../../core/services/auth.service';
import { Db } from '../../../../db';
import { DoctorPatient } from '../../../../type';

export interface Patient extends User {
  patientId?: string;
  dateOfBirth?: string;
  gender?: string;
  contactNumber?: string;
  address?: string;
  bloodType?: string;
  allergies?: string[];
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  lastVisit?: Date;
  status?: string;
  age?: number;
  hasMedicalRecord?: boolean;
  assignedDoctors?: string[]; // Array of doctor <PERSON><PERSON>s
}

export interface MedicalRecord {
  id?: string;
  patientId: string;
  doctorId: string;
  date: Date;
  diagnosis: string;
  treatment: string;
  prescription?: string;
  notes: string;
  nextVisit?: Date;
  attachments?: string[]; // URLs to files in Firebase Storage
}

export interface DoctorPatientRelation {
  doctorId: string;
  patientId: string;
  assignedDate: Date;
}

@Injectable({
  providedIn: 'root'
})
export class PatientService {
  private medicalRecordsCollection = 'medicalRecords';
  private doctorPatientCollection = 'doctorPatient';

  constructor(
    private http: HttpClient,
    private firestore: Firestore,
    private storage: Storage,
    private authService: AuthService,
    private ngZone: NgZone,
    private db: Db
  ) {}

  getAllPatients(): Observable<Patient[]> {
    return this.ngZone.runOutsideAngular(() => {
      const usersRef = collection(this.firestore, 'users');
      const patientsQuery = query(
        usersRef,
        where('role', '==', 'patient'),
        orderBy('lastName')
      );

      return collectionData(patientsQuery, { idField: 'uid' }).pipe(
        map(patients => {
          return this.ngZone.run(() => patients as Patient[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching patients:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getPatient(uid: string): Observable<Patient | null> {
    return this.ngZone.runOutsideAngular(() => {
      const userDocRef = doc(this.firestore, `users/${uid}`);
      return from(getDoc(userDocRef)).pipe(
        map(docSnap => {
          return this.ngZone.run(() => {
            if (docSnap.exists() && docSnap.data()['role'] === 'patient') {
              return { uid, ...docSnap.data() } as Patient;
            } else {
              return null;
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching patient:', error);
            return of(null);
          });
        })
      );
    });
  }

  removePatient(patientId: string, doctorId: string): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Removing doctor-patient relationship: Doctor ID ${doctorId}, Patient ID ${patientId}`);

      // This only removes the relationship between doctor and patient, not the patient account
      // First try to find the specific relationship document with our naming convention
      const relationshipId = `${doctorId}_${patientId}`;
      const docRef = doc(this.firestore, `${this.doctorPatientCollection}/${relationshipId}`);

      // Try to delete the document directly first
      return from(getDoc(docRef)).pipe(
        switchMap(docSnapshot => {
          return this.ngZone.run(() => {
            if (docSnapshot.exists()) {
              console.log(`Found doctor-patient relationship document with ID: ${relationshipId}`);
              // Document exists, delete it
              return this.ngZone.runOutsideAngular(() => {
                return from(deleteDoc(docRef)).pipe(
                  map(() => {
                    console.log(`Successfully removed doctor-patient relationship with ID: ${relationshipId}`);
                    return void 0;
                  })
                );
              });
            } else {
              console.log(`Relationship document not found with ID: ${relationshipId}, trying with query`);
              // If not found by ID, fall back to query method
              const relationQuery = query(
                collection(this.firestore, this.doctorPatientCollection),
                where('doctorId', '==', doctorId),
                where('patientId', '==', patientId)
              );

              return this.ngZone.runOutsideAngular(() => {
                return from(getDocs(relationQuery)).pipe(
                  switchMap(querySnapshot => {
                    return this.ngZone.run(() => {
                      if (querySnapshot.empty) {
                        console.log('No relationship found to remove');
                        return throwError(() => new Error('No relationship found'));
                      }

                      console.log(`Found ${querySnapshot.size} relationships to delete`);
                      const deletePromises = querySnapshot.docs.map(doc =>
                        this.ngZone.runOutsideAngular(() => deleteDoc(doc.ref))
                      );

                      return from(Promise.all(deletePromises)).pipe(
                        map(() => {
                          console.log('Successfully removed all doctor-patient relationships');
                          return void 0;
                        })
                      );
                    });
                  })
                );
              });
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error removing patient from doctor:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  findPatientByEmail(email: string): Observable<Patient | null> {
    console.log('Finding patient by email in Firebase:', email);

    // Use Angular's injector inside component context
    return new Observable<Patient | null>(observer => {
      // Make email search case-insensitive by lowercasing
      const emailLowerCase = email.toLowerCase();

      // First try with exact role='patient'
      this.searchPatientWithRole(emailLowerCase, 'patient')
        .then(patient => {
          if (patient) {
            console.log('Patient found with role=patient:', patient);
            observer.next(patient);
            observer.complete();
            return;
          }

          // If not found, try with role='PATIENT' (uppercase)
          return this.searchPatientWithRole(emailLowerCase, 'PATIENT');
        })
        .then(patient => {
          if (patient) {
            console.log('Patient found with role=PATIENT:', patient);
            observer.next(patient);
            observer.complete();
            return;
          }

          // As a last resort, search without role filter
          return this.searchPatientWithoutRoleFilter(emailLowerCase);
        })
        .then(patient => {
          if (patient) {
            console.log('Patient found without role filter:', patient);
            observer.next(patient);
            observer.complete();
            return;
          }

          // Finally, if still not found
          console.log('No patient found with email after all attempts:', email);
          observer.next(null);
          observer.complete();
        })
        .catch(error => {
          console.error('Error searching for patient:', error);
          observer.error(error);
        });
    });
  }

  // Helper method to search for a patient with a specific role
  private searchPatientWithRole(email: string, role: string): Promise<Patient | null> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Searching for patient with email=${email} and role=${role}`);
      const usersRef = collection(this.firestore, 'users');

      // Create a query looking for the email and role
      const patientQuery = query(
        usersRef,
        where('email', '==', email),
        where('role', '==', role)
      );

      return getDocs(patientQuery)
        .then(querySnapshot => {
          return this.ngZone.run(() => {
            if (querySnapshot.empty) {
              console.log(`No patient found with email=${email} and role=${role}`);
              return null;
            }

            const doc = querySnapshot.docs[0];
            console.log(`Found patient with id=${doc.id} for email=${email} and role=${role}`);
            return { uid: doc.id, ...doc.data() } as Patient;
          });
        });
    });
  }

  // Helper method to search for a patient without role filter
  private searchPatientWithoutRoleFilter(email: string): Promise<Patient | null> {
    return this.ngZone.runOutsideAngular(() => {
      console.log(`Searching for patient with email=${email} without role filter`);
      const usersRef = collection(this.firestore, 'users');

      // Create a query looking for the email only
      const patientQuery = query(
        usersRef,
        where('email', '==', email)
      );

      return getDocs(patientQuery)
        .then(querySnapshot => {
          return this.ngZone.run(() => {
            if (querySnapshot.empty) {
              console.log(`No user found with email=${email}, trying with original case email`);

              // Try with original case email (in case the database stores emails with different casing)
              return this.searchWithOriginalCaseEmail(email);
            }

            // Find documents that might be patients based on role field or other patient-like attributes
            const potentialPatients = querySnapshot.docs.filter(doc => {
              const data = doc.data();
              return data['role'] === 'patient' ||
                     data['role'] === 'PATIENT' ||
                     data['patientId'] ||
                     data.hasOwnProperty('medicalRecords');
            });

            if (potentialPatients.length === 0) {
              // If no obvious patients, just take the first document
              const doc = querySnapshot.docs[0];
              console.log(`No obvious patient found, using first user with id=${doc.id}`);
              return { uid: doc.id, ...doc.data() } as Patient;
            }

            const doc = potentialPatients[0];
            console.log(`Found potential patient with id=${doc.id} for email=${email}`);
            return { uid: doc.id, ...doc.data() } as Patient;
          });
        });
    });
  }

  // Search for users using the original case email as provided (not lowercased)
  private searchWithOriginalCaseEmail(lowercaseEmail: string): Promise<Patient | null> {
    return this.ngZone.runOutsideAngular(() => {
      // Get all users
      const usersRef = collection(this.firestore, 'users');
      return getDocs(usersRef)
        .then(querySnapshot => {
          return this.ngZone.run(() => {
            if (querySnapshot.empty) {
              console.log('No users found in database');
              return null;
            }

            // Manually search through all users to find matching email with any case
            for (const doc of querySnapshot.docs) {
              const data = doc.data();
              // Check email field and common variations
              const userEmail = data['email'] || data['emailAddress'] || data['userEmail'] || '';

              // Case-insensitive comparison
              if (userEmail.toLowerCase() === lowercaseEmail) {
                console.log(`Found user with case-insensitive email match: ${userEmail}`);
                return { uid: doc.id, ...data } as Patient;
              }
            }

            console.log('No users found with matching email after case-insensitive search');
            return null;
          });
        });
    });
  }

  addPatientToDoctor(patientId: string, doctorId: string): Observable<void> {
    console.log(`Creating doctor-patient relationship: Doctor ID ${doctorId}, Patient ID ${patientId}`);

    return this.ngZone.run(() => {
      // Check if relationship already exists
      const relationQuery = query(
        collection(this.firestore, this.doctorPatientCollection),
        where('doctorId', '==', doctorId),
        where('patientId', '==', patientId)
      );

      return from(getDocs(relationQuery)).pipe(
        switchMap(snapshot => {
          if (!snapshot.empty) {
            console.log('Relationship already exists in Firebase');
            // Return success instead of error to ensure UI flow continues
            return of(void 0);
          }

          // Create new relationship document
          const relationshipId = `${doctorId}_${patientId}`;
          const relationshipRef = doc(this.firestore, this.doctorPatientCollection, relationshipId);

          return from(setDoc(relationshipRef, {
            doctorId: doctorId,
            patientId: patientId,
            assignedDate: new Date().toISOString()
          })).pipe(
            map(() => {
              console.log('Successfully created doctor-patient relationship in Firebase');
              return void 0;
            })
          );
        }),
        catchError(error => {
          console.error('Error creating doctor-patient relationship:', error);
          return throwError(() => error);
        })
      );
    });
  }

  getPatientCount(doctorId: string): Observable<number> {
    return this.ngZone.runOutsideAngular(() => {
      const relationQuery = query(
        collection(this.firestore, this.doctorPatientCollection),
        where('doctorId', '==', doctorId)
      );

      return from(getDocs(relationQuery)).pipe(
        map(querySnapshot => {
          return this.ngZone.run(() => querySnapshot.size);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error getting patient count:', error);
            return of(0);
          });
        })
      );
    });
  }

  getDoctorPatients(doctorId: string): Observable<Patient[]> {
    console.log('Fetching patients for doctor:', doctorId);

    return this.ngZone.run(() => {
      // Check all possible variations of the doctor-patient relationship
      return this.checkAllDoctorPatientFormats(doctorId);
    });
  }

  private checkAllDoctorPatientFormats(doctorId: string): Observable<Patient[]> {
    // Try all possible formats of doctor-patient relationships

    // 1. Try with doctorId field
    const relationQuery1 = query(
        collection(this.firestore, this.doctorPatientCollection),
        where('doctorId', '==', doctorId)
      );

    // 2. Try with doctor_id field
    const relationQuery2 = query(
      collection(this.firestore, this.doctorPatientCollection),
      where('doctor_id', '==', doctorId)
    );

    // First try the standard format
    return from(getDocs(relationQuery1)).pipe(
      switchMap(snapshot1 => {
        const patients1 = this.extractPatientIds(snapshot1);
        if (patients1.length > 0) {
          console.log('Found patients with doctorId field format:', patients1);
          return this.fetchPatientsByIds(patients1);
        }

        // If no results, try the alternative format
        return from(getDocs(relationQuery2)).pipe(
          switchMap(snapshot2 => {
            const patients2 = this.extractPatientIds(snapshot2, true);
            if (patients2.length > 0) {
              console.log('Found patients with doctor_id field format:', patients2);
              return this.fetchPatientsByIds(patients2);
            }

            // If still no results, check for any other collection with this pattern
            console.log('No patients found with any field format. Trying alternate collection names...');

            // Try 'doctorPatients' collection (plural form)
            const relationQuery3 = query(
              collection(this.firestore, 'doctorPatients'),
              where('doctorId', '==', doctorId)
            );

            return from(getDocs(relationQuery3)).pipe(
              switchMap(snapshot3 => {
                const patients3 = this.extractPatientIds(snapshot3);
                if (patients3.length > 0) {
                  console.log('Found patients in doctorPatients collection:', patients3);
                  return this.fetchPatientsByIds(patients3);
                }

                console.log('No patients found in any collection format. Checking both ID formats together...');

                // As a last resort, load all doctor-patient relationships and filter manually
                return this.checkAllDoctorPatientRelationships(doctorId);
              })
            );
          })
        );
      }),
      catchError(error => {
        console.error('Error fetching doctor patients:', error);
        return of([]);
      })
    );
  }

  private extractPatientIds(snapshot: any, useUnderscoreFormat = false): string[] {
    if (snapshot.empty) {
      return [];
    }

    const patientIds = snapshot.docs.map((doc: any) => {
      const data = doc.data();
      return useUnderscoreFormat
        ? (data['patient_id'] || data['patientId'])
        : (data['patientId'] || data['patient_id']);
    }).filter((id: string | undefined | null) => id !== undefined && id !== null);

    console.log('Extracted patient IDs:', patientIds);
    return patientIds;
  }

  private checkAllDoctorPatientRelationships(doctorId: string): Observable<Patient[]> {
    // Get all doctor-patient relationships
    return from(getDocs(collection(this.firestore, this.doctorPatientCollection))).pipe(
        switchMap(snapshot => {
          if (snapshot.empty) {
          console.log('No doctor-patient relationships found at all');
            return of([]);
          }

        // Manually filter to find relationships matching this doctor
        const relationships = snapshot.docs.map(doc => doc.data());
        console.log('All doctor-patient relationships:', relationships);

        // Look for any field that might contain the doctor ID
        const patientIds = relationships
          .filter(rel =>
            rel['doctorId'] === doctorId ||
            rel['doctor_id'] === doctorId ||
            (rel['doctor'] && rel['doctor'].id === doctorId) ||
            rel['doctor'] === doctorId
          )
          .map(rel =>
            rel['patientId'] ||
            rel['patient_id'] ||
            (rel['patient'] && rel['patient'].id) ||
            rel['patient']
          )
          .filter(id => id !== undefined && id !== null);

        if (patientIds.length > 0) {
          console.log('Found patient IDs through relationship inspection:', patientIds);
          return this.fetchPatientsByIds(patientIds);
        }

        console.log('Could not find any patients linked to this doctor after checking all formats');
        return of([]);
      }),
      catchError(error => {
        console.error('Error checking all doctor-patient relationships:', error);
        return of([]);
      })
    );
  }

  private fetchPatientsByIds(patientIds: string[]): Observable<Patient[]> {
    return this.ngZone.run(() => {
          // Create an array of promises to get each patient's data
          const patientPromises = patientIds.map(patientId => {
            const userDocRef = doc(this.firestore, 'users', patientId);
            return getDoc(userDocRef);
          });

          // Wait for all patient data to be fetched
          return from(Promise.all(patientPromises)).pipe(
            map(patientDocs => {
              const patients = patientDocs
                .filter(doc => doc.exists())
                .map(doc => ({
                  uid: doc.id,
                  ...doc.data()
                } as Patient));

              console.log('Fetched patients from Firebase:', patients);
              return patients;
        })
      );
    });
  }

  getMedicalRecords(patientId: string): Observable<MedicalRecord[]> {
    return this.ngZone.runOutsideAngular(() => {
      const medicalRecordsRef = collection(this.firestore, this.medicalRecordsCollection);
      const recordsQuery = query(
        medicalRecordsRef,
        where('patientId', '==', patientId),
        orderBy('date', 'desc')
      );

      return collectionData(recordsQuery, { idField: 'id' }).pipe(
        map(records => {
          return this.ngZone.run(() => records.map(record => ({
            ...record,
            date: (record['date'] as Timestamp).toDate(),
            nextVisit: record['nextVisit'] ? (record['nextVisit'] as Timestamp).toDate() : undefined
          })) as MedicalRecord[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching medical records:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getMedicalRecord(recordId: string): Observable<MedicalRecord> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, `${this.medicalRecordsCollection}/${recordId}`);
      return from(getDoc(recordDocRef)).pipe(
        map(docSnap => {
          return this.ngZone.run(() => {
            if (docSnap.exists()) {
              const data = docSnap.data();
              return {
                id: docSnap.id,
                ...data,
                date: (data['date'] as Timestamp).toDate(),
                nextVisit: data['nextVisit'] ? (data['nextVisit'] as Timestamp).toDate() : undefined
              } as MedicalRecord;
            } else {
              throw new Error('Medical record not found');
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  addMedicalRecord(record: Omit<MedicalRecord, 'id'>): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      const medicalRecordsRef = collection(this.firestore, this.medicalRecordsCollection);
      return from(addDoc(medicalRecordsRef, record)).pipe(
        map(docRef => {
          return this.ngZone.run(() => docRef.id);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error adding medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  updateMedicalRecord(recordId: string, record: Partial<MedicalRecord>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const recordDocRef = doc(this.firestore, `${this.medicalRecordsCollection}/${recordId}`);
      return from(updateDoc(recordDocRef, record)).pipe(
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error updating medical record:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }
}
