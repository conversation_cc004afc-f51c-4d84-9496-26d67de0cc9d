.doctor-profile-container {
  width: 100%;
  min-height: 100vh;
  background: #f3f4f6;
  font-family: Inter, sans-serif;
  padding-bottom: 2rem;
}

.profile-header {
  background-color: #199A8E;
  padding: 24px;
  border-radius: 8px;
  color: white;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.profile-header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  letter-spacing: -0.5px;
  color: white;
}

.profile-header p {
  opacity: 0.9;
  font-size: 16px;
  line-height: 1.5;
  color: white;
  margin-bottom: 0.5rem;
}

.profile-header .required-fields-note {
  font-size: 0.85rem;
  color: #2c3e50;
  margin-top: 1rem;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.required-fields-note .required-star {
  color: #ff3860;
  margin-right: 0.25rem;
}

.profile-content {
  margin: 0 auto;
  width: 98%;
  max-width: 1600px;
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
}

.left-column, .right-column {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.left-column {
  position: sticky;
  top: 24px;
}

.personal-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  height: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.image-preview {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 2px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-preview.has-image {
  border: 2px solid #199A8E;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #6c757d;
}

.upload-icon {
  width: 32px;
  height: 32px;
  color: #6c757d;
}

.upload-button {
  padding: 8px 16px;
  background-color: #199A8E;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.upload-button:hover {
  background-color: #158276;
}

input, textarea {
  width: 100%;
  padding: 0.875rem 1.25rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 0.9375rem;
  color: #374151;
  background: #ffffff;
  transition: all 0.2s ease;
  margin-bottom: 1rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

input::placeholder, textarea::placeholder {
  color: #afb1b5;
}

input:focus, textarea:focus {
  outline: none;
  border-color: #199a8e;
  box-shadow: 0 0 0 4px rgba(25,154,142,0.1);
}

input[readonly] {
  background-color: #f3f4f6;
  border-color: #e5e7eb;
  color: #6b7280;
  cursor: not-allowed;
}

input[readonly]:focus {
  border-color: #e5e7eb;
  box-shadow: none;
}

textarea {
  min-height: 120px;
  resize: vertical;
}

h2 {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #0a0a0a;
}

h2 i {
  margin-right: 8px;
  color: #199A8E;
}

.qualification-container,
.hospital-affiliation-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
}

.qualification-tags,
.hospital-affiliation-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 40px;
}

.qualification-pill,
.hospital-affiliation-pill {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #f3f4f6;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  color: #374151;
  height: fit-content;
}

.qualification-pill i,
.hospital-affiliation-pill i {
  cursor: pointer;
  color: #6b7280;
  transition: color 0.2s ease;
}

.qualification-pill i:hover,
.hospital-affiliation-pill i:hover {
  color: #ef4444;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 30px;
}

.save-profile, .set-availability {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.save-profile:hover, .set-availability:hover {
  background-color: #45a049;
}

.set-availability {
  background-color: #2196F3;
}

.set-availability:hover {
  background-color: #0b7dda;
}

.required-star {
  color: #ff3860;
  margin-left: 2px;
}

.required-fields-note {
  font-size: 0.85rem;
  color: #666;
  margin-bottom: 1rem;
  text-align: center;
}

@media (max-width: 1200px) {
  .profile-content {
    grid-template-columns: 350px 1fr;
  }
}

@media (max-width: 1024px) {
  .profile-content {
    grid-template-columns: 1fr;
    width: 95%;
  }

  .left-column {
    position: static;
  }
}

@media (max-width: 640px) {
  .profile-header {
    padding: 20px 24px;
  }

  .profile-content {
    width: 98%;
  }

  section {
    padding: 20px;
  }
}

/* Success Modal Styles */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.success-modal {
  background-color: white;
  border-radius: 12px;
  padding: 32px;
  width: 90%;
  max-width: 450px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.success-icon {
  font-size: 64px;
  color: #199A8E;
  margin-bottom: 16px;
}

.success-modal h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #111827;
  justify-content: center;
}

.success-modal p {
  color: #6b7280;
  margin-bottom: 24px;
  line-height: 1.5;
}

.proceed-button {
  padding: 12px 24px;
  background-color: #199A8E;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.proceed-button:hover {
  background-color: #158276;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Validation Indicators and Read-only Fields */
.invalid-input {
  border: 1px solid #ff3860 !important;
  background-color: rgba(255, 56, 96, 0.05);
}

.error-message {
  color: #ff3860;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.field-note {
  color: #6b7280;
  font-size: 0.8rem;
  margin-top: -0.5rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.field-note i {
  color: #6b7280;
  margin-right: 0.25rem;
  font-size: 0.8rem;
}

input[readonly] {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.7;
}
