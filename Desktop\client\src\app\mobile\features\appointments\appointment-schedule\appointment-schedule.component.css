.appointment-container {
    width: 100vw;
    height: 100vh;
    font-family: 'Poppins', sans-serif;
    padding: 16px;
    border-radius: 10px;
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
}

.back-button {
    background: none;
    border: none;
    font-size: 20px;
    color: #199A8E;
    cursor: pointer;

}

.header-title {
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin: 40px 40px 30px 0;
    width: 100%;
    
}

header {
    display: flex;
    justify-content: space-between;
}

header button {
  flex: 1;
  padding: clamp(5px, 1.25vw, 8px);
  margin: -20px clamp(3px, 0.75vw, 5px);
  font-size: clamp(12px, 3vw, 14px);
  font-weight: 500;
  border: none;
  border-radius: 20px;
  background: #e6f1f3;
  color: #199A8E;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 80px;
}

header button:hover:not(.active) {
  background: rgba(25, 154, 142, 0.2);
}

header button.active {
  background: #199A8E;
  color: #fff;
}

.appointment-card {
  display: flex;
  align-items: flex-start;
  background: #e6f1f3;
  border-radius: 12px;
  padding: clamp(12px, 3vw, 16px);
  margin: clamp(20px, 5vh, 30px) 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  gap: clamp(10px, 2.5vw, 12px);
}

.appointment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.doctor-image {
  width: clamp(40px, 10vw, 50px);
  height: clamp(40px, 10vw, 50px);
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.appointment-info {
  flex: 1;
}

.appointment-info h3 {
  margin: 0;
  font-size: clamp(13px, 3.25vw, 14px);
  font-weight: bold;
  color: #199A8E;
}

.appointment-info p {
  margin: clamp(4px, 1vh, 6px) 0;
  font-size: clamp(11px, 2.75vw, 12px);
  color: #555;
  line-height: 1.4;
}

.date-time-status {
  display: flex;
  align-items: center;
  gap: clamp(10px, 2.5vw, 15px);
  font-size: clamp(11px, 2.75vw, 12px);
  flex-wrap: wrap;
  margin-top: clamp(6px, 1.5vh, 8px);
}

.date-time-status p {
  display: flex;
  align-items: center;
  margin: 0;
  gap: 4px;
}

.date-time-status i {
  font-size: clamp(9px, 2.25vw, 10px);
}

.status {
  display: flex;
  align-items: center;
  font-size: clamp(11px, 2.75vw, 12px);
  font-weight: bold;
  gap: 4px;
}

.confirmed, .confirmed i {
  color: #199A8E;
}

.completed, .completed i {
  color: grey;
}

.cancelled, .cancelled i {
  color: red;
}

.status i {
  font-size: clamp(20px, 5vw, 25px);
  margin: 0;
}

.actions {
  display: flex;
  gap: clamp(8px, 2vw, 12px);
  margin-top: clamp(8px, 2vh, 12px);
  flex-wrap: wrap;
}

button {
  padding: clamp(6px, 1.5vh, 8px) clamp(10px, 2.5vw, 12px);
  font-size: clamp(11px, 2.75vw, 12px);
  border: none;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.cancel-btn,
.reschedule-btn,
.rebook-btn {
  background: white;
  color: #199A8E;
  border: 1px solid #199A8E;
  padding: clamp(2px, 0.5vh, 3px) clamp(20px, 5vw, 25px);
  transition: all 0.3s ease;
}

.cancel-btn:hover,
.reschedule-btn:hover,
.rebook-btn:hover {
    background: #199A8E;
    color: white;
}

.no-appointments {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40vh;
    margin-top: 10px; 
    text-align: center; 
}

.no-appointments p {
    color: #555;
    font-size: 16px;
    font-weight: bold;
}

.schedule-screen {
  padding: 12px;
  min-height: 100vh;
  background: #f8f9fa;
  position: relative;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.title {
  font-size: 20px;
  color: #111827;
  margin: 16px 0 20px;
  text-align: center;
  font-weight: 600;
}

/* Error and Loading States */
.error-alert {
  background: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 12px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(185, 28, 28, 0.1);
  font-size: 13px;
}

.error-alert button {
  background: #b91c1c;
  color: white;
  border: none;
  padding: 6px 10px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  height: 40vh;
}

.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid rgba(25, 154, 142, 0.1);
  border-top: 3px solid #199a8e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  color: #4b5563;
  font-size: 14px;
}

/* Content and Tabs */
.content {
  padding-top: 10px;
  margin-bottom: 80px; /* Make space for navbar */
}

.tabs {
  display: flex;
  background: white;
  border-radius: 16px;
  padding: 6px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  padding: 10px 6px;
  border: none;
  border-radius: 12px;
  background: none;
  color: #6b7280;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
}

.tab.active {
  background: #199a8e;
  color: white;
  box-shadow: 0 2px 5px rgba(25, 154, 142, 0.3);
}

.tab .badge {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ef4444;
}

/* Appointment Cards */
.appointments-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
  width: 100%;
  box-sizing: border-box;
}

.appointment-card {
  background: white;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  margin: 0;
  position: relative;
}

/* Card Status Indicator */
.card-status-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;
}

/* Card Header */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
}

.doctor-info {
  flex: 1;
  padding-right: 12px;
}

.doctor-info h3 {
  margin: 0 0 4px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.4;
}

.specialization {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

/* Status Badges */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Appointment Meta Information */
.appointment-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px 16px;
  background: #fafafa;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  flex: 1;
  min-width: 120px;
}

.meta-item i {
  color: #199a8e;
  font-size: 14px;
}

.meta-item span {
  font-size: 14px;
  color: #374151;
}

/* Visit Reason and Doctor Notes */
.visit-reason, .doctor-notes {
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
}

.visit-reason h4, .doctor-notes h4 {
  margin: 0 0 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.visit-reason p, .doctor-notes p {
  margin: 0;
  font-size: 14px;
  color: #4b5563;
  line-height: 1.6;
  overflow-wrap: break-word;
}

/* Card Actions */
.card-actions {
  padding: 12px 16px;
  border-top: 1px solid #f3f4f6;
}

.cancel-button {
  width: 100%;
  padding: 10px;
  background: #fee2e2;
  color: #b91c1c;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-button:hover {
  background: #fecaca;
}

/* Empty State */
.no-appointments {
  text-align: center;
  padding: 30px 16px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin: 10px 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-state i {
  font-size: 28px;
  color: #9ca3af;
  margin-bottom: 12px;
}

.empty-state p {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.empty-state small {
  color: #9ca3af;
  font-size: 12px;
  display: block;
  margin-top: 4px;
}

/* Status-specific styling */
.approved-appointment {
  border-left: none; /* Remove default left border as we're using the indicator now */
}

/* Animation for subtle emphasis on approved appointments */
.approved-appointment {
  animation: subtle-pulse 3s infinite ease-in-out;
}

@keyframes subtle-pulse {
  0% { box-shadow: 0 2px 8px rgba(34, 197, 94, 0.05); }
  50% { box-shadow: 0 4px 12px rgba(34, 197, 94, 0.12); }
  100% { box-shadow: 0 2px 8px rgba(34, 197, 94, 0.05); }
}

/* Responsive Design - iPhone 13 Pro and smaller screens */
@media screen and (max-width: 390px) {
  .schedule-screen {
    padding: 12px;
  }
  
  .card-header {
    padding: 12px 12px 10px 16px;
  }
  
  .meta-item {
    width: calc(50% - 3px); /* Two items per row */
    padding: 5px 8px;
    font-size: 12px;
  }
  
  .doctor-info {
    max-width: 65%;
  }
  
  .appointment-meta {
    padding: 10px 12px;
  }
  
  .visit-reason, .doctor-notes, .card-actions {
    padding: 10px 12px;
  }
  
  .visit-reason p, .doctor-notes p {
    font-size: 12px;
  }
}

/* Responsive Design */
@media (max-width: 400px) {
  .title {
    font-size: 20px;
  }
  
  .tab {
    font-size: 13px;
    padding: 10px 4px;
  }
  
  .doctor-info h3 {
    font-size: 15px;
  }
  
  .detail-item {
    min-width: 100%;
  }
}

/* Animation for subtle emphasis */
@keyframes subtle-pulse {
  0% { box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); }
  50% { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); }
  100% { box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); }
}

.approved-appointment {
  animation: subtle-pulse 3s infinite ease-in-out;
}

/* Mobile tweaks */
@media (max-width: 480px) {
  .approved-notice {
    padding: 8px;
    font-size: 14px;
  }
}

@media (max-width: 400px) {
  .schedule-screen {
    padding: 16px;
  }
  
  .title {
    font-size: 20px;
  }
  
  .appointment-card {
    padding: 12px;
  }
  
  .doctor-info h3 {
    font-size: 15px;
  }
  
  .specialization {
    font-size: 13px;
  }
  
  .detail-item {
    font-size: 13px;
  }
}

/* Responsive Design - iPhone 13 Pro Specific */
@media screen and (max-width: 390px) {
  .schedule-screen {
    padding: 12px;
  }
  
  .appointment-card {
    padding: 14px;
  }
  
  .detail-item {
    min-width: 100%;
    margin-bottom: 6px;
  }
  
  .doctor-info {
    max-width: 60%;
    margin-bottom: 6px;
  }
  
  .status-badge {
    padding: 4px 8px;
    min-width: 60px;
    font-size: 11px;
  }
  
  .tabs {
    padding: 4px;
  }
  
  .tab {
    padding: 10px 6px;
    font-size: 13px;
  }

  .appointment-details {
    padding: 8px;
  }

  .concern-section, .notes-section {
    padding: 8px;
  }
}

/* Responsive adjustments for very small devices */
@media screen and (max-width: 320px) {
  .schedule-screen {
    padding: 10px;
  }
  
  .meta-item {
    width: 100%; /* One item per row on very small devices */
  }
}
