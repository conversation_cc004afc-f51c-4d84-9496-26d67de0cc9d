.appointment-screen {
  max-width: 480px;
  margin: 0 auto;
  padding: 20px;
  font-family: system-ui, -apple-system, sans-serif;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 60px;
  min-height: 100vh;
  background: #f8f9fa;
}

.content {
  padding: 20px;
  width: auto;
  max-width: 100%;
  box-sizing: border-box;
  margin-top: 15px;
  background: white;
  border-radius: 16px;
  margin-bottom: 70px;
}

.title {
  font-size: 24px;
  font-weight: 700;
  margin: 16px 0 24px;
  color: #111827;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 8px;
}

.error-alert {
  background: #fee2e2;
  color: #b91c1c;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #199a8e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 16px;
  background-color: #F0FDFC;
  padding: 16px;
  border-radius: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.doctor-image {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 16px;
}

.doctor-details {
  flex: 1;
}

.doctor-details h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px;
  color: #111827;
}

.subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.details-grid {
  display: grid;
  gap: 16px;
  border-top: 1px solid #EAEAEA;
  border-bottom: 1px solid #EAEAEA;
  padding: 32px 0;
  margin-bottom: 24px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  margin-bottom: 24px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #6b7280;
  font-size: 14px;
}

.value {
  font-weight: 500;
  font-size: 14px;
  color: #111827;
}

.value.status {
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

/* Remove the generic status style that forces everything green */
.status {
  /* Remove color: #14B8A6; as we'll use dynamic colors */
}

/* Status-specific styles */
.status-pending {
  background-color: #fef3c7;
  color: #d97706;
}

.status-approved {
  background-color: #dcfce7;
  color: #16a34a;
}

.status-completed {
  background-color: #dbeafe;
  color: #2563eb;
}

.status-cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.status-available {
  background-color: #f3f4f6;
  color: #4b5563;
}

.problem-section, .notes-section {
  margin-bottom: 24px;
  width: 100%;
}

.problem-section h3, .notes-section h3 {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 12px;
  color: #374151;
}

.problem-text, .notes-text {
  background-color: #f9fafb;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #6b7280;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: auto;
  max-width: 100%;
}

.action-buttons {
  margin-top: 24px;
}

.cancel-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 12px;
  background: #fee2e2;
  color: #b91c1c;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
}

.cancel-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.book-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 12px;
  background: #199a8e;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
}

.no-appointment {
  text-align: center;
  padding: 32px 16px;
}

.no-appointment p {
  color: #6b7280;
  margin: 0 0 16px;
}

@media (max-width: 480px) {
  .appointment-screen {
    padding: 0 0 60px 0;
  }

  .content {
    padding: 20px;
  }
}

@media (max-width: 400px) {
  .title {
    font-size: 20px;
  }

  .content {
    padding: 16px;
  }

  .doctor-image {
    width: 48px;
    height: 48px;
  }

  .doctor-details h2 {
    font-size: 16px;
  }

  .subtitle {
    font-size: 12px;
  }
}
