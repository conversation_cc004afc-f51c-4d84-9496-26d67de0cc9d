import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  inject,
  WritableSignal,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { PatientService, Patient } from '../services/patient.service';
import { PatientCountService } from '../../../shared/services/patient-count.service';
import { Db } from '../../../../db';
import { DoctorPatient, UserType } from '../../../../type';
import { USER_TABLE } from '../../../../constant';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-view-patients',
  templateUrl: './view_patients.component.html',
  styleUrls: ['./view_patients.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  providers: [PatientService],
})
export class ViewPatientsComponent implements OnInit {
  searchQuery: string = '';
  isLoading: boolean = true;
  showAddPatientModal: boolean = false;
  showDeleteModal: boolean = false;
  selectedPatient: UserType | null = null;
  newPatientEmail: string = '';
  addingPatient: boolean = false;
  successMessage: string = '';
  errorMessage: string = '';
  sidebarCollapsed: boolean = false;
  isMobileView: boolean = false;
  doctorName: string = '';
  private patientSubscription: Subscription | undefined;

  db: Db = inject(Db);
  myPatients: WritableSignal<UserType[]> = signal<UserType[]>([]);
  filteredPatients: WritableSignal<UserType[]> = signal<UserType[]>([]);

  constructor(
    private router: Router,
    private patientService: PatientService,
    private patientCountService: PatientCountService,
    private authService: AuthService
  ) {
    this.checkScreenSize();
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.checkScreenSize();
  }

  checkScreenSize() {
    this.isMobileView = window.innerWidth <= 768;
    this.sidebarCollapsed = this.isMobileView;
  }

  ngOnInit(): void {
    this.loadDoctorPatient();
  }

  ngOnDestroy(): void {
    if (this.patientSubscription) {
      this.patientSubscription.unsubscribe();
    }
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  filterPatients(): void {
    if (!this.searchQuery) {
      this.filteredPatients.set(this.myPatients());
      return;
    }

    const query = this.searchQuery.toLowerCase();
    const filtered = this.myPatients().filter((patient) => {
      const fullName = `${patient.firstname} ${patient.lastname}`.toLowerCase();
      const email = (patient.email || '').toLowerCase();
      return fullName.includes(query) || email.includes(query);
    });

    this.filteredPatients.set(filtered);
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.filterPatients();
  }

  openAddPatientModal(): void {
    this.showAddPatientModal = true;
    this.newPatientEmail = '';
    this.successMessage = '';
    this.errorMessage = '';
  }

  closeAddPatientModal(): void {
    this.showAddPatientModal = false;
    this.newPatientEmail = '';
    this.successMessage = '';
    this.errorMessage = '';
  }

  showDeleteConfirmation(patient: UserType): void {
    this.selectedPatient = patient;
    this.showDeleteModal = true;
  }

  cancelDelete(): void {
    this.selectedPatient = null;
    this.showDeleteModal = false;
  }

  confirmDelete(): void {
    if (this.selectedPatient && this.selectedPatient.id) {
      const patientId = this.selectedPatient.id;
      const doctorId = this.db.current_doctor()?.id || '';

      // Find and remove the doctor-patient relationship from local storage
      const doctorPatients = this.db.doctorPatientTable().filter(
        dp => dp.doctor_id === doctorId &&
              dp.patient_id === patientId
      );

      if (doctorPatients.length > 0) {
        // Remove the relationship from the local database
        this.db.removeDoctorPatient(doctorPatients[0].id);

        // Also remove from Firebase
        this.patientService.removePatient(patientId, doctorId)
          .subscribe({
            next: () => {
              console.log('Patient successfully removed from Firebase');
            },
            error: (err) => {
              console.error('Error removing patient from Firebase:', err);
            }
          });

        // Clean up appointments that reference this patient
        this.cleanupPatientAppointments(patientId);

        // Refresh the patient list and update count
        this.loadDoctorPatient();
      }

      // Close the modal
      this.showDeleteModal = false;
      this.selectedPatient = null;
    }
  }

  // New method to clean up appointments referencing deleted patient
  cleanupPatientAppointments(patientId: string): void {
    // Get all appointments for this patient
    const patientAppointments = this.db.appointmentTable()
      .filter(app => app.patient_id === patientId);

    // Delete each appointment
    patientAppointments.forEach(app => {
      this.db.deleteAppointment(app.id);
    });
  }

  logout(): void {
    this.router.navigate(['/login']);
  }

  loadDoctorPatient(): void {
    // Get current doctor ID from Auth directly
    const currentUser = this.authService.getCurrentUser();
    if (currentUser && currentUser.uid) {
      const currentDoctorId = currentUser.uid;

      console.log('Loading patients for authenticated doctor ID:', currentDoctorId);

      // Use the patient service to get patients from Firebase
      this.patientService.getDoctorPatients(currentDoctorId).subscribe({
        next: (patients) => {
          console.log('Patients loaded from Firebase:', patients);

          if (patients.length === 0) {
            // No patients found for this doctor
            this.myPatients.set([]);
            this.filteredPatients.set([]);
            this.patientCountService.updatePatientCount(0);
            this.isLoading = false;
            return;
          }

          // Map Firebase Patient objects to UserType objects
          const userTypePatients: UserType[] = patients.map(patient => ({
            id: patient.uid || '',
            email: patient.email || '',
            firstname: this.extractFirstName(patient),
            lastname: this.extractLastName(patient),
            password: '', // Required by UserType but not used
            role: 'PATIENT'
          }));

          this.myPatients.set(userTypePatients);
          this.filteredPatients.set(userTypePatients);
          // Update the patient count in the service
          this.patientCountService.updatePatientCount(userTypePatients.length);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading patients from Firebase:', error);
          this.isLoading = false;
          // Only use filtered local storage as a fallback
          this.loadFromLocalStorage(currentDoctorId);
        }
      });
    } else {
      console.error('No current doctor found');
      this.myPatients.set([]);
      this.filteredPatients.set([]);
      this.patientCountService.updatePatientCount(0);
      this.isLoading = false;
      // Try local storage as fallback
      this.loadFromLocalStorage();
    }
  }

  private loadFromLocalStorage(doctorId?: string): void {
    const currentDoctor = this.db.current_doctor();
    const currentDoctorId = doctorId || currentDoctor?.id;

    if (!currentDoctorId) {
      console.error('No current doctor found');
      this.myPatients.set([]);
      this.filteredPatients.set([]);
      this.patientCountService.updatePatientCount(0);
      return;
    }

    const doctorPatients: DoctorPatient[] = this.db
      .doctorPatientTable()
      .filter((val) => val.doctor_id === currentDoctorId);

    if (doctorPatients.length === 0) {
      // No patients found for this doctor in local storage
      this.myPatients.set([]);
      this.filteredPatients.set([]);
      this.patientCountService.updatePatientCount(0);
      return;
    }

    let patients: UserType[] = [];

    for (let i = 0; i < doctorPatients.length; i++) {
      for (let x = 0; x < this.db.userTable().length; x++) {
        if (doctorPatients[i].patient_id === this.db.userTable()[x].id) {
          patients.push(this.db.userTable()[x]);
        }
      }
    }

    this.myPatients.set(patients);
    this.filteredPatients.set(patients);
    this.patientCountService.updatePatientCount(patients.length);
  }

  addPatient(): void {
    this.addingPatient = true;
    this.errorMessage = '';
    this.successMessage = '';

    // Get the current doctor ID from Auth
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser || !currentUser.uid) {
      this.errorMessage = 'Doctor ID not found. Please log in again.';
      this.addingPatient = false;
      return;
    }

    const doctorId = currentUser.uid;
    console.log(`Doctor ${doctorId} is adding patient with email: ${this.newPatientEmail}`);

    // First check if patient exists in local database
    let patient: UserType | null =
      this.db.userTable().find((user) => user.email == this.newPatientEmail) ||
      null;

    if (patient && patient.id) {
      console.log(`Patient found in local database with ID: ${patient.id}`);

      // Create local doctor-patient relationship
      let doctorPatient: DoctorPatient = {
        id: this.db.generateId(),
        doctor_id: doctorId,
        patient_id: patient.id,
      };

      // Add to local DB first for immediate feedback
      this.db.addDoctorPatient(doctorPatient);

      // Then create the relationship in Firebase
      this.patientService.addPatientToDoctor(patient.id, doctorId)
        .subscribe({
          next: () => {
            console.log('Doctor-patient relationship successfully added to Firebase');
            this.successMessage = 'Patient added successfully to your patient list';

            // Update locally immediately for better UX
            this.updateLocalPatientsList(patient as UserType);
          },
          error: (err) => {
            console.error('Error adding doctor-patient relationship to Firebase:', err);
            // Still show success since local storage was updated
            this.successMessage = 'Patient added locally, but could not update Firebase';

            // Still update locally
            this.updateLocalPatientsList(patient as UserType);
          },
          complete: () => {
            // Update the patient list and count from Firebase
            setTimeout(() => {
              this.loadDoctorPatient();

              // Close modal after delay
              setTimeout(() => {
                this.closeAddPatientModal();
              }, 1000);
            }, 500);
          }
        });

      this.addingPatient = false;
      return;
    }

    // If not found in local database, check Firebase
    this.continueAddPatientWithFirebase(doctorId);
  }

  private continueAddPatientWithFirebase(doctorId: string): void {
    console.log('Patient not found in local database, checking Firebase...');
    this.patientService.findPatientByEmail(this.newPatientEmail)
      .pipe(
        finalize(() => {
          this.addingPatient = false;
        })
      )
      .subscribe({
        next: (firebasePatient) => {
          if (firebasePatient && firebasePatient.uid) {
            console.log('Patient found in Firebase:', firebasePatient);

            // Create a new UserType object from Firebase patient data
            const newPatient: UserType = {
              id: firebasePatient.uid,
              email: firebasePatient.email || this.newPatientEmail,
              firstname: this.extractFirstName(firebasePatient),
              lastname: this.extractLastName(firebasePatient),
              password: '', // We don't know the password, but it's required by the type
              role: 'PATIENT'
            };

            // Add patient to the userTable directly
            this.db.userTable.update(users => [...users, newPatient]);
            this.db.storage.setItem('USER_TABLE', this.db.userTable());

            // Create doctor-patient relationship in both local and Firebase
            let doctorPatient: DoctorPatient = {
              id: this.db.generateId(),
              doctor_id: doctorId,
              patient_id: newPatient.id,
            };

            // Add to local database
            this.db.addDoctorPatient(doctorPatient);

            // Update locally immediately for better UX
            this.updateLocalPatientsList(newPatient);

            // Also add to Firebase
            this.patientService.addPatientToDoctor(newPatient.id, doctorId)
              .subscribe({
                next: () => {
                  console.log('Doctor-patient relationship successfully added to Firebase');
                  this.successMessage = 'Patient imported from Firebase and added successfully';
                },
                error: (err) => {
                  console.error('Error adding doctor-patient relationship to Firebase:', err);
                  this.successMessage = 'Patient imported and added locally, but Firebase update failed';
                },
                complete: () => {
                  // Update the patient list
                  setTimeout(() => {
                    this.loadDoctorPatient();

                    // Close modal after delay
                    setTimeout(() => {
                      this.closeAddPatientModal();
                    }, 1000);
                  }, 500);
                }
              });
          } else {
            console.log('Patient not found in Firebase');
            this.errorMessage = 'Patient with this email does not exist in the system';
            setTimeout(() => {
              this.errorMessage = '';
            }, 5000);
          }
        },
        error: (error) => {
          console.error('Error searching for patient in Firebase:', error);
          this.errorMessage = 'Error searching for patient. Please try again.';
          setTimeout(() => {
            this.errorMessage = '';
          }, 5000);
        }
      });
  }

  // Helper method to update local patient list immediately
  private updateLocalPatientsList(patient: UserType): void {
    // Check if this patient is already in the myPatients list
    const existingPatient = this.myPatients().find(p => p.id === patient.id);
    if (!existingPatient) {
      // Add the patient to the local lists for immediate display
      this.myPatients.update(patients => [...patients, patient]);
      this.filteredPatients.update(patients => [...patients, patient]);
      // Update the patient count
      this.patientCountService.updatePatientCount(this.myPatients().length);
    }
  }

  // Helper method to extract first name from patient data with different possible field names
  private extractFirstName(patient: any): string {
    // Check all possible field name variations
    return patient.firstName ||
           patient.first_name ||
           patient.firstname ||
           (patient.displayName ? patient.displayName.split(' ')[0] : '') ||
           (patient.fullName ? patient.fullName.split(' ')[0] : '') ||
           (patient.name ? patient.name.split(' ')[0] : '') ||
           '';
  }

  // Helper method to extract last name from patient data with different possible field names
  private extractLastName(patient: any): string {
    // Check all possible field name variations
    return patient.lastName ||
           patient.last_name ||
           patient.lastname ||
           (patient.displayName && patient.displayName.split(' ').length > 1
             ? patient.displayName.split(' ').slice(1).join(' ')
             : '') ||
           (patient.fullName && patient.fullName.split(' ').length > 1
             ? patient.fullName.split(' ').slice(1).join(' ')
             : '') ||
           (patient.name && patient.name.split(' ').length > 1
             ? patient.name.split(' ').slice(1).join(' ')
             : '') ||
           '';
  }
}
