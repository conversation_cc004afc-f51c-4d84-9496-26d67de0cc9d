import { inject, Injectable } from '@angular/core';
import { AppointmentStatusType, AppointmentType, UserType } from './type';
import { Db } from './db';
import { Storage } from './storage';
import { APPOINTMENT_TABLE } from './constant';
import { FirebaseDataService } from './core/services/firebase-data.service';

@Injectable({
  providedIn: 'root',
})
export class SecuraService {
  db: Db = inject(Db);
  storage: Storage = inject(Storage);
  private firebaseDataService = inject(FirebaseDataService);

  findBookingByDoctorId() {}

  findBookingByPatientId() {}

  doctorAppoitments(doctor_id: string): AppointmentType[] {
    return this.firebaseDataService.getAppointmentsByDoctorId(doctor_id);
  }

  viewUserById(user_id: string): UserType | undefined {
    return this.firebaseDataService.getUsers().find((user) => user.id == user_id);
  }

  patientAppointments(
    patient_id: string,
    status: AppointmentStatusType
  ): AppointmentType[] {
    return this.firebaseDataService.getAppointmentsByPatientId(patient_id, status);
  }

  appointmentStatus(
    appointment_id: string,
    status: AppointmentStatusType
  ): void {
    this.firebaseDataService.updateAppointment(appointment_id, { status }).subscribe({
      next: () => console.log('Appointment status updated'),
      error: (error) => console.error('Error updating appointment status:', error)
    });
  }
}
