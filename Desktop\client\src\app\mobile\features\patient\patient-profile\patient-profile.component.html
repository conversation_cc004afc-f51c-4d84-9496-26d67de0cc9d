
  
  <div class="gradient-background">
  <div class="profile-container">
    <header>
      <app-navbar />
      <h1>My Profile</h1>
    </header>

    <div class="profile-image-container">
      <div class="image-preview" [class.has-image]="selectedImage">
        <div class="upload-placeholder" *ngIf="!selectedImage">
          <i class="bi bi-person"></i>
        </div>
        <img *ngIf="selectedImage" [src]="selectedImage" alt="Profile">
        <div class="loading-overlay" *ngIf="isLoading">
          <div class="spinner"></div>
        </div>
      </div>
    </div>

    <div class="menu-items">
      <button class="menu-item" routerLink="/mobile/patient-update-profile">
        <div class="menu-icon">
          <i class="bi bi-person"></i>
        </div>
        <span>Update Profile</span>
        <i class="bi bi-chevron-right chevron"></i>
      </button>

      <button class="menu-item" routerLink="/mobile/medical-record">
        <div class="menu-icon">
          <i class="bi bi-key"></i>
        </div>
        <span>View Medical Records</span>
        <i class="bi bi-chevron-right chevron"></i>
      </button>

      <button class="menu-item" (click)="openLogoutModal()">
        <div class="menu-icon">
          <i class="bi bi-box-arrow-right"></i>
        </div>
        <span>Logout</span>
        <i class="bi bi-chevron-right chevron"></i>
      </button>
    </div>


    <div class="modal-overlay" *ngIf="showLogoutModal">
      <div class="modal">
        <h2>Logout</h2>
        <p>Are you sure you want to logout?</p>
        <div class="modal-buttons">
          <button class="cancel-button" (click)="closeLogoutModal()">Cancel</button>
          <button class="logout-button" (click)="logout()">Logout</button>
        </div>
      </div>
    </div>
  </div>
</div>
