import{Cb as T,Fb as F,Ib as P,Ic as E,Jb as R,Jc as x,Kc as C,Ra as D,Tb as j,Zb as S,a as p,ab as U,b as d,ec as A,fc as $,h as I,i as b,j as m,k as l,l as h,o as u,t as _,v as f,x as w,z as g}from"./chunk-YV65XDJO.js";var y=function(a){return a[a.State=0]="State",a[a.Transition=1]="Transition",a[a.Sequence=2]="Sequence",a[a.Group=3]="Group",a[a.Animate=4]="Animate",a[a.Keyframes=5]="Keyframes",a[a.Style=6]="Style",a[a.Trigger=7]="Trigger",a[a.Reference=8]="Reference",a[a.AnimateChild=9]="AnimateChild",a[a.AnimateRef=10]="AnimateRef",a[a.Query=11]="Query",a[a.<PERSON>agger=12]="Stagger",a}(y||{}),B="*";function H(a,t){return{type:y.Trigger,name:a,definitions:t,options:{}}}function M(a,t=null){return{type:y.Animate,styles:t,timings:a}}function G(a,t=null){return{type:y.Sequence,steps:a,options:t}}function J(a){return{type:y.Style,styles:a,offset:null}}function Q(a,t,e=null){return{type:y.Transition,expr:a,animation:t,options:e}}var L=class{_onDoneFns=[];_onStartFns=[];_onDestroyFns=[];_originalOnDoneFns=[];_originalOnStartFns=[];_started=!1;_destroyed=!1;_finished=!1;_position=0;parentPlayer=null;totalTime;constructor(t=0,e=0){this.totalTime=t+e}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(i=>i()),e.length=0}},q=class{_onDoneFns=[];_onStartFns=[];_finished=!1;_started=!1;_destroyed=!1;_onDestroyFns=[];parentPlayer=null;totalTime=0;players;constructor(t){this.players=t;let e=0,i=0,r=0,o=this.players.length;o==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(n=>{n.onDone(()=>{++e==o&&this._onFinish()}),n.onDestroy(()=>{++i==o&&this._onDestroy()}),n.onStart(()=>{++r==o&&this._onStart()})}),this.totalTime=this.players.reduce((n,s)=>Math.max(n,s.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let e=t*this.totalTime;this.players.forEach(i=>{let r=i.totalTime?Math.min(1,e/i.totalTime):1;i.setPosition(r)})}getPosition(){let t=this.players.reduce((e,i)=>e===null||i.totalTime>e.totalTime?i:e,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let e=t=="start"?this._onStartFns:this._onDoneFns;e.forEach(i=>i()),e.length=0}},W="!";var N=class a{constructor(t,e,i,r,o,n){this.http=t;this.router=e;this.db=i;this.auth=r;this.firestore=o;this.authErrorMapper=n;this.autoLogin()}USER_INFO_KEY="med_secure_user_info";TOKEN_KEY="med_secure_token";userInfoSubject=new I(null);isAuthenticated$=this.userInfoSubject.pipe(h(t=>!!t));register(t){return this.db.userTable().find(i=>i.email===t.email)?l(()=>({message:"Email already exists",success:!1})):b(F(this.auth,t.email,t.password)).pipe(_(i=>{let r=i.user.uid,o={id:r,firstname:t.firstName,lastname:t.lastName,email:t.email,password:t.password,role:t.role.toUpperCase()};this.db.register(o),console.log("Registration successful with local DB:",o);let n={uid:r,firstName:t.firstName,lastName:t.lastName,email:t.email,role:t.role.toLowerCase(),createdAt:new Date().toISOString(),profileComplete:!1,bio:"",phoneNumber:"",hospitalAffiliations:[],qualifications:[],services:[],profilePicture:null},s={name:`${t.firstName} ${t.lastName}`,email:t.email,role:t.role.toLowerCase(),id:r,firstName:t.firstName,lastName:t.lastName,bio:"",phoneNumber:"",hospitalAffiliations:[],qualifications:[],services:[],profilePicture:null};if(t.role.toLowerCase()==="doctor"){Object.assign(n,{specialization:t.specialty||"",medicalLicenseNumber:t.medicalLicenseNumber||""}),s.doctorId=r,s.specialization=t.specialty;let O={id:r,bio:"",image:"",hospitalname:"",qualification:t.medicalLicenseNumber||"",specialisation:t.specialty||"",contact:"",paymentPlan:"",user_id:r};this.db.addNewDoctor(O)}this.saveUserInfo(s);let c=S(this.firestore,`users/${r}`);return b(A(c,n)).pipe(h(()=>({userId:r,message:"Registration successful",success:!0})))}),u(i=>{console.error("Firebase registration error:",i);let r=this.authErrorMapper.mapRegistrationError(i);return l(()=>({message:r,success:!1}))}))}login(t,e){return b(P(this.auth,t,e)).pipe(_(i=>{let r=i.user.uid,o=this.db.login(t,e);if(o){let n={userId:r,name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),success:!0};if(o.role==="DOCTOR"){let s=this.db.doctorTable().find(c=>c.user_id===o.id);if(s){let c={name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),id:r,doctorId:s.id,firstName:o.firstname,lastName:o.lastname,specialization:s.specialisation,bio:s.bio,phoneNumber:s.contact,hospitalAffiliations:s.hospitalname,profilePicture:s.image||null};this.saveUserInfo(c)}}else{let s={name:`${o.firstname} ${o.lastname}`,email:o.email,role:o.role.toLowerCase(),id:r,firstName:o.firstname,lastName:o.lastname};this.saveUserInfo(s)}return m(n)}else{let n=t.split("@")[0].split("."),s=n[0]||"User",c=n.length>1?n[1]:"",v={id:r,firstname:s,lastname:c,email:t,password:e,role:"DOCTOR"};this.db.register(v);let O={name:`${s} ${c}`,email:t,role:"doctor",id:r,doctorId:r,firstName:s,lastName:c};return this.saveUserInfo(O),m({userId:r,name:`${s} ${c}`,email:t,role:"doctor",success:!0})}}),u(i=>{console.error("Firebase login error:",i);let r=this.db.login(t,e);if(r)return this.migrateLocalUserToFirebase(r,e);let o=this.authErrorMapper.mapFirebaseError(i);return l(()=>({message:o,success:!1}))}))}migrateLocalUserToFirebase(t,e){return b(F(this.auth,t.email,e)).pipe(_(i=>{let r=i.user.uid;t.id=r;let o={uid:r,firstName:t.firstname,lastName:t.lastname,email:t.email,role:t.role.toLowerCase(),createdAt:new Date().toISOString()},n=S(this.firestore,`users/${r}`);return b(A(n,o)).pipe(h(()=>{let s={name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),id:r,firstName:t.firstname,lastName:t.lastname};return this.saveUserInfo(s),{userId:r,name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),success:!0}}))}),u(i=>{console.error("Migration to Firebase failed:",i);let r={name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),id:t.id,firstName:t.firstname,lastName:t.lastname};return this.saveUserInfo(r),m({userId:t.id,name:`${t.firstname} ${t.lastname}`,email:t.email,role:t.role.toLowerCase(),success:!0})}))}saveUserInfo(t){localStorage.setItem(this.USER_INFO_KEY,JSON.stringify(t)),t.token&&localStorage.setItem(this.TOKEN_KEY,t.token),this.userInfoSubject.next(t),console.log("User info saved and updated:",t)}getUserInfo(){let t=localStorage.getItem(this.USER_INFO_KEY);if(t)try{let i=JSON.parse(t);return this.userInfoSubject.getValue()||this.userInfoSubject.next(i),i}catch(i){return console.error("Error parsing user info:",i),null}return this.userInfoSubject.getValue()}isAuthenticated(){return!!this.getUserInfo()}logout(){R(this.auth).then(()=>{localStorage.removeItem(this.USER_INFO_KEY),localStorage.removeItem(this.TOKEN_KEY),this.userInfoSubject.next(null),this.router.navigate(["/login"])}).catch(t=>{console.error("Logout error:",t)})}autoLogin(){let t=localStorage.getItem(this.USER_INFO_KEY);if(t)try{let e=JSON.parse(t);this.userInfoSubject.next(e),e.role==="doctor"&&e.doctorId&&this.getDoctor(e.doctorId).subscribe({next:i=>{let r=p(p({},e),i);this.saveUserInfo(r)},error:i=>console.error("Error refreshing doctor info:",i)}),console.log("Auto login successful:",e)}catch(e){console.error("Error during auto login:",e)}}updateUserInfo(t){let e=this.getUserInfo();if(e){let i=p(p({},e),t);this.saveUserInfo(i)}}getDoctorId(){let t=this.getUserInfo();return t?t.role&&t.role.toLowerCase()==="doctor"?t.doctorId||t.id||null:t.doctorId||null:null}updateProfile(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("Doctor ID not found"));let i=this.db.doctorTable().find(s=>s.id===e);if(!i)return l(()=>new Error("Doctor profile not found"));let r=p({},i);t.bio&&(r.bio=t.bio),t.hospitalname&&(r.hospitalname=t.hospitalname),t.specialization&&(r.specialisation=t.specialization),t.contact&&(r.contact=t.contact),t.experience&&(r.hospitalname=t.experience),t.education&&(r.qualification=t.education),this.db.doctorTable.update(s=>s.map(c=>c.id===e?r:c)),this.db.storage.setItem("DOCTOR_TABLE",this.db.doctorTable());let o={bio:t.bio||"",hospitalAffiliations:t.experience||t.hospitalname||"",specialization:t.specialization||"",phoneNumber:t.contactNumber||t.contact||"",qualifications:t.education||"",services:t.services||"",updatedAt:new Date().toISOString()},n=S(this.firestore,`users/${e}`);return b($(n,o)).pipe(h(()=>{let s=this.getUserInfo();if(s){let c=d(p({},s),{specialization:t.specialization,bio:t.bio,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services});this.saveUserInfo(c)}return{message:"Profile updated successfully in both local DB and Firestore",id:e,bio:t.bio,specialization:t.specialization,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services}}),u(s=>{console.error("Error updating profile in Firestore:",s);let c=this.getUserInfo();if(c){let v=d(p({},c),{specialization:t.specialization,bio:t.bio,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname,qualifications:t.education,services:t.services});this.saveUserInfo(v)}return m({message:"Profile updated in local DB only (Firestore update failed)",id:e,bio:t.bio,specialization:t.specialization,phoneNumber:t.contactNumber||t.contact,hospitalAffiliations:t.experience||t.hospitalname})}))}updateProfilePicture(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("Doctor ID not found"));let i=S(this.firestore,`users/${e}`);return b($(i,{profilePicture:t,updatedAt:new Date().toISOString()})).pipe(f(()=>{console.log("Profile picture updated successfully in Firestore");let r=this.getUserInfo();if(r){let o=d(p({},r),{profilePicture:t});this.saveUserInfo(o)}}),u(r=>(console.warn("Error updating profile picture in Firestore, creating document:",r),b(A(i,{profilePicture:t,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{merge:!0})).pipe(f(()=>console.log("Created new user document in Firestore with profile picture")),u(o=>(console.error("Could not create user document in Firestore:",o),m(null)))))),_(()=>{let r=this.db.doctorTable().find(n=>n.id===e);if(r){let n=d(p({},r),{image:t});this.db.doctorTable.update(s=>s.map(c=>c.id===e?n:c))}else{console.log("Doctor profile not found in local DB, creating it");let n=this.getUserInfo(),s=n?.id||e,c={id:e,bio:n?.bio||"",image:t,hospitalname:n?.hospitalAffiliations?.toString()||"",qualification:n?.qualifications?.toString()||"",specialisation:n?.specialization||"",contact:n?.phoneNumber||"",paymentPlan:"Free",user_id:s};this.db.doctorTable.update(v=>[...v,c]),r=c}this.db.storage.setItem("DOCTOR_TABLE",this.db.doctorTable());let o=this.getUserInfo();if(o){let n=d(p({},o),{profilePicture:t});this.saveUserInfo(n);let s=this.db.current_doctor();s&&s.id&&this.db.storage.setItem("CURRENT_DOCTOR",s)}return m({message:"Profile picture updated successfully",profilePicture:t})}))}getDoctor(t){let e=this.db.doctorTable().find(r=>r.id===t);if(!e)return l(()=>new Error("Doctor not found"));let i=this.db.userTable().find(r=>r.id===e.user_id);return i?m({id:e.id,firstName:i.firstname,lastName:i.lastname,email:i.email,bio:e.bio,specialization:e.specialisation,phoneNumber:e.contact,hospitalAffiliations:e.hospitalname,profilePicture:e.image}):l(()=>new Error("User not found for doctor"))}clearUserInfo(){localStorage.removeItem(this.USER_INFO_KEY),localStorage.removeItem(this.TOKEN_KEY),this.userInfoSubject.next(null)}verifyCurrentPassword(t){let e=this.getUserInfo();if(!e||!e.email)return l(()=>new Error("User not logged in"));let i=this.db.userTable().find(r=>r.email===e.email);return i?i.password===t?m({verified:!0}):l(()=>new Error("Current password is incorrect")):l(()=>new Error("User not found"))}changePassword(t,e){let i=this.getUserInfo();if(!i||!i.email)return l(()=>new Error("User not logged in"));let r=this.db.userTable().find(n=>n.email===i.email);if(!r)return l(()=>new Error("User not found"));if(r.password!==t)return l(()=>new Error("Current password is incorrect"));let o=d(p({},r),{password:e});return this.db.userTable.update(n=>n.map(s=>s.id===r.id?o:s)),this.db.storage.setItem("USER_TABLE",this.db.userTable()),m({message:"Password changed successfully"})}static \u0275fac=function(e){return new(e||a)(g(D),g(U),g(E),g(T),g(j),g(x))};static \u0275prov=w({token:a,factory:a.\u0275fac,providedIn:"root"})};var z=class a{constructor(t,e,i){this.http=t;this.authService=e;this.db=i;this.authService.isAuthenticated$.subscribe(r=>{r?this.initializeAppointments():(this.appointmentsSubject.next([]),this.alertsSubject.next([]))}),this.authService.isAuthenticated()&&this.initializeAppointments()}apiUrl=`${C.apiUrl}/api/appointments`;appointments=new I([]);alerts=new I([]);appointmentError$=new I("");appointmentsSubject=new I([]);appointments$=this.appointmentsSubject.asObservable();alertsSubject=new I([]);alerts$=this.alertsSubject.asObservable();getDoctorId(){return this.authService.getDoctorId()}loadAppointments(){let t=this.getDoctorId();if(!t)return console.warn("No doctor ID found - Please ensure you are logged in as a doctor"),this.appointmentError$.next("No doctor ID found - Please ensure you are logged in as a doctor"),this.appointments.next([]),m([]);let e=parseInt(t,10);return isNaN(e)?(console.error("Invalid doctor ID format:",t),this.appointmentError$.next("Invalid doctor ID format"),m([])):(console.log("Loading appointments for doctor ID:",e),this.http.get(`${this.apiUrl}/doctor/${e}`).pipe(h(i=>{let r=i.map(o=>d(p({},o),{appointmentDate:new Date(o.appointmentDate)}));return console.log("Loaded appointments:",r),this.appointments.next(r),r}),u(i=>{console.error("Error loading appointments:",i);let r=i.error?.message||"Failed to load appointments";return this.appointmentError$.next(r),l(()=>new Error(r))})))}getAppointments(){return this.appointments.asObservable()}getAppointmentsByDateRange(t,e,i){let r=e.toISOString().split("T")[0],o=i.toISOString().split("T")[0];return this.http.get(`${this.apiUrl}/doctor/${t}/date-range?startDate=${r}&endDate=${o}`).pipe(h(n=>n.map(s=>d(p({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(n=>{this.appointments.next(n)}),u(n=>(console.error("Error loading appointments by date range:",n),m([]))))}getAppointment(t){return this.http.get(`${this.apiUrl}/${t}`).pipe(h(e=>d(p({},e),{appointmentDate:new Date(e.appointmentDate)})),u(e=>{console.error("Error fetching appointment by id:",e);let i=this.appointments.getValue().find(r=>r.appointmentId===t);return m(i)}))}createDoctorAvailabilitySlot(t,e,i,r){let o=t||this.getDoctorId();if(!o)return l(()=>new Error("No doctor ID found"));let n=e.toISOString().split("T")[0];return this.http.post(`${this.apiUrl}/doctor/${o}/availability`,{},{params:{date:n,startTime:i,durationMinutes:r.toString()}}).pipe(h(s=>d(p({},s),{appointmentDate:new Date(s.appointmentDate)})),f(s=>{let c=this.appointments.getValue();this.appointments.next([...c,s]),this.addAlert({id:Date.now().toString(),message:"New availability slot created successfully",type:"info",time:"just now"})}),u(s=>(console.error("Error creating availability slot:",s),l(()=>new Error("Failed to create availability slot")))))}createDoctorAvailabilityBatch(t,e,i,r){let o=e.map(n=>n.toISOString().split("T")[0]);return this.http.post(`${this.apiUrl}/doctor/${t}/availability/batch`,{},{params:{dates:o,startTimes:i,durationMinutes:r.toString()}}).pipe(h(n=>n.map(s=>d(p({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(n=>{let s=this.appointments.getValue();this.appointments.next([...s,...n]),this.addAlert({id:Date.now().toString(),message:`Created ${n.length} availability slots successfully`,type:"info",time:"just now"})}),u(n=>(console.error("Error adding batch availability slots:",n),l(()=>new Error("Failed to create availability slots")))))}createRecurringAvailability(t){let e=this.getDoctorId();if(!e)return l(()=>new Error("No doctor ID found"));let i=t.weekdays.map((n,s)=>n?s:-1).filter(n=>n!==-1),r=t.startDate.toISOString().split("T")[0],o=t.endDate.toISOString().split("T")[0];return this.http.post(`${this.apiUrl}/doctor/${e}/availability/recurring`,{},{params:{startDate:r,endDate:o,daysOfWeek:i,startTime:t.startTime,endTime:t.endTime,slotDuration:t.slotDuration.toString()}}).pipe(h(n=>n.map(s=>d(p({},s),{appointmentDate:new Date(s.appointmentDate)}))),f(n=>{let s=this.appointments.getValue();this.appointments.next([...s,...n]),this.addAlert({id:Date.now().toString(),message:`Created ${n.length} recurring availability slots`,type:"info",time:"just now"})}),u(n=>(console.error("Error creating recurring availability:",n),l(()=>new Error("Failed to create recurring availability slots")))))}updateAppointment(t){return this.http.put(`${this.apiUrl}/${t.appointmentId}`,t).pipe(f(e=>{this.updateAppointmentInList(e)}),u(e=>(console.error("Error updating appointment:",e),l(()=>new Error("Failed to update appointment")))))}updateAppointmentInList(t){let i=this.appointments.getValue().map(r=>r.appointmentId===t.appointmentId?d(p({},t),{appointmentDate:new Date(t.appointmentDate)}):r);this.appointments.next(i)}deleteAppointmentSlot(t){return this.http.delete(`${this.apiUrl}/${t}/availability`).pipe(f(()=>{let e=this.appointments.getValue();this.appointments.next(e.filter(i=>i.appointmentId!==t)),this.addAlert({id:Date.now().toString(),message:"Availability slot was deleted",type:"info",time:"just now"})}),u(e=>(console.error("Error deleting availability slot:",e),l(()=>new Error("Failed to delete availability slot")))))}getDoctorAvailabilitySlots(t){return this.http.get(`${this.apiUrl}/doctor/${t}/availability`).pipe(h(e=>e.map(i=>d(p({},i),{appointmentDate:new Date(i.appointmentDate)}))),f(e=>{let i=this.appointments.getValue(),r=new Set(i.map(n=>n.appointmentId)),o=e.filter(n=>!r.has(n.appointmentId));o.length>0&&this.appointments.next([...i,...o])}),u(e=>(console.error("Error fetching doctor availability slots:",e),m([]))))}getDoctorUpcomingAppointments(t){return this.http.get(`${this.apiUrl}/doctor/${t}/upcoming`).pipe(h(e=>e.map(i=>d(p({},i),{appointmentDate:new Date(i.appointmentDate)}))),f(e=>{this.appointments.next(e)}),u(e=>(console.error("Error fetching doctor upcoming appointments:",e),m([]))))}getDoctorPendingAppointments(t){return this.http.get(`${this.apiUrl}/doctor/${t}/pending`).pipe(h(e=>e.map(i=>d(p({},i),{appointmentDate:new Date(i.appointmentDate)}))),u(e=>(console.error("Error fetching pending appointments:",e),m([]))))}approveAppointment(t,e){return this.http.put(`${this.apiUrl}/${t}/approve`,{},{params:{doctorNotes:e||""}}).pipe(f(i=>{this.updateAppointmentInList(i),this.addAlert({id:Date.now().toString(),message:`Appointment with ${i.patientName} approved`,type:"info",time:"just now"})}),u(i=>(console.error("Error approving appointment:",i),l(()=>new Error("Failed to approve appointment")))))}rejectAppointment(t,e){return this.http.put(`${this.apiUrl}/${t}/reject`,{},{params:{doctorNotes:e||""}}).pipe(f(i=>{this.updateAppointmentInList(i),this.addAlert({id:Date.now().toString(),message:`Appointment with ${i.patientName} rejected`,type:"error",time:"just now"})}),u(i=>(console.error("Error rejecting appointment:",i),l(()=>new Error("Failed to reject appointment")))))}getAlerts(){return this.alerts.asObservable()}getCurrentAppointments(){return this.appointments.getValue()}getCurrentAlerts(){return this.alerts.getValue()}getTodaysAppointments(){let t=new Date;return t.setHours(0,0,0,0),this.appointments.getValue().filter(e=>{let i=new Date(e.appointmentDate);return i.setHours(0,0,0,0),i.getTime()===t.getTime()&&e.status==="Approved"})}getPendingConsultations(){return this.appointments.getValue().filter(t=>t.status==="Pending"&&!t.isDoctorAvailabilitySlot).length}generateAlerts(t){let e=[],i=new Date,r=this.getPendingConsultations();r>0&&e.push({id:"pending-alert",type:"info",message:`You have ${r} pending appointment requests`,time:"just now"});let o=this.getTodaysAppointments().length;o>0&&e.push({id:"today-alert",type:"warning",message:`You have ${o} appointments scheduled for today`,time:"just now"}),this.alerts.next(e)}getTotalPatients(){return new Set(this.appointments.getValue().filter(e=>e.patientId).map(e=>e.patientId)).size}getAppointmentErrors(){return this.appointmentError$.asObservable()}clearAppointmentErrors(){this.appointmentError$.next("")}refreshAppointments(){this.loadAppointments()}addAlert(t){let e=this.alerts.getValue();this.alerts.next([t,...e])}initializeAppointments(){let t=this.authService.getDoctorId();if(!t){console.error("No doctor ID found");return}let e=this.db.appointmentTable().filter(r=>r.doctor_id===t).map(r=>{let o=this.db.patientTable().find(s=>s.id===r.patient_id),n=o?this.db.userTable().find(s=>s.id===o.user_id):null;return d(p({},r),{patientName:n?`${n.firstname} ${n.lastname}`:"Unknown Patient"})});this.appointmentsSubject.next(e);let i=e.filter(r=>r.status==="Pending").map(r=>({type:"info",message:`New appointment request from ${r.patientName}`,time:new Date(r.date).toLocaleDateString()}));this.alertsSubject.next(i)}static \u0275fac=function(e){return new(e||a)(g(D),g(N),g(E))};static \u0275prov=w({token:a,factory:a.\u0275fac,providedIn:"root"})};export{y as a,B as b,H as c,M as d,G as e,J as f,Q as g,L as h,q as i,W as j,N as k,z as l};
