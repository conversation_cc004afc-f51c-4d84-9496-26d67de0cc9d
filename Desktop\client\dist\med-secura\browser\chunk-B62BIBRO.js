import{A as r,Dc as n,Ec as p,Ic as a,x as o}from"./chunk-YV65XDJO.js";var s=class i{db=r(a);storage=r(n);firebaseDataService=r(p);findBookingByDoctorId(){}findBookingByPatientId(){}doctorAppoitments(t){return this.firebaseDataService.getAppointmentsByDoctorId(t)}viewUserById(t){return this.firebaseDataService.getUsers().find(e=>e.id==t)}patientAppointments(t,e){return this.firebaseDataService.getAppointmentsByPatientId(t,e)}appointmentStatus(t,e){this.firebaseDataService.updateAppointment(t,{status:e}).subscribe({next:()=>console.log("Appointment status updated"),error:m=>console.error("Error updating appointment status:",m)})}static \u0275fac=function(e){return new(e||i)};static \u0275prov=o({token:i,factory:i.\u0275fac,providedIn:"root"})};export{s as a};
