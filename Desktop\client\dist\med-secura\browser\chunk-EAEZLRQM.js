import{a as mn,c as gn,d as un,f as Vt,g as fn,h as bt,i as vn,j as xt,k as Cn,l as Pn}from"./chunk-VFZ3QRB6.js";import{a as Mn}from"./chunk-B62BIBRO.js";import{$ as ce,$b as pt,A as G,Ac as ut,Bc as Ce,Ca as tn,Cb as rn,Cc as ft,D as M,Da as tt,Dc as ht,E as y,F as Ie,Fb as an,Fc as Ue,G as He,Ga as nt,Ha as it,I as Rt,Ia as Ee,Ib as sn,Ic as ae,J as be,Ja as U,Jb as ln,Jc as _n,K as Ut,Kb as cn,Lb as jt,Lc as _t,M as ee,Mb as rt,Na as nn,Nb as pn,O as c,Oa as V,Ob as at,P,Pa as ue,Pb as Ke,Qb as dn,R as S,Ra as xe,Rb as st,S as x,Sb as lt,Tb as Ne,U as g,V as Ze,Vb as Lt,W as A,Wa as ie,Wb as ct,X as ke,Xb as _e,Yb as Bt,Z as te,Za as ot,Zb as W,_a as on,_b as fe,a as I,aa as pe,ab as j,ac as dt,b as F,ba as a,bb as $,ca as s,cc as qe,d as J,da as u,dc as ye,ea as Ae,ec as Oe,f as Se,fa as De,fc as Ge,g as Kt,ga as D,gc as ve,h as ne,ha as b,i as k,ia as f,ic as oe,j as w,jc as Y,k as C,ka as Yt,kc as re,l as E,la as Xt,lc as Fe,m as $e,ma as Qt,n as qt,na as Je,nc as hn,o as v,oa as p,oc as ze,pa as O,pc as Re,q as Gt,qa as T,ra as We,rc as bn,s as Xe,sa as de,sc as mt,t as B,ta as me,tc as gt,u as Qe,ua as ge,v as Z,va as et,wa as Te,x as X,ya as Jt,yc as xn,z,za as en}from"./chunk-YV65XDJO.js";var vt=class r{constructor(e){this.router=e}touchStartX=0;touchEndX=0;minSwipeDistance=50;navigationTimer;ngOnInit(){this.navigationTimer=setTimeout(()=>{this.navigateToInfo()},4e3)}ngOnDestroy(){this.navigationTimer&&clearTimeout(this.navigationTimer)}onTouchStart(e){this.touchStartX=e.touches[0].clientX}onTouchEnd(e){this.touchEndX=e.changedTouches[0].clientX,this.handleSwipe()}onClick(e){let t=window.innerWidth;e.clientX>t/2&&this.navigateToInfo()}handleSwipe(){let e=this.touchEndX-this.touchStartX;Math.abs(e)>=this.minSwipeDistance&&e<0&&this.navigateToInfo()}navigateToInfo(){this.navigationTimer&&clearTimeout(this.navigationTimer),this.router.navigate(["/mobile/info"])}static \u0275fac=function(t){return new(t||r)(P(j))};static \u0275cmp=S({type:r,selectors:[["app-splash-page"]],hostBindings:function(t,n){t&1&&b("touchstart",function(o){return n.onTouchStart(o)})("touchend",function(o){return n.onTouchEnd(o)})("click",function(o){return n.onClick(o)})},decls:6,vars:0,consts:[[1,"splash-screen",3,"click"],["src","images/logo.png","alt","App logo"],[1,"app-name"],[2,"font-weight","bold"]],template:function(t,n){t&1&&(a(0,"div",0),b("click",function(){return n.navigateToInfo()}),u(1,"img",1),a(2,"p",2)(3,"span",3),p(4,"Med"),s(),p(5,"Secura"),s()())},styles:[".splash-screen[_ngcontent-%COMP%]{position:relative;background-color:#28b2a7;display:flex;justify-content:center;align-items:center;height:100vh;width:100vw;flex-direction:column}img[_ngcontent-%COMP%]{max-width:80%;max-height:80%}.app-name[_ngcontent-%COMP%]{position:absolute;top:60%;justify-content:center;margin-top:.2rem;font-size:2rem;color:#fff}"]})};var je=class r{infoImg;heading;description;nextPage=new Rt;previousPage=new Rt;touchStartX=0;touchEndX=0;minSwipeDistance=50;onTouchStart(e){this.touchStartX=e.touches[0].clientX}onTouchEnd(e){this.touchEndX=e.changedTouches[0].clientX,this.handleSwipe()}onClick(e){let t=window.innerWidth;e.clientX>t/2?this.nextPage.emit():this.previousPage.emit()}handleSwipe(){let e=this.touchEndX-this.touchStartX;Math.abs(e)>=this.minSwipeDistance&&(e>0?this.previousPage.emit():this.nextPage.emit())}static \u0275fac=function(t){return new(t||r)};static \u0275cmp=S({type:r,selectors:[["app-info-slider"]],hostBindings:function(t,n){t&1&&b("touchstart",function(o){return n.onTouchStart(o)})("touchend",function(o){return n.onTouchEnd(o)})("click",function(o){return n.onClick(o)})},inputs:{infoImg:"infoImg",heading:"heading",description:"description"},outputs:{nextPage:"nextPage",previousPage:"previousPage"},decls:9,vars:3,consts:[["role","region","aria-label","Information slider",1,"info-slider"],[1,"circle"],["alt","","draggable","false",3,"src"],[1,"heading"],[1,"description"]],template:function(t,n){t&1&&(a(0,"div",0)(1,"div",1),u(2,"img",2),s(),a(3,"div",3)(4,"h2"),p(5),s()(),a(6,"div",4)(7,"p"),p(8),s()()()),t&2&&(c(2),g("src",n.infoImg,ee),c(3),O(n.heading),c(3),O(n.description))},styles:[".info-slider[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center;margin-top:40px;padding-inline:20px;touch-action:pan-y pinch-zoom;-webkit-user-select:none;user-select:none;width:100%;min-height:calc(100vh - 240px);position:relative;cursor:pointer}.circle[_ngcontent-%COMP%]{display:flex;padding:4rem;margin-top:clamp(20px,5vh,60px);align-items:center;justify-content:center;width:16rem;height:16rem;background:#199a8e0a;border-radius:50%;margin-bottom:20px}.circle[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{height:100%;object-fit:fill;object-position:left;pointer-events:none;-webkit-user-drag:none}.heading[_ngcontent-%COMP%]{color:#199a8e;font-size:20px;margin-bottom:40px;font-weight:700;user-select:none;-webkit-user-select:none}.description[_ngcontent-%COMP%] > p[_ngcontent-%COMP%]{font-size:20px;font-family:Times New Roman,Times,serif;user-select:none;-webkit-user-select:none}@media screen and (max-height: 600px){.info-slider[_ngcontent-%COMP%]{min-height:450px}.circle[_ngcontent-%COMP%]{margin-top:20px;padding:2rem;width:12rem;height:12rem}}"]})};var $t=r=>({clicked:r});function An(r,e){r&1&&(a(0,"h1",11),p(1,"Skip"),s())}function Dn(r,e){if(r&1){let t=D();a(0,"app-info-slider",12),b("indexSwitch",function(){M(t);let i=f();return y(i.switch(0))}),s()}}function Tn(r,e){if(r&1){let t=D();a(0,"app-info-slider",13),b("indexSwitch",function(){M(t);let i=f();return y(i.switch(1))}),s()}}function En(r,e){if(r&1){let t=D();a(0,"app-info-slider",14),b("indexSwitch",function(){M(t);let i=f();return y(i.switch(2))}),s()}}function Nn(r,e){r&1&&(a(0,"div",15)(1,"button",16),p(2,"Get Started"),s()())}var Ct=class r{constructor(e){this.router=e}color;description="";index=0;touchStartX=0;touchEndX=0;minSwipeDistance=50;totalSlides=3;isGetStartedScreen=!1;onTouchStart(e){this.touchStartX=e.touches[0].clientX}onTouchEnd(e){this.touchEndX=e.changedTouches[0].clientX,this.handleSwipe()}onClick(e){let t=window.innerWidth,n=e.clientX;n>t/2&&!this.isGetStartedScreen?this.nextSlide():n<=t/2&&this.previousSlide()}handleSwipe(){let e=this.touchEndX-this.touchStartX;Math.abs(e)>=this.minSwipeDistance&&(e>0?this.previousSlide():this.isGetStartedScreen||this.nextSlide())}nextSlide(){this.index<this.totalSlides-1?this.switch(this.index+1):this.router.navigate(["/mobile/login"])}previousSlide(){this.index>0&&this.switch(this.index-1)}switch(e){e>=0&&e<this.totalSlides&&(this.index=e,this.isGetStartedScreen=e===this.totalSlides-1)}static \u0275fac=function(t){return new(t||r)(P(j))};static \u0275cmp=S({type:r,selectors:[["app-info-page"]],hostBindings:function(t,n){t&1&&b("touchstart",function(o){return n.onTouchStart(o)})("touchend",function(o){return n.onTouchEnd(o)})("click",function(o){return n.onClick(o)})},inputs:{color:"color",description:"description"},decls:11,vars:14,consts:[[1,"info-page"],[1,"top"],["class","skip","routerLink","/mobile/login",4,"ngIf"],["infoImg","images/1s.png","heading","From Physical To Digital ","description","Digital file management has halved the 10+ hours doctors once spent on paperwork, freeing up more time for patient care!","color","#199A8E",3,"indexSwitch",4,"ngIf"],["infoImg","images/2s.png","heading","Secure File Management","description","Secure medical apps safeguard patient data with encryption, access controls, and multi-factor authentication.",3,"indexSwitch",4,"ngIf"],["infoImg","images/3s.png","heading","Schedule your appointments","description","Our App makes it simple to book appointments at your convenience",3,"indexSwitch",4,"ngIf"],["class","btn",4,"ngIf"],[1,"current-index"],[1,"index","1st",3,"click","ngClass"],[1,"index","2st",3,"click","ngClass"],[1,"index","3st",3,"click","ngClass"],["routerLink","/mobile/login",1,"skip"],["infoImg","images/1s.png","heading","From Physical To Digital ","description","Digital file management has halved the 10+ hours doctors once spent on paperwork, freeing up more time for patient care!","color","#199A8E",3,"indexSwitch"],["infoImg","images/2s.png","heading","Secure File Management","description","Secure medical apps safeguard patient data with encryption, access controls, and multi-factor authentication.",3,"indexSwitch"],["infoImg","images/3s.png","heading","Schedule your appointments","description","Our App makes it simple to book appointments at your convenience",3,"indexSwitch"],[1,"btn"],["routerLink","/mobile/login"]],template:function(t,n){t&1&&(a(0,"div",0)(1,"div",1),x(2,An,2,0,"h1",2)(3,Dn,1,0,"app-info-slider",3)(4,Tn,1,0,"app-info-slider",4)(5,En,1,0,"app-info-slider",5)(6,Nn,3,0,"div",6),s(),a(7,"div",7)(8,"div",8),b("click",function(){return n.switch(0)}),s(),a(9,"div",9),b("click",function(){return n.switch(1)}),s(),a(10,"div",10),b("click",function(){return n.switch(2)}),s()()()),t&2&&(c(2),g("ngIf",n.index==0||n.index==1),c(),g("ngIf",n.index==0),c(),g("ngIf",n.index==1),c(),g("ngIf",n.index==2),c(),g("ngIf",n.index==2),c(2),g("ngClass",Te(8,$t,n.index==0)),c(),g("ngClass",Te(10,$t,n.index==1)),c(),g("ngClass",Te(12,$t,n.index==2)))},dependencies:[je,U,it,$],styles:[".info-page[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;min-height:100vh;padding:clamp(.5rem,3vw,1.5rem);box-sizing:border-box}.top[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;width:100%;max-width:1200px;margin:0 auto}.current-index[_ngcontent-%COMP%]{width:clamp(60px,15vw,80px);height:auto;display:flex;justify-content:space-between;gap:clamp(.3rem,2vw,.6rem);margin:clamp(10px,3vh,20px) auto}.index[_ngcontent-%COMP%]{background:#bbb5b5;width:clamp(12px,3vw,17px);height:clamp(12px,3vw,17px);border-radius:50%}.clicked[_ngcontent-%COMP%]{background:#199a8e}.skip[_ngcontent-%COMP%]{font-size:clamp(14px,4vw,17px);text-align:end;cursor:pointer;padding:.5rem}.btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;position:fixed;bottom:clamp(20px,10vh,90px);left:50%;transform:translate(-50%);width:100%;padding:0 1rem}.btn[_ngcontent-%COMP%] > button[_ngcontent-%COMP%]{background:#199a8e;width:100%;max-width:400px;padding:clamp(.8rem,2vh,1.2rem);border:none;border-radius:8px;color:#fff;font-size:clamp(14px,3vw,16px)}@media screen and (max-height: 500px){.info-page[_ngcontent-%COMP%]{padding:.5rem}.btn[_ngcontent-%COMP%]{position:relative;bottom:0;margin:1rem 0}}app-info-slider[_ngcontent-%COMP%]{min-height:auto}@media screen and (min-width: 768px){.info-page[_ngcontent-%COMP%]{padding:1.5rem}.skip[_ngcontent-%COMP%]{font-size:16px}}"]})};var Pt=class r{static \u0275fac=function(t){return new(t||r)};static \u0275cmp=S({type:r,selectors:[["app-upcoming-schedule"]],decls:2,vars:0,template:function(t,n){t&1&&(a(0,"p"),p(1,"upcoming-schedule works!"),s())},encapsulation:2})};function Fn(r,e){r&1&&(a(0,"span",10),p(1,"Home"),s())}function zn(r,e){r&1&&(a(0,"span",10),p(1,"Appointments"),s())}function Rn(r,e){r&1&&(a(0,"span",10),p(1,"Schedule"),s())}function Un(r,e){r&1&&(a(0,"span",10),p(1,"Profile"),s())}var H=class r{constructor(e){this.router=e;this.router.events.subscribe(t=>{t instanceof ot&&this.setActiveTabFromRoute(t.url)})}activeTab="";setActiveTab(e){this.activeTab=e}setActiveTabFromRoute(e){e.includes("/patient-dashboard")?this.activeTab="home":e.includes("/schedule")?this.activeTab="appointments":e.includes("/appointment-booking")?this.activeTab="calendar":e.includes("/patient-profile")?this.activeTab="profile":e.includes("/patient-update-profile")&&(this.activeTab="profile")}static \u0275fac=function(t){return new(t||r)(P(j))};static \u0275cmp=S({type:r,selectors:[["app-navbar"]],decls:13,vars:12,consts:[[1,"bottom-nav"],["routerLink","/mobile/patient-dashboard",1,"nav-button",3,"click"],[1,"bi","bi-house-door-fill"],["class","nav-label",4,"ngIf"],["routerLink","/mobile/schedule",1,"nav-button",3,"click"],[1,"bi","bi-alarm"],["routerLink","/mobile/appointment-booking",1,"nav-button",3,"click"],[1,"bi","bi-calendar-check"],["routerLink","/mobile/patient-profile",1,"nav-button",3,"click"],[1,"bi","bi-person"],[1,"nav-label"]],template:function(t,n){t&1&&(a(0,"nav",0)(1,"button",1),b("click",function(){return n.setActiveTab("home")}),u(2,"i",2),x(3,Fn,2,0,"span",3),s(),a(4,"button",4),b("click",function(){return n.setActiveTab("appointments")}),u(5,"i",5),x(6,zn,2,0,"span",3),s(),a(7,"button",6),b("click",function(){return n.setActiveTab("calendar")}),u(8,"i",7),x(9,Rn,2,0,"span",3),s(),a(10,"button",8),b("click",function(){return n.setActiveTab("profile")}),u(11,"i",9),x(12,Un,2,0,"span",3),s()()),t&2&&(c(),A("active",n.activeTab==="home"),c(2),g("ngIf",n.activeTab==="home"),c(),A("active",n.activeTab==="appointments"),c(2),g("ngIf",n.activeTab==="appointments"),c(),A("active",n.activeTab==="calendar"),c(2),g("ngIf",n.activeTab==="calendar"),c(),A("active",n.activeTab==="profile"),c(2),g("ngIf",n.activeTab==="profile"))},dependencies:[U,$],styles:[".bottom-nav[_ngcontent-%COMP%]{position:fixed;bottom:0;left:0;width:100%;display:flex;justify-content:space-around;padding:22px;background-color:#fff!important;-webkit-backdrop-filter:none!important;backdrop-filter:none!important;box-shadow:0 -2px 5px #0000001a;z-index:9999}.nav-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background:none;border:none;color:silver;font-size:12px;cursor:pointer;padding:8px 16px;border-radius:20px}.nav-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:20px}.nav-button.active[_ngcontent-%COMP%]{color:#00a693;background-color:#e6f7f5}.nav-label[_ngcontent-%COMP%]{font-size:14px}.nav-button[_ngcontent-%COMP%]:not(.active)   .nav-label[_ngcontent-%COMP%]{display:none}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{transform:translateY(100%)}to{transform:translateY(0)}}@media (max-width: 375px){.profile-container[_ngcontent-%COMP%]{padding:15px}.nav-button[_ngcontent-%COMP%]{padding:6px 12px}}"]})};var Q=class r{constructor(e,t){this.location=e;this.router=t;this.router.events.subscribe(n=>{n instanceof ot&&this.navigationHistory.push(n.urlAfterRedirects)})}navigationHistory=[];goBack(){if(this.navigationHistory.length>1){this.navigationHistory.pop();let e=this.navigationHistory[this.navigationHistory.length-1];this.router.navigateByUrl(e)}else this.router.navigate(["/mobile/dashboard"])}static \u0275fac=function(t){return new(t||r)(P(nt),P(j))};static \u0275cmp=S({type:r,selectors:[["app-back-button"]],decls:2,vars:0,consts:[[1,"back-button",3,"click"],[1,"bi","bi-chevron-left"]],template:function(t,n){t&1&&(a(0,"button",0),b("click",function(){return n.goBack()}),u(1,"i",1),s())},styles:[".back-button[_ngcontent-%COMP%]{background:none;border:none;font-size:25px;color:#199a8e;display:flex;align-items:center;cursor:pointer;margin-top:24px;margin-left:15px}.back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:5px}"]})};var Le=class r{baseUrl;constructor(){this.baseUrl="/api",console.log("API Base URL:",this.baseUrl)}getUrl(e){let t=e.startsWith("/")?e.substring(1):e;return`${this.baseUrl}/${t}`}getBaseUrl(){return this.baseUrl}static \u0275fac=function(t){return new(t||r)};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};var Pe=class r{constructor(){}setItem(e,t){try{let n=JSON.stringify(t);localStorage.setItem(e,n)}catch(n){console.error(`Error saving to localStorage: ${e}`,n)}}getItem(e){try{let t=localStorage.getItem(e);if(t===null)return null;try{return JSON.parse(t)}catch(n){return e==="med_secure_patient_id"||e==="patient_id"||e==="patient_user_id"?(console.log(`Returning raw string value for ${e}:`,t),t):(console.error(`Error parsing JSON from localStorage for ${e}:`,n),null)}}catch(t){return console.error(`Error retrieving from localStorage: ${e}`,t),null}}removeItem(e){try{localStorage.removeItem(e)}catch(t){console.error(`Error removing from localStorage: ${e}`,t)}}clear(){try{localStorage.clear()}catch(e){console.error("Error clearing localStorage",e)}}static \u0275fac=function(t){return new(t||r)};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};var q=class r{constructor(e,t,n,i,o,l,d,m){this.http=e;this.apiUrlService=t;this.auth=n;this.firestore=i;this.storage=o;this.ngZone=l;this.db=d;this.authErrorMapper=m;this.baseUrl=this.apiUrlService.getBaseUrl(),console.log("Auth Service initialized with base URL:",this.baseUrl),this.auth.onAuthStateChanged(h=>{h?console.log("Firebase auth state changed - user signed in:",h.uid):(this.currentUserSubject.next(null),console.log("Firebase auth state changed - user signed out"))})}tokenKey="med_secure_token";userInfoKey="user_info";baseUrl;currentUserSubject=new ne(null);currentUser$=this.currentUserSubject.asObservable();userIdKey="patient_user_id";patientIdKey="med_secure_patient_id";legacyPatientIdKey="patient_id";handleFirebaseUser(e){console.log("Handling Firebase user:",e),this.ngZone.runOutsideAngular(()=>{this.fetchUserDataFromFirestore(e.uid).then(t=>{this.ngZone.run(()=>{if(t){let n={id:e.uid,patientId:t.patientId||e.uid,full_name:e.displayName||t.fullName||`${t.firstName||""} ${t.lastName||""}`.trim(),email:e.email||t.email||"",profilePicture:e.photoURL||t.profilePicture||null,phoneNumber:t.phoneNumber||"",address:t.address||"",role:t.role||"patient"};if(this.storage.setItem(this.userInfoKey,n),this.currentUserSubject.next({displayName:n.full_name,profileImage:n.profilePicture!==void 0?n.profilePicture:null,email:n.email,id:n.id||"",patientId:n.patientId}),(n.role?.toUpperCase()==="PATIENT"||!n.role)&&n.id){console.log("Setting current patient in DB from handleFirebaseUser:",n);let i={id:n.id,email:n.email,firstname:n.full_name.split(" ")[0]||"",lastname:n.full_name.split(" ").slice(1).join(" ")||"",role:"PATIENT",password:""};this.db.setCurrentPatient(i),this.storage.setItem(Ue,i),console.log("Current patient set successfully in handleFirebaseUser")}console.log("Firebase user data processed successfully")}else console.warn("No Firestore data found for Firebase user")})}).catch(t=>{this.ngZone.run(()=>{console.error("Error fetching user data from Firestore:",t)})})})}fetchUserDataFromFirestore(e){return J(this,null,function*(){try{return yield this.ngZone.runOutsideAngular(()=>J(this,null,function*(){let t=W(this.firestore,"patients",e),n=yield fe(t);if(n.exists())return F(I({},n.data()),{patientId:n.id});let i=W(this.firestore,"users",e),o=yield fe(i);return o.exists()?o.data():(console.warn("No user data found in Firestore for ID:",e),null)}))}catch(t){return console.error("Error fetching user data from Firestore:",t),null}})}login(e,t){return console.log("Attempting login with Firebase for:",e),new Se(n=>{this.ngZone.runOutsideAngular(()=>{sn(this.auth,e,t).then(i=>{console.log("Firebase login successful:",i);let o=i.user;this.fetchUserDataFromFirestore(o.uid).then(l=>{this.ngZone.run(()=>{let d=l?.profilePicture||null,m={message:"Login successful",token:i.user.refreshToken,name:l?.fullName||`${l?.firstName||""} ${l?.lastName||""}`.trim()||o.displayName||e,email:o.email||"",id:o.uid,role:l?.role?.toLowerCase()||"patient",success:!0,firstName:l?.firstName||"",lastName:l?.lastName||"",profilePicture:d},h={id:o.uid,email:o.email||"",full_name:m.name,role:m.role,profilePicture:d,firstName:m.firstName,lastName:m.lastName};if(this.storage.setItem(this.userInfoKey,h),(m.role?.toUpperCase()==="PATIENT"||!m.role)&&m.id){console.log("Setting current patient in DB after login:",m);let _={id:m.id,email:m.email,firstname:m.firstName||m.name?.split(" ")[0]||"",lastname:m.lastName||(m.name?.split(" ").length>1?m.name?.split(" ").slice(1).join(" "):""),role:"PATIENT",password:""};this.db.setCurrentPatient(_),this.storage.setItem(Ue,_),console.log("Current patient set in DB after login:",_)}n.next(m),n.complete()})}).catch(l=>{this.ngZone.run(()=>{console.error("Error fetching user data from Firestore after login:",l),n.error("Login failed: Error fetching user data")})})}).catch(i=>{this.ngZone.run(()=>{console.error("Firebase login error:",i);let o=this.authErrorMapper.mapFirebaseError(i);n.error(o)})})})})}legacyLogin(e,t){let n=this.storage.getItem(this.userInfoKey);if(n&&n.email===e){console.log("Found matching user info in local storage, attempting local login");let l=this.loginWithLocalDb(e,t);if(l)return l}let i={email:e,password:t},o=this.apiUrlService.getUrl("auth/login");return console.log("Logging in at endpoint:",o),this.http.post(o,i).pipe(Z(l=>{if(console.log("Login successful:",l),l.token?(this.storage.setItem(this.tokenKey,l.token),console.log("Token saved to localStorage")):console.warn("No token received in login response"),l.id?(this.storage.setItem(this.userIdKey,l.id),console.log("User ID saved:",l.id)):l.userId&&(this.storage.setItem(this.userIdKey,l.userId),console.log("User ID saved from userId:",l.userId)),l.patientId)this.storage.setItem(this.patientIdKey,l.patientId),console.log("Patient ID saved:",l.patientId);else if(l.role==="PATIENT"&&(l.id||l.userId)){let d=l.id||l.userId||"";d&&(this.storage.setItem(this.patientIdKey,d),console.log("Patient ID saved from userId:",d))}this.fetchLatestUserProfile(l)}),v(l=>(console.error("Login error:",l),l.status===0||l.status===500?(console.log("Network error or server error, trying local database login"),this.loginWithLocalDb(e,t)):l.error instanceof ErrorEvent?C(()=>new Error("Network error occurred")):C(()=>l.error?.message||"Login failed"))))}fetchLatestUserProfile(e){let t=this.storage.getItem(this.tokenKey);if(!t){console.warn("No auth token available for profile fetch"),this.handleFallbackUserInfo(e);return}let n=new ue({Authorization:`Bearer ${t}`});console.log("Fetching profile with token:",t);let i=this.apiUrlService.getUrl("patients/profile/info");console.log("Request URL:",i),this.http.get(i,{headers:n}).pipe(v(o=>(console.error("Error fetching latest profile:",o),o.status===404?console.warn("Patient profile endpoint not found (404). Using fallback user info."):o.status===0?console.warn("Network error when fetching profile. Backend may be unavailable."):console.error(`Error ${o.status} when fetching profile: ${o.message}`),this.handleFallbackUserInfo(e),C(()=>o)))).subscribe({next:o=>{if(console.log("Profile response:",o),o.success&&o.data){let l=o.data.id||this.storage.getItem(this.userIdKey)||void 0,d=o.data.patientId||this.storage.getItem(this.patientIdKey)||void 0;l&&this.storage.setItem(this.userIdKey,l),d&&this.storage.setItem(this.patientIdKey,d);let m={id:l,patientId:d,full_name:o.data.fullName,email:o.data.email,profilePicture:o.data.profilePicture||null,phoneNumber:o.data.phoneNumber,address:e.address,role:o.data.role};if(this.storage.setItem(this.userInfoKey,m),this.currentUserSubject.next({displayName:o.data.fullName,profileImage:o.data.profilePicture,email:o.data.email,id:l,patientId:d}),(o.data.role?.toUpperCase()==="PATIENT"||!o.data.role)&&l){console.log("Setting current patient in DB from profile fetch:",o.data);let h={id:l,email:o.data.email,firstname:o.data.fullName.split(" ")[0]||"",lastname:o.data.fullName.split(" ").slice(1).join(" ")||"",role:"PATIENT",password:""};this.db.setCurrentPatient(h),this.storage.setItem(Ue,h),console.log("Current patient set from profile fetch:",h)}}else this.handleFallbackUserInfo(e)},error:o=>{console.error("Error in profile subscription:",o),this.handleFallbackUserInfo(e)}})}handleFallbackUserInfo(e){console.log("Using fallback user info from login response");let t=e.id||e.userId||this.storage.getItem(this.userIdKey)||void 0,n=e.patientId||this.storage.getItem(this.patientIdKey)||void 0;t&&this.storage.setItem(this.userIdKey,t),n&&this.storage.setItem(this.patientIdKey,n),e.role==="PATIENT"&&t&&!n&&(this.storage.setItem(this.patientIdKey,t),console.log("Set patient ID from user ID:",t));let i={id:t,patientId:n||(e.role==="PATIENT"?t:void 0),full_name:e.name||`${e.firstName||""} ${e.lastName||""}`.trim(),email:e.email,profilePicture:e.profilePicture||null,phoneNumber:e.phoneNumber,address:e.address,role:e.role};if(this.storage.setItem(this.userInfoKey,i),this.currentUserSubject.next({displayName:i.full_name,profileImage:i.profilePicture!==void 0?i.profilePicture:null,email:i.email,id:i.id||"",patientId:i.patientId}),(i.role?.toUpperCase()==="PATIENT"||!i.role)&&i.id){console.log("Setting current patient in DB from fallback user info:",i);let o={id:i.id,email:i.email,firstname:i.full_name.split(" ")[0]||"",lastname:i.full_name.split(" ").slice(1).join(" ")||"",role:"PATIENT",password:""};this.db.setCurrentPatient(o),this.storage.setItem(Ue,o),console.log("Current patient set from fallback user info:",o)}}getCurrentUser(){return J(this,null,function*(){let e=this.currentUserSubject.getValue();if(e)return e;let t=this.auth.currentUser;if(t)try{let i=yield this.fetchUserDataFromFirestore(t.uid),o={id:t.uid,email:t.email||"",full_name:i?.fullName||t.displayName||"",profilePicture:i?.profilePicture||null,patientId:i?.patientId||t.uid,role:i?.role||"patient"},l={id:o.id||"",email:o.email,displayName:o.full_name,profileImage:o.profilePicture!==void 0?o.profilePicture:null,patientId:o.patientId};return this.storage.setItem(this.userInfoKey,o),this.currentUserSubject.next(l),l}catch(i){console.error("Error fetching user data from Firestore:",i)}let n=this.getUserInfo();return n?{id:n.id||"",email:n.email,displayName:n.full_name,profileImage:n.profilePicture!==void 0?n.profilePicture:null,patientId:n.patientId}:null})}getUserInfo(){let e=this.storage.getItem(this.userInfoKey);if(e)return e;let t=this.currentUserSubject.getValue();return t?{id:t.id||"",patientId:t.patientId,full_name:t.displayName,email:t.email,profilePicture:t.profileImage,role:"patient"}:null}getCurrentUserInfo(){let e=this.currentUserSubject.value;return e?{id:e.id||"",email:e.email,full_name:e.displayName,profilePicture:e.profileImage,patientId:e.patientId,role:"patient"}:this.storage.getItem(this.userInfoKey)}getUserId(){return this.storage.getItem(this.userIdKey)||this.getCurrentUserInfo()?.id||null}getPatientId(){return this.storage.getItem(this.patientIdKey)||this.storage.getItem(this.legacyPatientIdKey)||this.getCurrentUserInfo()?.patientId||null}saveUserInfo(e){console.log("Saving user info:",e),e.id&&this.storage.setItem(this.userIdKey,e.id),e.patientId&&this.storage.setItem(this.patientIdKey,e.patientId),this.storage.setItem(this.userInfoKey,e),this.currentUserSubject.next({displayName:e.full_name,profileImage:e.profilePicture!==void 0?e.profilePicture:null,email:e.email,id:e.id||"",patientId:e.patientId})}clearUserInfo(){this.storage.removeItem(this.userInfoKey),this.storage.removeItem(this.tokenKey),this.storage.removeItem(this.userIdKey),this.storage.removeItem(this.patientIdKey),this.storage.removeItem(this.legacyPatientIdKey),this.currentUserSubject.next(null)}register(e){return console.log("Registering with Firebase:",e.email),k(an(this.auth,e.email,e.password)).pipe(B(t=>{console.log("Firebase registration successful:",t);let n=t.user,i=`${e.firstName} ${e.lastName}`;return k(cn(n,{displayName:i})).pipe(B(()=>{let o={firstName:e.firstName,lastName:e.lastName,email:e.email,role:e.role.toUpperCase(),createdAt:new Date().toISOString(),profilePicture:null,phoneNumber:"",address:""},l=W(this.firestore,"users",n.uid),d=Promise.resolve(null),m=n.uid;if(e.role.toUpperCase()==="PATIENT"){let h={userId:n.uid,firstName:e.firstName,lastName:e.lastName,email:e.email,fullName:i,createdAt:new Date().toISOString(),medicalRecords:[]},_=W(this.firestore,"patients",n.uid);d=Oe(_,h).then(()=>null)}return k(Promise.all([Oe(l,o),d])).pipe(E(()=>{let h={message:"Registration successful",email:e.email,id:n.uid,userId:n.uid,role:e.role.toUpperCase(),name:i,token:n.refreshToken};e.role.toUpperCase()==="PATIENT"&&(h.patientId=m);let _={id:n.uid,patientId:h.patientId,full_name:i,email:e.email,profilePicture:null};if(this.storage.setItem(this.userInfoKey,_),this.currentUserSubject.next({displayName:_.full_name,profileImage:null,email:_.email,id:_.id||"",patientId:_.patientId}),this.storage.setItem(this.tokenKey,n.refreshToken),this.storage.setItem(this.userIdKey,n.uid),h.patientId&&(this.storage.setItem(this.patientIdKey,h.patientId),this.storage.setItem(this.legacyPatientIdKey,h.patientId)),e.role.toUpperCase()==="PATIENT"&&h.id){console.log("Setting current patient in DB after registration:",h);let N={id:h.id,email:h.email,firstname:e.firstName,lastname:e.lastName,role:"PATIENT",password:""};this.db.setCurrentPatient(N),this.storage.setItem(Ue,N),console.log("Current patient set in DB after registration:",N)}return h}))}))}),v(t=>{if(console.error("Firebase registration error:",t),t.code==="auth/network-request-failed")return console.log("Firebase network error, trying legacy registration"),this.legacyRegister(e);let n=this.authErrorMapper.mapRegistrationError(t);return C(()=>({error:{message:n}}))}))}legacyRegister(e){let t=this.apiUrlService.getUrl("auth/register");return console.log("Registering at endpoint:",t),this.http.post(t,e).pipe(Z(n=>{console.log("Registration response:",n),n.token&&this.storage.setItem(this.tokenKey,n.token);let i=n.userId||n.id;i&&(this.storage.setItem(this.userIdKey,i),console.log("User ID saved:",i)),n.role==="PATIENT"&&(i?(this.storage.setItem(this.patientIdKey,i),console.log("Patient ID saved (from userId):",i)):n.patientId&&(this.storage.setItem(this.patientIdKey,n.patientId),console.log("Patient ID saved (from patientId):",n.patientId)));let o={id:i,patientId:n.role==="PATIENT"?n.patientId||i:void 0,full_name:`${e.firstName} ${e.lastName}`,email:n.email||e.email,profilePicture:null};this.storage.setItem(this.userInfoKey,o),this.currentUserSubject.next({displayName:o.full_name,profileImage:null,email:o.email,id:o.id||"",patientId:o.patientId})}),v(n=>(console.error("Registration error:",n),n.status===0?(console.log("Network error or server unavailable, trying local database registration"),this.registerWithLocalDb(e)):this.handleError(n))))}logout(){return console.log("Logging out user"),this.clearUserInfo(),k(ln(this.auth)).pipe(Z(()=>{console.log("Firebase logout successful")}),v(e=>(console.error("Firebase logout error:",e),w(void 0))))}updateProfile(e){let n=this.getCurrentUserInfo()?.patientId!==void 0,i=n?this.apiUrlService.getUrl("patients/profile/update"):this.apiUrlService.getUrl("doctors/profile/update");console.log(`Updating profile for ${n?"patient":"doctor"} at endpoint: ${i}`);let o=this.getAuthToken();if(!o)return C(()=>new Error("Authentication token not found"));let l=new ue({Authorization:`Bearer ${o}`});return n?this.http.post(i,e,{headers:l}).pipe(Z(d=>{console.log("Profile update response:",d),d.user&&this.updateUserInfoFromProfile(d.user)}),v(d=>(console.error("Profile update error:",d),d.error instanceof ErrorEvent?C(()=>new Error("Network error occurred")):C(()=>d.error?.message||"Profile update failed")))):this.http.post(i,e).pipe(Z(d=>{console.log("Profile update response:",d),d.user&&this.updateUserInfoFromProfile(d.user)}),v(d=>(console.error("Profile update error:",d),d.error instanceof ErrorEvent?C(()=>new Error("Network error occurred")):C(()=>d.error?.message||"Profile update failed"))))}updateUserInfoFromProfile(e){let t=this.getCurrentUserInfo();if(t){let n,i;if(e.fullName){let l=e.fullName.split(" ");n=l[0],i=l.length>1?l.slice(1).join(" "):""}else n=e.firstName||t.full_name.split(" ")[0],i=e.lastName||(t.full_name.split(" ").length>1?t.full_name.split(" ").slice(1).join(" "):"");let o=F(I({},t),{full_name:`${n} ${i}`,profilePicture:e.profilePicture||t.profilePicture,phoneNumber:e.phoneNumber||t.phoneNumber,address:e.address||t.address});this.storage.setItem(this.userInfoKey,o),this.currentUserSubject.next({displayName:o.full_name,profileImage:o.profilePicture!==void 0?o.profilePicture:null,email:o.email,id:o.id||"",patientId:o.patientId}),console.log("User info updated from profile:",o)}}changePassword(e,t){return this.http.post(`${this.baseUrl}/change-password`,{currentPassword:e,newPassword:t}).pipe(v(n=>n.error instanceof ErrorEvent?C(()=>new Error("Network error occurred")):C(()=>n.error.message||"Password change failed")))}verifyCurrentPassword(e){return this.http.post(`${this.baseUrl}/verify-password`,{password:e}).pipe(v(t=>t.error instanceof ErrorEvent?C(()=>new Error("Network error occurred")):C(()=>t.error.message||"Password verification failed")))}isLoggedIn(){return this.currentUserSubject.value!==null}syncUserDataWithBackend(e,t,n,i,o){let l=this.getAuthToken();if(!l){console.error("Cannot sync user data: No auth token. Please log in again.");return}let d=new ue({Authorization:`Bearer ${l}`,"Content-Type":"application/json"}),m={firstName:e,lastName:t,email:n,phoneNumber:i,profilePicture:o};console.log("Syncing user data with backend:",m);let h=this.apiUrlService.getUrl("patients/profile/update");console.log("Sync endpoint:",h),this.http.post(h,m,{headers:d}).pipe(Z(_=>console.log("Backend sync response:",_)),v(_=>(console.error("Failed to sync user data with backend",_),w(null)))).subscribe()}getAuthToken(){let e=this.storage.getItem(this.tokenKey);return console.log("Getting auth token:",e?"Token exists":"No token found"),e}createPatientRecord(e,t,n,i){console.log("Creating patient record for user ID:",e);let o={userId:e,firstName:t,lastName:n,email:i},l=this.apiUrlService.getUrl("patients/create");return console.log("Create patient endpoint:",l),this.http.post(l,o).pipe(v(d=>(console.error("Error creating patient record:",d),C(()=>d.error.message||"Failed to create patient record"))))}fetchPatientIdByUserId(e){console.log("Fetching patient ID for user ID:",e);let t=this.apiUrlService.getUrl(`patients/by-user/${e}`);return console.log("Fetch patient ID endpoint:",t),this.http.get(t).pipe(E(n=>n.patientId),v(n=>(console.error("Error fetching patient ID:",n),C(()=>n.error.message||"Failed to fetch patient ID"))))}handleError(e){return console.error("API Error:",e),e.error instanceof ErrorEvent||e.status===0?(console.error("Client-side error:",e.error?.message||"Network connection error"),C(()=>new Error("Unable to connect to the server. Please check your internet connection and try again."))):e.error&&typeof e.error=="object"?e.error.message?.toLowerCase().includes("email already exists")||e.error.error?.toLowerCase().includes("email already exists")?C(()=>({error:{message:"Email already exists"}})):C(()=>e):C(()=>new Error("Something went wrong. Please try again later."))}loginWithLocalDb(e,t){if(console.log("Attempting login with local database"),!this.db)return console.error("Db service not available for offline login"),C(()=>new Error("Offline functionality not available"));try{let n=this.db.login(e,t);if(n){console.log("Local database login successful:",n);let i={message:"Login successful",token:"med_secure_offline_token",name:`${n.firstname} ${n.lastname}`,email:n.email,id:n.id,userId:n.id,role:n.role,firstName:n.firstname,lastName:n.lastname,success:!0};if(n.role.toUpperCase()==="PATIENT"){let l=this.db.patientTable().find(d=>d.user_id===n.id);l?i.patientId=l.id:i.patientId=n.id}let o={id:n.id,patientId:i.patientId,full_name:i.name,email:n.email,profilePicture:null};return this.storage.setItem(this.userInfoKey,o),w(i)}else return console.log("Local database login failed"),C(()=>new Error("Invalid email or password"))}catch(n){return console.error("Error during local database login:",n),C(()=>new Error("Login failed. Please try again."))}}registerWithLocalDb(e){if(console.log("Attempting registration with local database"),!this.db)return console.error("Db service not available for offline registration"),C(()=>new Error("Offline functionality not available"));if(this.db.userTable().find(h=>h.email===e.email))return console.log("Email already exists in local database"),C(()=>({error:{message:"Email already exists"}}));let n=this.db.generateId(),i={id:n,email:e.email,password:e.password,firstname:e.firstName,lastname:e.lastName,role:e.role.toUpperCase()},o=[...this.db.userTable(),i];this.db.userTable.set(o),this.db.storage.setItem("USER_TABLE",o);let l=n;if(e.role.toUpperCase()==="PATIENT"){l=this.db.generateId();let h={id:l,user_id:n,image:"",contact:""},_=[...this.db.patientTable(),h];this.db.patientTable.set(_),this.db.storage.setItem("PATIENT_TABLE",_)}let d={message:"Registration successful",email:e.email,id:n,userId:n,patientId:e.role.toUpperCase()==="PATIENT"?l:void 0,role:e.role.toUpperCase(),offline:!0},m={id:n,patientId:e.role.toUpperCase()==="PATIENT"?l:void 0,full_name:`${e.firstName} ${e.lastName}`,email:e.email,profilePicture:null};return this.storage.setItem(this.userInfoKey,m),w(d)}static \u0275fac=function(t){return new(t||r)(z(xe),z(Le),z(rn),z(Ne),z(Pe),z(be),z(ae),z(_n))};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};var le=class r{constructor(e){this.http=e;this.loadInitialData()}apiUrl="";currentAppointmentSubject=new ne(null);appointmentsSubject=new ne([]);availableSlotsSubject=new ne([]);doctorSchedulesSubject=new ne([]);selectedDoctorSubject=new ne(null);db=G(ae);securaService=G(Mn);authService=G(q);firestore=G(Ne);storage=G(Pe);firebaseAppointmentService=G(_t);currentAppointment$=this.currentAppointmentSubject.asObservable();appointments$=this.appointmentsSubject.asObservable();availableSlots$=this.availableSlotsSubject.asObservable();doctorSchedules$=this.doctorSchedulesSubject.asObservable();selectedDoctor$=this.selectedDoctorSubject.asObservable();loadInitialData(){this.loadAppointmentsForCurrentUser(),this.loadAvailableSlots(),this.syncAvailabilityWithFirebase()}loadAppointmentsForCurrentUser(){console.log("Loading appointments for current user from Firebase");let e=this.authService.getPatientId();if(e)console.log("Patient ID found, loading appointments from Firebase:",e),this.loadAppointmentsFromFirestore(e);else{console.warn("No patient ID available from auth service, trying to get from user info");let t=this.authService.getUserInfo();if(t&&(t.patientId||t.id)){let n=t.patientId||t.id;console.log("Using ID from user info for Firebase query:",n),n?(this.storage.setItem("patient_id",n),this.loadAppointmentsFromFirestore(n)):(console.error("ID is undefined, cannot load appointments from Firebase"),this.appointmentsSubject.next([]))}else console.error("No patient ID available in user info either, cannot load appointments from Firebase"),this.appointmentsSubject.next([])}}loadAppointmentsFromFirestore(e){console.log("Loading appointments from Firestore for patient ID:",e);let t=_e(this.firestore,"appointments");console.log("Querying Firestore collection: appointments"),console.log("Creating query for patientId field...");let n=ye(t,ve("patientId","==",e));console.log("Creating query for patient_id field...");let i=ye(t,ve("patient_id","==",e));console.log("Executing both queries..."),k(Promise.all([pt(n),pt(i)])).pipe(E(([o,l])=>{console.log("Query results received from Firestore"),console.log(`First query returned ${o.size} documents`),console.log(`Second query returned ${l.size} documents`);let d=[],m=new Set;return[o,l].forEach((h,_)=>{if(h.empty){console.log(`Query ${_+1} returned no results`);return}h.forEach(N=>{if(m.has(N.id)){console.log(`Skipping duplicate document: ${N.id}`);return}m.add(N.id);let R=N.data();console.log(`Processing appointment: ${N.id}`);let Sn=R.doctorId||R.doctor_id||"",we;R.appointmentDate instanceof jt?(we=R.appointmentDate.toDate(),console.log(`Timestamp date found: ${we}`)):R.date?(we=new Date(R.date),console.log(`String date found: ${R.date}`)):typeof R.appointmentDate=="string"?(we=new Date(R.appointmentDate),console.log(`String appointmentDate found: ${R.appointmentDate}`)):(we=new Date,console.warn("No valid date format found for appointment",N.id));let In=R.appointmentTime||R.time||"",Ft="Pending",zt=(R.status||"Pending").toString();console.log(`Appointment status: ${zt}`),["Approved","Rejected","Completed","Cancelled","Available","Pending"].includes(zt)&&(Ft=zt);let kn={appointmentId:N.id,doctorId:Sn,doctorName:R.doctorName||"Unknown Doctor",specialization:R.specialization||"",appointmentDate:we,appointmentTime:In,durationMinutes:R.durationMinutes||30,reasonForVisit:R.reasonForVisit||"",status:Ft,doctorNotes:R.doctorNotes||""};d.push(kn),console.log(`Added appointment with ID: ${N.id}, status: ${Ft}`)})}),console.log(`Loaded ${d.length} appointments from Firebase for patient ${e}`),d.length>0&&console.log("First appointment:",JSON.stringify(d[0])),this.sortAppointments(d)}),v(o=>(console.error("Error loading appointments from Firestore:",o),console.log("Attempting to load from legacy API..."),this.loadAppointmentsFromAPI(e),w([])))).subscribe(o=>{console.log(`Pushing ${o.length} appointments to subject`),this.appointmentsSubject.next(o);let l=new Date,d=o.find(m=>{let h=new Date(m.appointmentDate),_=this.parseTime(m.appointmentTime),N=new Date(h.getFullYear(),h.getMonth(),h.getDate(),_.getHours(),_.getMinutes()),R=new Date(N);return R.setMinutes(R.getMinutes()+m.durationMinutes),l>=N&&l<=R&&m.status==="Approved"});d&&(console.log("Found current active appointment:",d.appointmentId),this.currentAppointmentSubject.next(d)),this.updateLocalAppointments(o)})}sortAppointments(e){return[...e].sort((t,n)=>{let i=new Date(t.appointmentDate),o=new Date(n.appointmentDate),l=i.getTime()-o.getTime();if(l!==0)return l;let d=this.parseTime(t.appointmentTime),m=this.parseTime(n.appointmentTime);return d.getTime()-m.getTime()})}updateLocalAppointments(e){let t=e.map(n=>{let i=n.status;return n.status==="Rejected"&&(i="Cancelled"),{id:n.appointmentId,patient_id:this.authService.getPatientId()||"",doctor_id:n.doctorId,date:n.appointmentDate.toISOString().split("T")[0],time:n.appointmentTime,status:i,patientName:this.authService.getUserInfo()?.full_name||"",reasonForVisit:n.reasonForVisit,doctorNotes:n.doctorNotes}});this.db.appointmentTable.set(t),this.storage.setItem("APPOINTMENT_TABLE",t)}fetchDoctorDetailsFromFirestore(e){return e?k(fe(W(this.firestore,"doctors",e))).pipe(E(t=>t.exists()?I({id:t.id},t.data()):null),v(t=>(console.error(`Error fetching doctor details for ID ${e}:`,t),w(null)))):w(null)}loadAppointments(){let e=this.authService.getPatientId();if(!e){console.warn("No patient ID available, skipping appointment loading"),this.appointmentsSubject.next([]);return}console.log("Loading appointments from legacy API for patient ID:",e),this.http.get(`${this.apiUrl}/appointments/patient/${e}`).pipe(E(t=>(console.log("Appointments loaded from API:",t),t.map(n=>{let i=this.db.userTable().find(l=>l.id===n.doctorId),o=i?this.db.doctorTable().find(l=>l.user_id===i.id):null;return F(I({},n),{appointmentDate:new Date(n.appointmentDate),doctorName:i?`${i.firstname} ${i.lastname}`:"Unknown Doctor",specialization:o?.specialisation||""})}))),v(t=>(console.error("Error loading appointments from API:",t),w([])))).subscribe(t=>{this.appointmentsSubject.next(t)})}fetchPatientIdFromProfile(){let e=this.storage.getItem("med_secure_token");if(!e){console.warn("No auth token available, cannot fetch profile");return}let t={Authorization:`Bearer ${e}`};this.http.get(`${this.apiUrl}/patients/profile/info`,{headers:t}).pipe(v(n=>(console.error("Error fetching patient profile:",n),w(null)))).subscribe(n=>{if(n&&n.success&&n.data){let i=n.data.id||n.data.patientId;i&&(console.log("Retrieved patient ID from profile:",i),this.storage.setItem("patient_id",i),this.loadAppointments())}})}loadAvailableSlots(){console.log("Loading available appointment slots from Firebase");let e=this.storage.getItem("current_doctor");if(!e||!e.id){console.warn("No doctor info available for loading slots"),this.availableSlotsSubject.next([]);return}let t=e.id;console.log("Loading available slots for doctor ID:",t);let n=_e(this.firestore,"appointments"),i=ye(n,ve("doctorId","==",t),ve("status","==","Available"),qe("appointmentDate","asc"));k(pt(i)).pipe(E(o=>{let l=[];return o.forEach(d=>{let m=d.data(),h=m.appointmentDate instanceof jt?m.appointmentDate.toDate():new Date(m.appointmentDate);l.push({appointmentId:d.id,doctorId:m.doctorId||m.doctor_id||"",doctorName:m.doctorName||"Available Slot",appointmentDate:h,appointmentTime:m.appointmentTime||m.time||"",durationMinutes:m.durationMinutes||30,reasonForVisit:"",status:"Available"})}),console.log("Loaded available slots from Firebase:",l),l}),v(o=>{console.error("Error loading available slots from Firebase:",o),console.log("Falling back to local storage for available slots");try{let l=this.db.appointmentTable().filter(d=>d.status==="Available");if(l.length>0){console.log("Found available slots in local storage:",l.length);let d=l.map(m=>({appointmentId:m.id,doctorId:m.doctor_id,doctorName:"Available Slot",appointmentDate:new Date(m.date),appointmentTime:m.time,durationMinutes:30,reasonForVisit:"",status:"Available"}));return w(d)}}catch(l){console.error("Error parsing local storage appointments:",l)}return w([])})).subscribe(o=>{this.availableSlotsSubject.next(o)})}getCurrentDoctor(){return this.selectedDoctorSubject.getValue()}getDoctorDetails(e){return this.http.get(`${this.apiUrl}/doctors/${e}`).pipe(Z(t=>{this.selectedDoctorSubject.next(t),this.storage.setItem("currentDoctor",t)}),v(t=>(console.error("Error loading doctor details:",t),C(()=>new Error("Failed to load doctor details")))))}setCurrentDoctor(e){this.selectedDoctorSubject.next(e),this.storage.setItem("currentDoctor",e),this.loadAvailableSlots()}getAppointmentsWithAvailableSlotsByDate(e){let t=this.appointmentsSubject.getValue(),n=this.availableSlotsSubject.getValue();return[...t,...n].filter(o=>new Date(o.appointmentDate).toDateString()===e.toDateString())}getAvailableSlotsByDate(e){return this.availableSlotsSubject.getValue().filter(n=>new Date(n.appointmentDate).toDateString()===e.toDateString())}getAvailableSlotsByDoctorAndDate(e,t){return this.availableSlotsSubject.getValue().filter(i=>{let o=new Date(i.appointmentDate);return i.doctorId===e&&o.toDateString()===t.toDateString()})}bookAppointment(e,t){let n=this.authService.getPatientId();if(!n)return C(()=>new Error("No patient ID available"));let i=this.authService.getUserInfo(),o=i?i.full_name:"Unknown Patient",l={patientId:n,patientName:o,doctorId:e.doctorId,doctorName:e.doctorName,specialization:e.specialization||"",appointmentDate:e.appointmentDate,appointmentTime:e.appointmentTime,durationMinutes:e.durationMinutes||30,reasonForVisit:t,status:"Pending",createdAt:new Date,updatedAt:new Date};console.log("Booking appointment in Firestore:",l);let d=_e(this.firestore,"appointments");return k(ct(d,l)).pipe(E(m=>{let h={appointmentId:m.id,doctorId:e.doctorId,doctorName:e.doctorName,specialization:e.specialization,appointmentDate:e.appointmentDate,appointmentTime:e.appointmentTime,durationMinutes:e.durationMinutes,reasonForVisit:t,status:"Pending"},_=this.appointmentsSubject.getValue();return this.appointmentsSubject.next([..._,h]),console.log("Appointment booked successfully:",h),h}),v(m=>(console.error("Error booking appointment in Firestore:",m),this.bookAppointmentLegacy(e,t))))}bookAppointmentLegacy(e,t){let n=this.authService.getPatientId();if(!n)return C(()=>new Error("No patient ID available"));let i={patientId:n,doctorId:e.doctorId,appointmentDate:new Date(e.appointmentDate).toISOString(),appointmentTime:e.appointmentTime,reasonForVisit:t};return this.http.post(`${this.apiUrl}/appointments/book`,i).pipe(Z(o=>{let l=this.appointmentsSubject.getValue();this.appointmentsSubject.next([...l,F(I({},o),{appointmentDate:new Date(o.appointmentDate)})]),console.log("Appointment booked successfully via API:",o)}),v(o=>(console.error("Error booking appointment via API:",o),C(()=>new Error("Failed to book appointment")))))}cancelAppointment(e,t){console.log("Cancelling appointment in Firestore:",e);let n=W(this.firestore,"appointments",e);return k(Ge(n,{status:"Cancelled",cancellationReason:t,updatedAt:new Date})).pipe(E(()=>{let o=this.appointmentsSubject.getValue().map(l=>l.appointmentId===e?F(I({},l),{status:"Cancelled"}):l);this.appointmentsSubject.next(o),console.log("Appointment cancelled successfully in Firestore")}),v(i=>(console.error("Error cancelling appointment in Firestore:",i),this.cancelAppointmentLegacy(e,t))))}cancelAppointmentLegacy(e,t){return this.http.post(`${this.apiUrl}/appointments/${e}/cancel`,{reason:t}).pipe(Z(()=>{let i=this.appointmentsSubject.getValue().map(o=>o.appointmentId===e?F(I({},o),{status:"Cancelled"}):o);this.appointmentsSubject.next(i),console.log("Appointment cancelled successfully via API")}),v(n=>(console.error("Error cancelling appointment via API:",n),C(()=>new Error("Failed to cancel appointment")))))}parseTime(e){let t=new Date,n=e.split(":");if(n.length>=2){let i=parseInt(n[0],10),o=parseInt(n[1],10);t.setHours(i,o,0,0)}return t}getCurrentAppointment(){return this.currentAppointmentSubject.getValue()}setCurrentAppointment(e){this.currentAppointmentSubject.next(e)}getUpcomingAppointments(){let e=new Date;return this.appointmentsSubject.getValue().filter(n=>new Date(n.appointmentDate)>=e&&(n.status==="Approved"||n.status==="Pending")).sort((n,i)=>{let o=new Date(n.appointmentDate),l=new Date(i.appointmentDate);return o.getTime()-l.getTime()}).slice(0,5)}getAllAppointments(){return this.appointmentsSubject.getValue()}getAppointmentsByStatus(e){return this.appointmentsSubject.getValue().filter(n=>n.status===e)}getAppointmentsByDate(e){let t=this.appointmentsSubject.getValue(),n=e.toISOString().split("T")[0];return t.filter(i=>new Date(i.appointmentDate).toISOString().split("T")[0]===n).sort((i,o)=>{let l=this.parseTime(i.appointmentTime).getTime(),d=this.parseTime(o.appointmentTime).getTime();return l-d})}addAppointmentsFromSecura(e,t){this.securaService.patientAppointments(e,t).forEach(i=>{if(i.status===t&&i.patient_id===e){let o=this.db.appointmentTable().find(l=>l.id===i.id);o?(o.status=t,this.db.saveToLocalStorage()):this.db.addAppointment(i)}})}refreshAppointments(){return console.log("Refreshing appointments from Firebase..."),new Se(e=>{let t=this.authService.getPatientId();if(t){console.log("Refreshing appointments for patient ID:",t),this.loadAppointmentsFromFirestore(t);let n=this.appointmentsSubject.subscribe(i=>{console.log(`Refreshed ${i.length} appointments from Firebase`),e.next(i),e.complete(),n.unsubscribe()})}else console.warn("No patient ID available for refreshing appointments"),e.next([]),e.complete()})}getDoctorSchedules(e){return this.http.get(`${this.apiUrl}/api/schedules/doctor/${e}`).pipe(E(t=>t.filter(n=>n.isActive)),Z(t=>{this.doctorSchedulesSubject.next(t)}),v(t=>(console.error("Error loading doctor schedules:",t),w([]))))}formatTimeForDisplay(e){if(!e)return"";let[t,n]=e.split(":").map(Number),i=t>=12?"PM":"AM";return`${t%12||12}:${n.toString().padStart(2,"0")} ${i}`}getDayName(e){return["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][e]||""}getCompletedAppointments(){return this.appointmentsSubject.getValue().filter(e=>e.status==="Completed").sort((e,t)=>{let n=new Date(e.appointmentDate).getTime();return new Date(t.appointmentDate).getTime()-n})}getCancelledAppointments(){return this.appointmentsSubject.getValue().filter(e=>["Cancelled","Rejected"].includes(e.status)).sort((e,t)=>{let n=new Date(e.appointmentDate).getTime();return new Date(t.appointmentDate).getTime()-n})}syncAvailabilityWithFirebase(){console.log("Initializing availability synchronization with Firebase");let e=this.storage.getItem("current_doctor");if(!e||!e.id){console.log("Not logged in as a doctor, skipping availability sync");return}let t=e.id;console.log(`Starting availability sync for doctor ID: ${t}`),this.firebaseAppointmentService.syncAvailabilityData(t).subscribe({next:()=>{console.log("Successfully synchronized availability data from mobile service")},error:n=>{console.error("Error synchronizing availability data from mobile service:",n)}})}loadAppointmentsFromAPI(e){console.log("Falling back to API for appointments with patient ID:",e),this.loadAppointments()}static \u0275fac=function(t){return new(t||r)(z(xe))};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};var Ln=(r,e,t)=>({"bi-calendar-x":r,"bi-calendar-check":e,"bi-calendar-plus":t});function Bn(r,e){if(r&1){let t=D();a(0,"div",7)(1,"span"),p(2),s(),a(3,"button",8),b("click",function(){M(t);let i=f();return y(i.loadAppointments())}),p(4,"Retry"),s()()}if(r&2){let t=f();c(2),O(t.error)}}function Vn(r,e){r&1&&(a(0,"div",9),u(1,"div",10),a(2,"p"),p(3,"Loading appointments..."),s()())}function $n(r,e){r&1&&u(0,"span",17)}function Hn(r,e){if(r&1&&(a(0,"div",31)(1,"h4"),p(2,"Doctor's Notes"),s(),a(3,"p"),p(4),s()()),r&2){let t=f().$implicit;c(4),O(t.doctorNotes)}}function Zn(r,e){if(r&1&&(a(0,"div",19),u(1,"div",20),a(2,"div",21)(3,"div",22)(4,"h3"),p(5),s(),a(6,"p",23),p(7),s()(),a(8,"div",24),p(9),s()(),a(10,"div",25)(11,"div",26),u(12,"i",27),a(13,"span"),p(14),s()(),a(15,"div",26),u(16,"i",28),a(17,"span"),p(18),s()(),a(19,"div",26),u(20,"i",29),a(21,"span"),p(22),s()()(),x(23,Hn,5,1,"div",30),s()),r&2){let t=e.$implicit,n=f(3);A("approved-appointment",t.status==="Approved"),c(),Ze("background-color",n.getStatusColor(t.status)),c(4),T("Dr. ",t.doctorName,""),c(2),O(t.specialization||"General Practitioner"),c(),Ze("background-color",n.getStatusColor(t.status)+"20")("color",n.getStatusColor(t.status)),c(),T(" ",t.status," "),c(5),O(n.formatDate(t.appointmentDate)),c(4),O(n.formatTime(t.appointmentTime)),c(4),T("",t.durationMinutes," min"),c(),g("ngIf",t.doctorNotes&&n.activeTab==="completed")}}function Wn(r,e){if(r&1&&(Ae(0),x(1,Zn,24,15,"div",18),De()),r&2){let t=f(2);c(),g("ngForOf",t.filteredAppointments)}}function Kn(r,e){r&1&&(a(0,"p"),p(1," You don't have any approved appointments. "),u(2,"br"),a(3,"small"),p(4,"Would you like to book one?"),s()())}function qn(r,e){r&1&&(a(0,"p"),p(1," You don't have any completed appointments yet. "),s())}function Gn(r,e){r&1&&(a(0,"p"),p(1," You don't have any cancelled appointments. "),s())}function Yn(r,e){if(r&1&&(a(0,"div",32)(1,"div",33),u(2,"i",34),x(3,Kn,5,0,"p",35)(4,qn,2,0,"p",35)(5,Gn,2,0,"p",35),s()()),r&2){let t=f(2);c(2),g("ngClass",Jt(4,Ln,t.activeTab==="cancelled",t.activeTab==="completed",t.activeTab==="upcoming")),c(),g("ngIf",t.activeTab==="upcoming"),c(),g("ngIf",t.activeTab==="completed"),c(),g("ngIf",t.activeTab==="cancelled")}}function Xn(r,e){if(r&1){let t=D();a(0,"div",11)(1,"div",12)(2,"button",13),b("click",function(){M(t);let i=f();return y(i.setActiveTab("upcoming"))}),p(3," Approved "),x(4,$n,1,0,"span",14),s(),a(5,"button",13),b("click",function(){M(t);let i=f();return y(i.setActiveTab("completed"))}),p(6," Completed "),s(),a(7,"button",13),b("click",function(){M(t);let i=f();return y(i.setActiveTab("cancelled"))}),p(8," Cancelled "),s()(),a(9,"div",15),x(10,Wn,2,1,"ng-container",16)(11,Yn,6,8,"ng-template",null,0,tt),s()()}if(r&2){let t=Je(12),n=f();c(2),A("active",n.activeTab==="upcoming"),c(2),g("ngIf",n.hasUpcomingAppointments),c(),A("active",n.activeTab==="completed"),c(2),A("active",n.activeTab==="cancelled"),c(3),g("ngIf",n.filteredAppointments.length>0)("ngIfElse",t)}}var Ot=class r{constructor(e,t){this.location=e;this.appointmentService=t}activeTab="upcoming";tabHistory=["upcoming"];appointments=[];upcomingAppointments=[];completedAppointments=[];cancelledAppointments=[];isLoading=!1;error=null;ngOnInit(){this.loadAppointments()}loadAppointments(){this.isLoading=!0,this.error=null;let e=this.appointmentService.getAllAppointments();console.log("Local appointments loaded:",e),e.length>0?(this.appointments=e,this.categorizeAppointments()):(console.warn("No local appointments found"),this.appointments=[],this.upcomingAppointments=[],this.completedAppointments=[],this.cancelledAppointments=[]),this.isLoading=!1,this.appointmentService.refreshAppointments()}categorizeAppointments(){console.log("Categorizing appointments, total count:",this.appointments.length),console.log("Appointment statuses:",this.appointments.map(e=>e.status)),this.upcomingAppointments=this.appointments.filter(e=>e.status==="Approved").sort((e,t)=>new Date(e.appointmentDate).getTime()-new Date(t.appointmentDate).getTime()),this.completedAppointments=this.appointments.filter(e=>e.status==="Completed").sort((e,t)=>new Date(t.appointmentDate).getTime()-new Date(e.appointmentDate).getTime()),this.cancelledAppointments=this.appointments.filter(e=>["Cancelled","Rejected"].includes(e.status)).sort((e,t)=>new Date(t.appointmentDate).getTime()-new Date(e.appointmentDate).getTime()),console.log("Upcoming appointments (Approved only):",this.upcomingAppointments.length),console.log("Completed appointments:",this.completedAppointments.length),console.log("Cancelled appointments:",this.cancelledAppointments.length)}goBack(){this.tabHistory.length>1&&(this.tabHistory.pop(),this.activeTab=this.tabHistory[this.tabHistory.length-1])}setActiveTab(e){this.tabHistory[this.tabHistory.length-1]!==e&&this.tabHistory.push(e),this.activeTab=e}get filteredAppointments(){switch(this.activeTab){case"upcoming":return this.upcomingAppointments;case"completed":return this.completedAppointments;case"cancelled":return this.cancelledAppointments;default:return[]}}get hasUpcomingAppointments(){return this.upcomingAppointments.length>0}get hasApprovedAppointments(){return this.upcomingAppointments.length>0}get hasCompletedAppointments(){return this.completedAppointments.length>0}get hasCancelledAppointments(){return this.cancelledAppointments.length>0}formatDate(e){let t=new Date(e),n=t.toLocaleDateString("en-US",{weekday:"short"}),i=t.toLocaleDateString("en-US",{month:"short"}),o=t.getDate();return`${n}, ${i} ${o}`}formatTime(e){let[t,n]=e.split(":"),i=parseInt(t,10),o=i>=12?"PM":"AM";return`${i%12||12}:${n} ${o}`}getStatusColor(e){switch(e){case"Pending":return"#eab308";case"Approved":return"#22c55e";case"Rejected":return"#ef4444";case"Completed":return"#199a8e";case"Cancelled":return"#6b7280";default:return"#6b7280"}}cancelAppointment(e){e&&(this.isLoading=!0,this.appointmentService.cancelAppointment(e.appointmentId,"Cancelled by patient").subscribe({next:()=>{this.loadAppointments(),this.isLoading=!1},error:t=>{console.error("Error cancelling appointment:",t),this.error="Failed to cancel appointment",this.isLoading=!1}}))}static \u0275fac=function(t){return new(t||r)(P(nt),P(le))};static \u0275cmp=S({type:r,selectors:[["app-appointment-schedule"]],decls:8,vars:3,consts:[["noAppointments",""],[1,"schedule-screen"],["routerLink","/patient-dashboard"],[1,"title"],["class","error-alert",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","content",4,"ngIf"],[1,"error-alert"],[3,"click"],[1,"loading-container"],[1,"loading-spinner"],[1,"content"],[1,"tabs"],[1,"tab",3,"click"],["class","badge",4,"ngIf"],[1,"appointments-list"],[4,"ngIf","ngIfElse"],[1,"badge"],["class","appointment-card",3,"approved-appointment",4,"ngFor","ngForOf"],[1,"appointment-card"],[1,"card-status-indicator"],[1,"card-header"],[1,"doctor-info"],[1,"specialization"],[1,"status-badge"],[1,"appointment-meta"],[1,"meta-item"],[1,"bi","bi-calendar-check"],[1,"bi","bi-clock"],[1,"bi","bi-hourglass-split"],["class","doctor-notes",4,"ngIf"],[1,"doctor-notes"],[1,"no-appointments"],[1,"empty-state"],[1,"bi",3,"ngClass"],[4,"ngIf"]],template:function(t,n){t&1&&(a(0,"div",1),u(1,"app-back-button",2),a(2,"h1",3),p(3,"My Appointments"),s(),x(4,Bn,5,1,"div",4)(5,Vn,4,0,"div",5)(6,Xn,13,9,"div",6),u(7,"app-navbar"),s()),t&2&&(c(4),g("ngIf",n.error),c(),g("ngIf",n.isLoading),c(),g("ngIf",!n.isLoading))},dependencies:[Ee,U,it,H,Q,$],styles:[".appointment-container[_ngcontent-%COMP%]{width:100vw;height:100vh;font-family:Poppins,sans-serif;padding:16px;border-radius:10px}.header-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin-bottom:30px}.back-button[_ngcontent-%COMP%]{background:none;border:none;font-size:20px;color:#199a8e;cursor:pointer}.header-title[_ngcontent-%COMP%]{text-align:center;font-size:16px;font-weight:700;color:#333;margin:40px 40px 30px 0;width:100%}header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{flex:1;padding:clamp(5px,1.25vw,8px);margin:-20px clamp(3px,.75vw,5px);font-size:clamp(12px,3vw,14px);font-weight:500;border:none;border-radius:20px;background:#e6f1f3;color:#199a8e;cursor:pointer;transition:all .3s ease;white-space:nowrap;min-width:80px}header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(.active){background:#199a8e33}header[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background:#199a8e;color:#fff}.appointment-card[_ngcontent-%COMP%]{display:flex;align-items:flex-start;background:#e6f1f3;border-radius:12px;padding:clamp(12px,3vw,16px);margin:clamp(20px,5vh,30px) 0;box-shadow:0 2px 5px #0000001a;transition:transform .3s ease,box-shadow .3s ease;gap:clamp(10px,2.5vw,12px)}.appointment-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.doctor-image[_ngcontent-%COMP%]{width:clamp(40px,10vw,50px);height:clamp(40px,10vw,50px);border-radius:50%;object-fit:cover;flex-shrink:0}.appointment-info[_ngcontent-%COMP%]{flex:1}.appointment-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:clamp(13px,3.25vw,14px);font-weight:700;color:#199a8e}.appointment-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:clamp(4px,1vh,6px) 0;font-size:clamp(11px,2.75vw,12px);color:#555;line-height:1.4}.date-time-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(10px,2.5vw,15px);font-size:clamp(11px,2.75vw,12px);flex-wrap:wrap;margin-top:clamp(6px,1.5vh,8px)}.date-time-status[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{display:flex;align-items:center;margin:0;gap:4px}.date-time-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(9px,2.25vw,10px)}.status[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:clamp(11px,2.75vw,12px);font-weight:700;gap:4px}.confirmed[_ngcontent-%COMP%], .confirmed[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#199a8e}.completed[_ngcontent-%COMP%], .completed[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:gray}.cancelled[_ngcontent-%COMP%], .cancelled[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:red}.status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(20px,5vw,25px);margin:0}.actions[_ngcontent-%COMP%]{display:flex;gap:clamp(8px,2vw,12px);margin-top:clamp(8px,2vh,12px);flex-wrap:wrap}button[_ngcontent-%COMP%]{padding:clamp(6px,1.5vh,8px) clamp(10px,2.5vw,12px);font-size:clamp(11px,2.75vw,12px);border:none;border-radius:15px;cursor:pointer;transition:all .3s ease;white-space:nowrap}.cancel-btn[_ngcontent-%COMP%], .reschedule-btn[_ngcontent-%COMP%], .rebook-btn[_ngcontent-%COMP%]{background:#fff;color:#199a8e;border:1px solid #199A8E;padding:clamp(2px,.5vh,3px) clamp(20px,5vw,25px);transition:all .3s ease}.cancel-btn[_ngcontent-%COMP%]:hover, .reschedule-btn[_ngcontent-%COMP%]:hover, .rebook-btn[_ngcontent-%COMP%]:hover{background:#199a8e;color:#fff}.no-appointments[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:40vh;margin-top:10px;text-align:center}.no-appointments[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#555;font-size:16px;font-weight:700}.schedule-screen[_ngcontent-%COMP%]{padding:12px;min-height:100vh;background:#f8f9fa;position:relative;max-width:100%;overflow-x:hidden;box-sizing:border-box}.title[_ngcontent-%COMP%]{font-size:20px;color:#111827;margin:16px 0 20px;text-align:center;font-weight:600}.error-alert[_ngcontent-%COMP%]{background:#fee2e2;color:#b91c1c;padding:12px;border-radius:12px;margin-bottom:16px;display:flex;justify-content:space-between;align-items:center;box-shadow:0 2px 4px #b91c1c1a;font-size:13px}.error-alert[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background:#b91c1c;color:#fff;border:none;padding:6px 10px;border-radius:6px;font-size:12px;font-weight:500}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:40px 20px;height:40vh}.loading-spinner[_ngcontent-%COMP%]{width:36px;height:36px;border:3px solid rgba(25,154,142,.1);border-top:3px solid #199a8e;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:16px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#4b5563;font-size:14px}.content[_ngcontent-%COMP%]{padding-top:10px;margin-bottom:80px}.tabs[_ngcontent-%COMP%]{display:flex;background:#fff;border-radius:16px;padding:6px;margin-bottom:16px;box-shadow:0 2px 8px #0000000d}.tab[_ngcontent-%COMP%]{flex:1;padding:10px 6px;border:none;border-radius:12px;background:none;color:#6b7280;font-size:13px;font-weight:600;cursor:pointer;position:relative;transition:all .3s ease}.tab.active[_ngcontent-%COMP%]{background:#199a8e;color:#fff;box-shadow:0 2px 5px #199a8e4d}.tab[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{position:absolute;top:4px;right:4px;width:6px;height:6px;border-radius:50%;background:#ef4444}.appointments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:14px;width:100%;box-sizing:border-box}.appointment-card[_ngcontent-%COMP%]{background:#fff;border-radius:12px;padding:0;box-shadow:0 2px 8px #0000000d;border:1px solid #f0f0f0;overflow:hidden;width:100%;max-width:100%;box-sizing:border-box;margin:0;position:relative}.card-status-indicator[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:4px;height:100%;border-top-left-radius:12px;border-bottom-left-radius:12px}.card-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:16px;border-bottom:1px solid #f3f4f6}.doctor-info[_ngcontent-%COMP%]{flex:1;padding-right:12px}.doctor-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 4px;font-size:16px;font-weight:600;color:#111827;line-height:1.4}.specialization[_ngcontent-%COMP%]{margin:0;font-size:14px;color:#6b7280}.status-badge[_ngcontent-%COMP%]{padding:6px 12px;border-radius:20px;font-size:13px;font-weight:600;white-space:nowrap;box-shadow:0 1px 2px #0000000d}.appointment-meta[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:8px;padding:12px 16px;background:#fafafa}.meta-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;padding:6px 10px;background:#fff;border-radius:8px;box-shadow:0 1px 2px #00000008;flex:1;min-width:120px}.meta-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#199a8e;font-size:14px}.meta-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;color:#374151}.visit-reason[_ngcontent-%COMP%], .doctor-notes[_ngcontent-%COMP%]{padding:12px 16px;border-top:1px solid #f3f4f6}.visit-reason[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .doctor-notes[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;font-size:14px;font-weight:600;color:#374151}.visit-reason[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .doctor-notes[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;color:#4b5563;line-height:1.6;overflow-wrap:break-word}.card-actions[_ngcontent-%COMP%]{padding:12px 16px;border-top:1px solid #f3f4f6}.cancel-button[_ngcontent-%COMP%]{width:100%;padding:10px;background:#fee2e2;color:#b91c1c;border:none;border-radius:8px;font-size:14px;font-weight:600;cursor:pointer;transition:all .2s ease;display:flex;align-items:center;justify-content:center;gap:8px}.cancel-button[_ngcontent-%COMP%]:hover{background:#fecaca}.no-appointments[_ngcontent-%COMP%]{text-align:center;padding:30px 16px;background:#fff;border-radius:12px;box-shadow:0 2px 10px #0000000d;margin:10px 0}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:28px;color:#9ca3af;margin-bottom:12px}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;font-size:14px;line-height:1.5;margin:0}.empty-state[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:#9ca3af;font-size:12px;display:block;margin-top:4px}.approved-appointment[_ngcontent-%COMP%]{border-left:none}@keyframes _ngcontent-%COMP%_subtle-pulse{0%{box-shadow:0 2px 8px #22c55e0d}50%{box-shadow:0 4px 12px #22c55e1f}to{box-shadow:0 2px 8px #22c55e0d}}@media screen and (max-width: 390px){.schedule-screen[_ngcontent-%COMP%]{padding:12px}.card-header[_ngcontent-%COMP%]{padding:12px 12px 10px 16px}.meta-item[_ngcontent-%COMP%]{width:calc(50% - 3px);padding:5px 8px;font-size:12px}.doctor-info[_ngcontent-%COMP%]{max-width:65%}.appointment-meta[_ngcontent-%COMP%], .visit-reason[_ngcontent-%COMP%], .doctor-notes[_ngcontent-%COMP%], .card-actions[_ngcontent-%COMP%]{padding:10px 12px}.visit-reason[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .doctor-notes[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:12px}}@media (max-width: 400px){.title[_ngcontent-%COMP%]{font-size:20px}.tab[_ngcontent-%COMP%]{font-size:13px;padding:10px 4px}.doctor-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:15px}.detail-item[_ngcontent-%COMP%]{min-width:100%}}@keyframes _ngcontent-%COMP%_subtle-pulse{0%{box-shadow:0 2px 10px #0000000d}50%{box-shadow:0 4px 15px #00000014}to{box-shadow:0 2px 10px #0000000d}}.approved-appointment[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_subtle-pulse 3s infinite ease-in-out}@media (max-width: 480px){.approved-notice[_ngcontent-%COMP%]{padding:8px;font-size:14px}}@media (max-width: 400px){.schedule-screen[_ngcontent-%COMP%]{padding:16px}.title[_ngcontent-%COMP%]{font-size:20px}.appointment-card[_ngcontent-%COMP%]{padding:12px}.doctor-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:15px}.specialization[_ngcontent-%COMP%], .detail-item[_ngcontent-%COMP%]{font-size:13px}}@media screen and (max-width: 390px){.schedule-screen[_ngcontent-%COMP%]{padding:12px}.appointment-card[_ngcontent-%COMP%]{padding:14px}.detail-item[_ngcontent-%COMP%]{min-width:100%;margin-bottom:6px}.doctor-info[_ngcontent-%COMP%]{max-width:60%;margin-bottom:6px}.status-badge[_ngcontent-%COMP%]{padding:4px 8px;min-width:60px;font-size:11px}.tabs[_ngcontent-%COMP%]{padding:4px}.tab[_ngcontent-%COMP%]{padding:10px 6px;font-size:13px}.appointment-details[_ngcontent-%COMP%], .concern-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{padding:8px}}@media screen and (max-width: 320px){.schedule-screen[_ngcontent-%COMP%]{padding:10px}.meta-item[_ngcontent-%COMP%]{width:100%}}"]})};var Qn=r=>({selected:r});function Jn(r,e){if(r&1){let t=D();a(0,"div",9)(1,"label")(2,"input",10),ge("ngModelChange",function(i){M(t);let o=f();return me(o.selectedReason,i)||(o.selectedReason=i),y(i)}),b("change",function(){let i=M(t).$implicit,o=f();return y(o.onReasonSelect(i))}),s(),u(3,"span",11),a(4,"span",12),p(5),s()()()}if(r&2){let t=e.$implicit,n=f();c(),ke(Te(5,Qn,t.selected)),c(),g("value",t.id),de("ngModel",n.selectedReason),c(3),O(t.label)}}var Ye=class r{cancelReasons=[{id:1,label:"Rescheduling",selected:!1},{id:2,label:"Weather Conditions",selected:!1},{id:3,label:"Unexpected Work",selected:!1},{id:4,label:"Others",selected:!1}];selectedReason=null;additionalReason="";onReasonSelect(e){this.cancelReasons.forEach(t=>t.selected=t.id===e.id)}onCancelAppointment(){if(!this.selectedReason){alert("Please select a reason for cancellation");return}console.log("Appointment cancelled with reason:",{reasonId:this.selectedReason,additionalReason:this.additionalReason})}static \u0275fac=function(t){return new(t||r)};static \u0275cmp=S({type:r,selectors:[["app-cancel-booking"]],decls:14,vars:2,consts:[[1,"container"],["routerLink","/schedule"],[1,"header"],[1,"main-content"],[1,"confirmation-text"],[1,"reasons-list"],["class","reason-item",4,"ngFor","ngForOf"],["placeholder","Enter Your Reason Here...",1,"reason-textarea",3,"ngModelChange","ngModel"],[1,"cancel-button",3,"click"],[1,"reason-item"],["type","radio","name","cancelReason",3,"ngModelChange","change","value","ngModel"],[1,"radio-custom"],[1,"reason-text"]],template:function(t,n){t&1&&(a(0,"div",0),u(1,"app-back-button",1),a(2,"header",2)(3,"h1"),p(4,"Cancel Appointment"),s()(),u(5,"app-navbar"),a(6,"main",3)(7,"p",4),p(8," Please select your reason for cancelling your appointment below... "),s(),a(9,"div",5),x(10,Jn,6,7,"div",6),s(),a(11,"textarea",7),ge("ngModelChange",function(o){return me(n.additionalReason,o)||(n.additionalReason=o),o}),s(),a(12,"button",8),b("click",function(){return n.onCancelAppointment()}),p(13," Cancel Appointment "),s()()()),t&2&&(c(10),g("ngForOf",n.cancelReasons),c(),de("ngModel",n.additionalReason))},dependencies:[V,Ee,Ce,oe,bn,re,ze,H,Q,$],styles:['.container[_ngcontent-%COMP%]{overflow-y:hidden;margin:0 auto;padding:20px 20px 80px;font-family:system-ui,-apple-system,sans-serif}.header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:32px}.back-button[_ngcontent-%COMP%]{background:none;border:none;font-size:24px;padding:8px;cursor:pointer;color:#0e8177}h1[_ngcontent-%COMP%]{margin:15px 0 15px 78px;font-size:24px;font-weight:600;color:#000}.confirmation-text[_ngcontent-%COMP%]{color:#333;margin-bottom:32px;width:100%;margin-left:25px}.reasons-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-bottom:32px;margin-left:25px}.reason-item[_ngcontent-%COMP%]{position:relative}.reason-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer}.reason-item[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{position:absolute;opacity:0}.radio-custom[_ngcontent-%COMP%]{width:24px;height:24px;border:2px solid #11998e;border-radius:50%;margin-right:12px;position:relative}.reason-item[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + .radio-custom[_ngcontent-%COMP%]:after{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:12px;height:12px;background-color:#11998e;border-radius:50%}.reason-text[_ngcontent-%COMP%]{font-size:16px;color:#333}.reason-textarea[_ngcontent-%COMP%]{width:calc(100% - 32px);min-height:150px;padding:16px;border:1.5px solid #e0e0e0;border-radius:16px;margin:8px 0 40px;font-family:inherit;font-size:16px;resize:none;color:#333;background-color:#fff}.reason-textarea[_ngcontent-%COMP%]::placeholder{color:#999}.reason-textarea[_ngcontent-%COMP%]:focus{outline:none;border-color:#11998e}.cancel-button[_ngcontent-%COMP%]{width:100%;padding:16px;background-color:#11998e;color:#fff;border:none;border-radius:30px;font-size:16px;font-weight:500;cursor:pointer;transition:background-color .2s;margin-top:40px;margin-bottom:20px}.cancel-button[_ngcontent-%COMP%]:hover{background-color:#0e8177}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{transform:translateY(100%)}to{transform:translateY(0)}}@media (max-width: 375px){.profile-container[_ngcontent-%COMP%]{padding:15px}.menu-item[_ngcontent-%COMP%]{padding:12px}.nav-button[_ngcontent-%COMP%]{padding:6px 12px}}@media (max-width: 480px){.container[_ngcontent-%COMP%]{padding:16px}.reason-textarea[_ngcontent-%COMP%]{width:calc(100% - 32px);min-height:120px}}']})};var Me=class r{constructor(e,t,n,i,o){this.http=e;this.authService=t;this.apiUrlService=n;this.storage=i;this.ngZone=o;this.initializeProfile()}profileSubject=new ne(null);currentProfile$=this.profileSubject.asObservable();initializeProfile(){this.fetchProfileFromFirebase().subscribe(e=>{e?console.log("Successfully loaded profile from Firebase:",e):console.warn("No profile found in Firebase during initialization")},e=>{console.error("Error loading profile from Firebase:",e)})}loadProfileFromStorage(){let e=localStorage.getItem("patient_profile");if(e)try{let t=JSON.parse(e);this.profileSubject.next(t)}catch(t){console.error("Error parsing stored profile",t)}}verifyProfileWithServer(e){let t=this.authService.getAuthToken();if(!t)return;let n=new ue({Authorization:`Bearer ${t}`}),i=this.apiUrlService.getUrl("patients/profile/info");console.log("Verifying profile with server at:",i),this.http.get(i,{headers:n}).subscribe({next:o=>{o.success&&o.data&&JSON.stringify(o.data)!==JSON.stringify(e)&&(this.saveProfileToStorage(o.data),this.profileSubject.next(o.data))},error:o=>console.error("Error verifying profile with server:",o)})}saveProfileToStorage(e){localStorage.setItem("patient_profile",JSON.stringify(e)),localStorage.setItem("user_info",JSON.stringify({full_name:e.fullName,firstName:e.firstName,lastName:e.lastName,email:e.email,phoneNumber:e.phoneNumber,profilePicture:e.profilePicture,address:e.address,role:e.role,createdAt:e.createdAt,updatedAt:e.updatedAt}))}getCurrentProfile(){let e=this.profileSubject.value;return e||this.fetchProfileFromFirebase().subscribe(),e}fetchLatestProfile(){let e=this.authService.getAuthToken();if(!e)return console.error("No auth token found"),w(this.getCurrentProfile()||{});let t=new ue({Authorization:`Bearer ${e}`}),n=this.apiUrlService.getUrl("patients/profile/info");return console.log("Fetching profile from server at:",n),this.http.get(n,{headers:t}).pipe(E(i=>{if(i.success&&i.data){let o=i.data;return this.profileSubject.next(o),this.saveProfileToStorage(o),o}throw new Error("Failed to fetch latest profile")}),v(i=>(console.error("Error fetching latest profile:",i),w(this.getCurrentProfile()||{}))))}updateProfile(e){return console.log("Updating profile with data:",e),this.updateProfileViaApi(e).pipe(B(t=>this.uploadProfileToFirestore(t||e).pipe(E(()=>t||e),v(n=>(console.error("Failed to save profile to Firestore:",n),w(t||e))))))}updateProfileViaApi(e){let t=this.authService.getAuthToken();if(!t)return console.error("No auth token found for API call"),w(null);let n=new ue({"Content-Type":"application/json",Authorization:`Bearer ${t}`}),i={firstName:e.fullName.split(" ")[0],lastName:e.fullName.split(" ").slice(1).join(" "),email:e.email,phoneNumber:e.phoneNumber,profilePicture:e.profilePicture};console.log("Updating profile via API with data:",i);let o=this.apiUrlService.getUrl("patients/profile/update");return console.log("Update profile endpoint:",o),this.http.post(o,i,{headers:n}).pipe(Z(l=>console.log("Server response:",l)),E(l=>{if(l.success&&l.data){let d=l.data;return this.profileSubject.next(d),this.saveProfileToStorage(d),d}else throw new Error("Server did not return success response")}),v(l=>(console.error("Error updating profile via API:",l),w(null))))}updateProfilePicture(e){console.log("Updating profile picture with base64 data");let t=this.getCurrentProfile();if(!t)return console.log("No existing profile found, creating one from auth data"),this.createProfileFromAuth(e);let n=this.authService.getAuthToken();if(!n)return console.error("No auth token found"),console.log("No auth token found, falling back to Firebase"),this.uploadProfileToFirestore(F(I({},t),{profilePicture:e}));let i=new ue({"Content-Type":"application/json",Authorization:`Bearer ${n}`}),o={profilePicture:e};console.log("Sending profile picture update request");let l=this.apiUrlService.getUrl("patients/profile/update");return console.log("Update picture endpoint:",l),this.http.post(l,o,{headers:i}).pipe(Z(d=>console.log("Profile picture update response:",d)),E(d=>{if(d.success&&d.data){let m=d.data;return this.profileSubject.next(m),this.saveProfileToStorage(m),console.log("API updated successfully, also updating Firebase for consistency"),this.uploadProfileToFirestore(m).subscribe(),this.authService.updateUserInfoFromProfile(m),m}else throw new Error("Server did not return success response")}),v(d=>{console.error("Error updating profile picture:",d),console.log("API failed, falling back to Firebase for profile picture update");let m=F(I({},t),{profilePicture:e,updatedAt:new Date().toISOString()});return this.uploadProfileToFirestore(m).pipe(B(h=>w(h).pipe(Gt(500),B(()=>this.fetchProfileFromFirebase().pipe(E(_=>_||h),v(()=>w(h)))))))}))}createProfileFromAuth(e){return console.log("Creating new profile with profile picture"),k(this.authService.getCurrentUser()).pipe(B(t=>{if(!t)return C(()=>new Error("No authenticated user found"));let n=this.authService.getUserInfo();n||console.warn("No user info available, creating minimal profile");let i=t.displayName||n?.full_name||"",o=i.split(" "),l=o[0]||"",d=o.length>1?o.slice(1).join(" "):"",m={id:t.id,fullName:i,firstName:l,lastName:d,email:t.email||n?.email||"",phoneNumber:n?.phoneNumber||"",profilePicture:e,address:n?.address||"",role:n?.role||"PATIENT",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};return console.log("Created new profile from auth data:",m),this.uploadProfileToFirestore(m)}))}fetchProfileFromFirebase(){return console.log("Fetching profile directly from Firebase"),k(this.authService.getCurrentUser()).pipe(B(e=>{if(!e||!e.id)return console.warn("No authenticated user found for Firebase fetch"),w(null);let t=this.getFirestore();return t?k(this.ngZone.runOutsideAngular(()=>J(this,null,function*(){try{let n=e.id,i=W(t,"patients",n),o=yield fe(i);if(o.exists()){console.log("Firebase profile data found:",o.data());let l=o.data();return{id:n,fullName:l.fullName||"",firstName:l.firstName||"",lastName:l.lastName||"",email:l.email||"",phoneNumber:l.phoneNumber||"",profilePicture:l.profilePicture||null,address:l.address||"",role:l.role||"PATIENT",createdAt:l.createdAt?typeof l.createdAt=="string"?l.createdAt:new Date(l.createdAt.seconds*1e3).toISOString():void 0,updatedAt:l.updatedAt||null}}else return console.log("No profile found in Firebase for user:",n),null}catch(n){return console.error("Error fetching profile from Firebase:",n),null}}))).pipe(Z(n=>{this.ngZone.run(()=>{n&&(this.profileSubject.next(n),this.saveProfileToStorage(n))})})):(console.error("Firestore not available"),w(null))}),v(e=>(console.error("Error in fetchProfileFromFirebase:",e),w(null))))}uploadProfileToFirestore(e){return k(this.authService.getCurrentUser()).pipe(B(t=>{if(!t||!t.id)return C(()=>new Error("No authenticated user found"));let n=this.getFirestore();return n?k(this.ngZone.runOutsideAngular(()=>J(this,null,function*(){try{let i=this.authService.getUserInfo(),o=t.id,l=W(n,"patients",o),d=e.fullName.split(" "),m=d[0]||"",h=d.length>1?d.slice(1).join(" "):"",_=new Date().toISOString(),N={fullName:e.fullName,firstName:m,lastName:h,email:e.email,phoneNumber:e.phoneNumber||"",profilePicture:e.profilePicture||null,address:e.address||i?.address||"",role:e.role||i?.role||"PATIENT",updatedAt:lt(),createdAt:e.createdAt||(e.id?void 0:_)};return yield Oe(l,N,{merge:!0}),I(I({},e),N)}catch(i){throw console.error("Error uploading profile to Firestore:",i),i}}))).pipe(Z(i=>{this.ngZone.run(()=>{this.profileSubject.next(i),this.saveProfileToStorage(i)})})):C(()=>new Error("Firestore not available"))}))}getFirestore(){try{return dt()}catch(e){return console.error("Error getting Firestore instance:",e),null}}refreshProfileFromFirebase(){return this.profileSubject.next(null),this.fetchProfileFromFirebase()}static \u0275fac=function(t){return new(t||r)(z(xe),z(q),z(Le),z(Pe),z(be))};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};function ei(r,e){r&1&&(a(0,"div",17),u(1,"i",10),s())}function ti(r,e){if(r&1&&u(0,"img",18),r&2){let t=f();g("src",t.selectedImage,ee)}}function ni(r,e){r&1&&(a(0,"div",19),u(1,"div",20),s())}function ii(r,e){if(r&1){let t=D();a(0,"div",21)(1,"div",22)(2,"h2"),p(3,"Logout"),s(),a(4,"p"),p(5,"Are you sure you want to logout?"),s(),a(6,"div",23)(7,"button",24),b("click",function(){M(t);let i=f();return y(i.closeLogoutModal())}),p(8,"Cancel"),s(),a(9,"button",25),b("click",function(){M(t);let i=f();return y(i.logout())}),p(10,"Logout"),s()()()()}}var St=class r{constructor(e){this.profileService=e}selectedImage=null;showLogoutModal=!1;activeTab="profile";router=G(j);fullName="";isLoading=!1;profileSubscription=null;ngOnInit(){this.profileSubscription=this.profileService.currentProfile$.subscribe(t=>{t&&(this.fullName=t.fullName,this.selectedImage=t.profilePicture||null,console.log("Profile updated in component:",t))});let e=this.profileService.getCurrentProfile();e?(this.fullName=e.fullName,this.selectedImage=e.profilePicture||null):this.fetchProfileFromServer()}ngOnDestroy(){this.profileSubscription&&this.profileSubscription.unsubscribe()}fetchProfileFromServer(){this.isLoading=!0,this.profileService.fetchLatestProfile().subscribe({next:e=>{this.isLoading=!1,console.log("Fetched profile from server:",e)},error:e=>{this.isLoading=!1,console.error("Error fetching profile:",e)}})}setActiveTab(e){this.activeTab=e}openLogoutModal(){this.showLogoutModal=!0}closeLogoutModal(){this.showLogoutModal=!1}logout(){return J(this,null,function*(){localStorage.clear(),this.router.navigate(["/mobile/login"])})}static \u0275fac=function(t){return new(t||r)(P(Me))};static \u0275cmp=S({type:r,selectors:[["app-patient-profile"]],decls:31,vars:6,consts:[[1,"gradient-background"],[1,"profile-container"],[1,"profile-image-container"],[1,"image-preview"],["class","upload-placeholder",4,"ngIf"],["alt","Profile",3,"src",4,"ngIf"],["class","loading-overlay",4,"ngIf"],[1,"menu-items"],["routerLink","/mobile/patient-update-profile",1,"menu-item"],[1,"menu-icon"],[1,"bi","bi-person"],[1,"bi","bi-chevron-right","chevron"],["routerLink","/mobile/medical-record",1,"menu-item"],[1,"bi","bi-key"],[1,"menu-item",3,"click"],[1,"bi","bi-box-arrow-right"],["class","modal-overlay",4,"ngIf"],[1,"upload-placeholder"],["alt","Profile",3,"src"],[1,"loading-overlay"],[1,"spinner"],[1,"modal-overlay"],[1,"modal"],[1,"modal-buttons"],[1,"cancel-button",3,"click"],[1,"logout-button",3,"click"]],template:function(t,n){t&1&&(a(0,"div",0)(1,"div",1)(2,"header"),u(3,"app-navbar"),a(4,"h1"),p(5,"My Profile"),s()(),a(6,"div",2)(7,"div",3),x(8,ei,2,0,"div",4)(9,ti,1,1,"img",5)(10,ni,2,0,"div",6),s()(),a(11,"div",7)(12,"button",8)(13,"div",9),u(14,"i",10),s(),a(15,"span"),p(16,"Update Profile"),s(),u(17,"i",11),s(),a(18,"button",12)(19,"div",9),u(20,"i",13),s(),a(21,"span"),p(22,"View Medical Records"),s(),u(23,"i",11),s(),a(24,"button",14),b("click",function(){return n.openLogoutModal()}),a(25,"div",9),u(26,"i",15),s(),a(27,"span"),p(28,"Logout"),s(),u(29,"i",11),s()(),x(30,ii,11,0,"div",16),s()()),t&2&&(c(7),A("has-image",n.selectedImage),c(),g("ngIf",!n.selectedImage),c(),g("ngIf",n.selectedImage),c(),g("ngIf",n.isLoading),c(20),g("ngIf",n.showLogoutModal))},dependencies:[V,U,Ce,$,H],styles:[".profile-container[_ngcontent-%COMP%]{width:100%;max-width:800px;margin:0 auto;background-color:#fff;min-height:100vh;padding:clamp(15px,4vw,30px);box-sizing:border-box;font-family:Segoe UI,Arial,sans-serif}header[_ngcontent-%COMP%]{display:flex;align-items:flex-end;margin-bottom:20px;width:100%;justify-content:center}h1[_ngcontent-%COMP%]{flex-grow:1;text-align:center;font-size:clamp(16px,4vw,20px);margin:30px 0 0;font-weight:600;color:#333;justify-content:center}.profile-image-container[_ngcontent-%COMP%]{position:relative;display:flex;justify-content:center;margin-bottom:30px;margin-top:30px}.image-preview[_ngcontent-%COMP%]{width:clamp(120px,30vw,150px);height:clamp(120px,30vw,150px);border-radius:50%;border:2px dashed #199A8E;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color:#f8f9fa;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 6px #0000001a}.image-preview.has-image[_ngcontent-%COMP%]{border:2px solid #199A8E}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;background-color:#e6f7f5}.upload-placeholder[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;color:#6c757d;font-size:clamp(12px,3vw,14px)}.upload-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(24px,6vw,32px)}.edit-icon[_ngcontent-%COMP%]{position:absolute;bottom:5px;right:calc(50% - clamp(50px,12vw,65px));background:#199a8e;color:#fff;border:none;border-radius:50%;width:clamp(28px,7vw,35px);height:clamp(28px,7vw,35px);display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:0 2px 4px #0003;transition:transform .2s}.edit-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1)}#file-upload[_ngcontent-%COMP%]{display:none}.loading-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;display:flex;align-items:center;justify-content:center;background-color:#ffffffb3;border-radius:50%}.spinner[_ngcontent-%COMP%]{width:30px;height:30px;border:3px solid rgba(25,154,142,.3);border-radius:50%;border-top-color:#199a8e;animation:_ngcontent-%COMP%_spin 1s ease-in-out infinite}@keyframes _ngcontent-%COMP%_spin{to{transform:rotate(360deg)}}.menu-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:clamp(10px,2.5vh,15px);margin-top:clamp(15px,4vh,30px);padding:0 clamp(5px,1.5vw,10px)}.menu-item[_ngcontent-%COMP%]{display:flex;align-items:center;background:#fff;border:none;padding:clamp(12px,3vh,15px);cursor:pointer;width:100%;border-radius:12px;box-shadow:0 2px 4px #0000000d;transition:transform .2s}.menu-item[_ngcontent-%COMP%]:hover{transform:translate(5px)}.menu-icon[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff;width:clamp(32px,8vw,40px);height:clamp(32px,8vw,40px);border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:clamp(10px,2.5vw,15px)}.menu-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(14px,3.5vw,18px)}.menu-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{flex-grow:1;text-align:left;font-size:clamp(14px,3.5vw,16px);color:#333;font-weight:500}.chevron[_ngcontent-%COMP%]{color:#199a8e;font-size:clamp(12px,3vw,14px)}.modal-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;display:flex;align-items:flex-end;justify-content:center;animation:_ngcontent-%COMP%_fadeIn .3s ease;z-index:1000}.modal[_ngcontent-%COMP%]{background:#fff;width:100%;max-width:500px;padding:clamp(20px,5vw,30px);border-radius:clamp(20px,5vw,27px) clamp(20px,5vw,27px) 0 0;text-align:center;animation:_ngcontent-%COMP%_slideUp .3s ease;margin-bottom:80px}.modal[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:clamp(16px,4vw,20px);margin-bottom:clamp(8px,2vh,10px);color:#333}.modal[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:clamp(20px,5vh,25px);font-size:clamp(14px,3.5vw,16px)}.modal-buttons[_ngcontent-%COMP%]{display:flex;gap:clamp(10px,2.5vw,15px);justify-content:center;flex-wrap:wrap}.cancel-button[_ngcontent-%COMP%], .logout-button[_ngcontent-%COMP%]{padding:clamp(10px,2.5vh,12px) clamp(20px,5vw,30px);border-radius:25px;font-size:clamp(14px,3.5vw,16px);cursor:pointer;transition:all .3s ease;min-width:clamp(100px,25vw,120px);font-weight:500}.cancel-button[_ngcontent-%COMP%]{border:1px solid #199A8E;background:#fff;color:#199a8e}.cancel-button[_ngcontent-%COMP%]:hover{background:#f0f9f8}.logout-button[_ngcontent-%COMP%]{border:1px solid #199A8E;background:#199a8e;color:#fff}.logout-button[_ngcontent-%COMP%]:hover{background:#148277}.bottom-nav[_ngcontent-%COMP%]{position:fixed;bottom:clamp(15px,4vh,20px);left:0;width:100%;display:flex;justify-content:space-around;padding:clamp(8px,2vh,10px);background-color:#fff;box-shadow:0 -2px 5px #0000001a;z-index:999}.nav-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(6px,1.5vw,8px);background:none;border:none;color:silver;font-size:clamp(10px,2.5vw,12px);cursor:pointer;padding:clamp(6px,1.5vh,8px) clamp(12px,3vw,16px);border-radius:20px}.nav-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(16px,4vw,20px)}.nav-button.active[_ngcontent-%COMP%]{color:#00a693;background-color:#e6f7f5}.nav-label[_ngcontent-%COMP%]{font-size:clamp(12px,3vw,14px)}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_slideUp{0%{transform:translateY(100%)}to{transform:translateY(0)}}@media (min-width: 768px){.profile-container[_ngcontent-%COMP%]{padding:30px;margin-top:20px;border-radius:20px;box-shadow:0 4px 6px #0000001a}.menu-items[_ngcontent-%COMP%]{max-width:600px;margin:30px auto}.modal[_ngcontent-%COMP%]{margin-bottom:100px}}@media (max-height: 600px){.profile-image-container[_ngcontent-%COMP%]{margin-bottom:15px}.menu-items[_ngcontent-%COMP%]{gap:8px}.bottom-nav[_ngcontent-%COMP%]{padding:6px}}"]})};function oi(r,e){r&1&&(a(0,"div",19),u(1,"i",20),a(2,"span"),p(3,"Upload Photo"),s()())}function ri(r,e){if(r&1&&u(0,"img",21),r&2){let t=f();g("src",t.selectedImage,ee)}}function ai(r,e){r&1&&(a(0,"span"),p(1,"Update Profile"),s())}function si(r,e){r&1&&(a(0,"span"),u(1,"i",22),p(2," Updating... "),s())}function li(r,e){if(r&1){let t=D();a(0,"div",23)(1,"div",24)(2,"div",25)(3,"div",26),Ie(),a(4,"svg",27),u(5,"polyline",28),s()(),He(),a(6,"h2",29),p(7,"Update Successful"),s(),a(8,"p",30),p(9," Your Profile has been updated "),s(),a(10,"button",31),b("click",function(){M(t);let i=f();return y(i.closeSuccessModal())}),p(11," Done "),s()()()()}}var It=class r{constructor(e,t,n){this.router=e;this.profileService=t;this.authService=n}profileImage=null;selectedImage=null;showSuccessModal=!1;isLoading=!1;refreshSubscription=null;profileData={fullName:"",phoneNumber:"",email:""};ngOnInit(){this.isLoading=!0,this.profileService.fetchProfileFromFirebase().subscribe({next:e=>{e?(console.log("Profile loaded from Firebase:",e),this.profileData=I({},e),this.selectedImage=e.profilePicture||null,this.isLoading=!1):this.loadFromLocalStorage()},error:e=>{console.error("Error loading profile from Firebase:",e),this.loadFromLocalStorage()}}),this.startPeriodicRefresh()}ngOnDestroy(){this.stopPeriodicRefresh()}startPeriodicRefresh(){this.refreshSubscription=qt(3e4).pipe(B(()=>this.profileService.fetchProfileFromFirebase())).subscribe({next:e=>{if(e&&!this.isLoading){console.log("Periodic refresh from Firebase:",e);let t=JSON.stringify(this.profileData),n=JSON.stringify(e);t!==n&&(console.log("Profile data has changed in Firebase, updating local view"),this.profileData=I({},e),this.selectedImage=e.profilePicture||null)}},error:e=>{console.error("Error during periodic refresh:",e)}})}stopPeriodicRefresh(){this.refreshSubscription&&(this.refreshSubscription.unsubscribe(),this.refreshSubscription=null)}loadFromLocalStorage(){let e=this.profileService.getCurrentProfile();e?(console.log("Profile loaded from local storage:",e),this.profileData=I({},e),this.selectedImage=e.profilePicture||null):this.createProfileFromAuth(),this.isLoading=!1}createProfileFromAuth(){this.authService.getCurrentUser().then(e=>{if(e){let t=this.authService.getUserInfo(),n=e.profileImage===void 0?null:e.profileImage,i={id:e.id,fullName:e.displayName||"",email:e.email||"",phoneNumber:t?.phoneNumber||"",profilePicture:n,address:t?.address||"",role:t?.role||"PATIENT"};console.log("Created profile from auth data:",i),this.profileData=i,this.selectedImage=i.profilePicture||null,this.saveProfileToFirebase(i)}else console.error("No user found in auth service")}).catch(e=>{console.error("Error getting current user:",e)})}onFileSelected(e){let t=e.target.files[0];if(t){if(!t.type.startsWith("image/")){alert("Please select an image file");return}let n=5*1024*1024;if(t.size>n){alert("Image size should be less than 5MB");return}let i=new FileReader;i.onload=o=>{let l=o.target.result;this.selectedImage=l,console.log("Image loaded as base64"),this.isLoading=!0,this.profileService.updateProfilePicture(l).subscribe({next:d=>{console.log("Profile picture updated successfully:",d),this.authService.updateUserInfoFromProfile(d),this.profileData=d,this.isLoading=!1},error:d=>{console.error("Failed to update profile picture",d),this.isLoading=!1,this.profileData.id?(this.profileData.profilePicture=l,this.saveProfileToFirebase(F(I({},this.profileData),{profilePicture:l}))):this.createLocalProfile(l),console.log("Continuing with local profile picture changes")}})},i.onerror=()=>{alert("Error reading file")},i.readAsDataURL(t)}}triggerFileInput(){let e=document.getElementById("file-upload");e&&e.click()}updateProfile(){if(this.isLoading=!0,!this.profileData.firstName||!this.profileData.lastName){let e=this.profileData.fullName.split(" ");this.profileData.firstName=e[0]||"",this.profileData.lastName=e.length>1?e.slice(1).join(" "):""}this.profileData.role||(this.profileData.role="PATIENT"),this.profileService.updateProfile(this.profileData).subscribe({next:e=>{console.log("Profile updated successfully:",e),this.authService.updateUserInfoFromProfile(F(I({},e),{full_name:e.fullName,firstName:e.firstName,lastName:e.lastName,address:e.address,role:e.role})),this.isLoading=!1,this.showSuccessModal=!0},error:e=>{console.error("Error updating profile via service:",e),this.saveProfileToFirebase(this.profileData),this.isLoading=!1,alert("Profile saved locally. Some features may be limited until you reconnect.")}})}closeSuccessModal(){this.showSuccessModal=!1,this.router.navigate(["/mobile/patient-dashboard"])}saveProfileToFirebase(e){try{let t=dt();if(!t){console.error("Firestore not available");return}this.authService.getCurrentUser().then(n=>{if(!n||!n.id){console.error("User ID not available for Firebase save");return}let i=this.authService.getUserInfo();console.log("Attempting to save profile to Firebase for user:",n.id);let o=W(t,"patients",n.id),l=e.fullName.split(" "),d=l[0]||"",m=l.length>1?l.slice(1).join(" "):"",h=new Date().toISOString(),_={fullName:e.fullName,firstName:d,lastName:m,email:e.email,phoneNumber:e.phoneNumber||"",profilePicture:e.profilePicture||null,address:e.address||i?.address||"",role:e.role||i?.role||"PATIENT",updatedAt:lt(),createdAt:e.id?void 0:h};console.log("Saving complete profile data to Firebase:",_),Oe(o,_,{merge:!0}).then(()=>{console.log("Successfully saved profile to Firebase"),this.profileData=F(I({},this.profileData),{id:n.id,firstName:d,lastName:m,address:_.address,role:_.role}),this.authService.updateUserInfoFromProfile(F(I({},this.profileData),{full_name:e.fullName,firstName:d,lastName:m,address:_.address,role:_.role}))}).catch(N=>{console.error("Error saving profile to Firebase:",N)})}).catch(n=>{console.error("Error getting current user for Firebase save:",n)})}catch(t){console.error("Error initializing Firestore:",t)}}createLocalProfile(e){this.authService.getCurrentUser().then(t=>{if(t){let n=this.authService.getUserInfo(),i=t.displayName||n?.full_name||"",o=i.split(" "),l=o[0]||"",d=o.length>1?o.slice(1).join(" "):"";this.profileData={id:t.id,fullName:i,firstName:l,lastName:d,email:t.email||n?.email||"",phoneNumber:n?.phoneNumber||"",profilePicture:e,address:n?.address||"",role:n?.role||"PATIENT",createdAt:new Date().toISOString()},console.log("Created local profile:",this.profileData),this.saveProfileToFirebase(this.profileData)}else console.error("No authenticated user to create local profile")}).catch(t=>{console.error("Error getting current user:",t)})}static \u0275fac=function(t){return new(t||r)(P(j),P(Me),P(q))};static \u0275cmp=S({type:r,selectors:[["app-patient-update-profile"]],decls:29,vars:11,consts:[["fileInput",""],[1,"profile-container"],["routerLink","/mobile/patient-profile"],[1,"content"],[1,"profile-image-container"],["for","file-upload",1,"image-preview",3,"click"],["class","upload-placeholder",4,"ngIf"],["alt","Profile",3,"src",4,"ngIf"],["for","file-upload",1,"edit-icon"],[1,"bi","bi-camera"],["type","file","id","file-upload","accept","image/*",3,"change"],[1,"profile-form",3,"ngSubmit"],[1,"form-group"],["type","text","name","fullName","required","",1,"form-input",3,"ngModelChange","ngModel"],["type","tel","name","phoneNumber","required","",1,"form-input",3,"ngModelChange","ngModel"],["type","email","name","email","required","",1,"form-input",3,"ngModelChange","ngModel"],["type","submit",1,"update-button",3,"disabled"],[4,"ngIf"],["class","modal-overlay",4,"ngIf"],[1,"upload-placeholder"],[1,"bi","bi-person"],["alt","Profile",3,"src"],[1,"bi","bi-arrow-repeat","spinning"],[1,"modal-overlay"],[1,"modal-content"],[1,"modal-body"],[1,"checkmark-circle"],["width","32","height","32","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["points","20 6 9 17 4 12"],[1,"modal-title"],[1,"modal-subtitle"],[1,"done-button",3,"click"]],template:function(t,n){if(t&1){let i=D();a(0,"div",1),u(1,"app-back-button",2)(2,"app-navbar"),a(3,"div",3)(4,"div",4)(5,"label",5),b("click",function(){return M(i),y(n.triggerFileInput())}),x(6,oi,4,0,"div",6)(7,ri,1,1,"img",7),s(),a(8,"label",8),u(9,"i",9),s(),a(10,"input",10,0),b("change",function(l){return M(i),y(n.onFileSelected(l))}),s()(),a(12,"form",11),b("ngSubmit",function(){return M(i),y(n.updateProfile())}),a(13,"div",12)(14,"label"),p(15,"Full Name"),s(),a(16,"input",13),ge("ngModelChange",function(l){return M(i),me(n.profileData.fullName,l)||(n.profileData.fullName=l),y(l)}),s()(),a(17,"div",12)(18,"label"),p(19,"Phone Number"),s(),a(20,"input",14),ge("ngModelChange",function(l){return M(i),me(n.profileData.phoneNumber,l)||(n.profileData.phoneNumber=l),y(l)}),s()(),a(21,"div",12)(22,"label"),p(23,"Email"),s(),a(24,"input",15),ge("ngModelChange",function(l){return M(i),me(n.profileData.email,l)||(n.profileData.email=l),y(l)}),s()(),a(25,"button",16),x(26,ai,2,0,"span",17)(27,si,3,0,"span",17),s()(),x(28,li,12,0,"div",18),s()()}t&2&&(c(5),A("has-image",n.selectedImage),c(),g("ngIf",!n.selectedImage),c(),g("ngIf",n.selectedImage),c(9),de("ngModel",n.profileData.fullName),c(4),de("ngModel",n.profileData.phoneNumber),c(4),de("ngModel",n.profileData.email),c(),g("disabled",n.isLoading),c(),g("ngIf",!n.isLoading),c(),g("ngIf",n.isLoading),c(),g("ngIf",n.showSuccessModal))},dependencies:[V,U,Ce,Re,oe,re,Fe,xn,ze,hn,H,Q,$,ie],styles:[".profile-container[_ngcontent-%COMP%]{max-width:480px;margin:0 auto;background-color:#fff;min-height:100vh;box-sizing:border-box;font-family:Arial,sans-serif;position:relative;padding:0}.content[_ngcontent-%COMP%]{padding:20px;margin-top:15px}.back-button[_ngcontent-%COMP%]{background:none;border:none;font-size:24px;color:#199a8e;cursor:pointer;padding:0}.modal-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.modal-content[_ngcontent-%COMP%]{background:#fff;border-radius:16px;padding:24px;width:90%;max-width:400px;animation:_ngcontent-%COMP%_slideUp .3s ease-out}.modal-body[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;text-align:center}.checkmark-circle[_ngcontent-%COMP%]{width:80px;height:80px;background-color:#f0fdfa;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:16px}.checkmark-circle[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{color:#14b8a6}.modal-title[_ngcontent-%COMP%]{font-size:24px;font-weight:600;margin:0 0 8px;color:#333}.modal-subtitle[_ngcontent-%COMP%]{font-size:16px;color:#666;margin:0 0 24px}.done-button[_ngcontent-%COMP%]{width:100%;padding:16px;background-color:#14b8a6;color:#fff;border:none;border-radius:25px;font-size:16px;font-weight:700;cursor:pointer;transition:background-color .2s}.profile-image-container[_ngcontent-%COMP%]{position:relative;display:flex;justify-content:center;margin-bottom:30px}.image-preview[_ngcontent-%COMP%]{width:150px;height:150px;border-radius:50%;border:2px dashed #199A8E;display:flex;align-items:center;justify-content:center;overflow:hidden;background-color:#f8f9fa;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 6px #0000001a}.image-preview.has-image[_ngcontent-%COMP%]{border:2px solid #199A8E}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.upload-placeholder[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:.5rem;color:#6c757d}.upload-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:32px}.edit-icon[_ngcontent-%COMP%]{position:absolute;bottom:5px;right:calc(50% - 65px);background:#199a8e;color:#fff;border:none;border-radius:50%;width:35px;height:35px;display:flex;align-items:center;justify-content:center;cursor:pointer;box-shadow:0 2px 4px #0003;transition:transform .2s}.edit-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1)}#file-upload[_ngcontent-%COMP%]{display:none}.profile-form[_ngcontent-%COMP%]{margin:0 auto;display:flex;flex-direction:column;gap:20px;max-width:334px}.form-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:14px;color:#333}.form-input[_ngcontent-%COMP%]{padding:12px;border:1px solid #00A693;border-radius:100px;font-size:16px;color:#333;background-color:#f9fcff}.form-input[_ngcontent-%COMP%]:focus{outline:none;border-color:#00a693;background-color:#e9f6fe}.update-button[_ngcontent-%COMP%]{background-color:#00a693;color:#fff;border:none;border-radius:100px;padding:15px;font-size:16px;cursor:pointer;margin-top:40px;transition:background-color .3s}.update-button[_ngcontent-%COMP%]:hover:not([disabled]){background-color:#008577}.update-button[disabled][_ngcontent-%COMP%]{background-color:#a0a0a0;cursor:not-allowed}@keyframes _ngcontent-%COMP%_slideUp{0%{transform:translateY(50px);opacity:0}to{transform:translateY(0);opacity:1}}.spinning[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s infinite linear;display:inline-block}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 480px){.image-preview[_ngcontent-%COMP%]{width:120px;height:120px}.edit-icon[_ngcontent-%COMP%]{right:calc(50% - 50px);width:30px;height:30px}.modal-content[_ngcontent-%COMP%]{width:95%;padding:20px}}"]})};var pi=["datesContainer"],di=(r,e)=>e.title,mi=(r,e)=>e.appointmentId;function gi(r,e){r&1&&(a(0,"div",4),u(1,"i",20),s())}function ui(r,e){if(r&1){let t=D();a(0,"button",27),b("click",function(){M(t);let i=f(2);return y(i.navigateToBooking())}),p(1),s()}if(r&2){let t=f().$implicit;c(),O(t.buttonText)}}function fi(r,e){if(r&1&&u(0,"img",26),r&2){let t=f().$implicit;g("src",t.image,ee)("alt",t.title)}}function hi(r,e){if(r&1&&(a(0,"div",21)(1,"div",22)(2,"div",23)(3,"h5"),p(4),s(),a(5,"p",24),p(6),s(),x(7,ui,2,1,"button",25),s(),x(8,fi,1,2,"img",26),s()()),r&2){let t=e.$implicit,n=e.$index,i=f();A("active",n===i.currentSlideIndex),c(4),O(t.title),c(2),O(t.description),c(),te(t.buttonText?7:-1),c(),te(t.image?8:-1)}}function bi(r,e){if(r&1&&(Ae(0),a(1,"span",28),p(2),s(),De()),r&2){let t=f();c(2),T("",t.allAppointments.length," appointments")}}function xi(r,e){if(r&1&&(a(0,"span",43),u(1,"i",44),p(2),s()),r&2){let t=f().$implicit;c(2),T(" ",t.specialization," ")}}function _i(r,e){if(r&1&&(a(0,"span",45),u(1,"i",46),p(2),en(3,"slice"),s()),r&2){let t=f().$implicit;c(2),We(" ",tn(3,2,t.reasonForVisit,0,20),"",t.reasonForVisit.length>20?"...":""," ")}}function vi(r,e){if(r&1){let t=D();a(0,"div",30),b("click",function(){let i=M(t).$implicit,o=f(2);return y(o.navigateToAppointmentDetails(i))}),a(1,"div",31)(2,"span",32),p(3),s(),a(4,"span",33),p(5),s()(),a(6,"div",34)(7,"h3",35),p(8),s(),a(9,"div",36)(10,"span",37),u(11,"i",38),p(12),s(),x(13,xi,3,1,"span",39)(14,_i,4,6,"span",40),s()(),a(15,"div",41)(16,"span",42),p(17),s()()()}if(r&2){let t=e.$implicit,n=f(2);c(3),O(n.getMonthShort(t.appointmentDate)),c(2),O(n.getDayOfMonth(t.appointmentDate)),c(3),T("Dr. ",t.doctorName,""),c(4),T(" ",n.formatTime(t.appointmentTime)," "),c(),g("ngIf",t.specialization),c(),g("ngIf",t.reasonForVisit),c(2),ke("status-"+t.status.toLowerCase()),c(),T(" ",t.status," ")}}function Ci(r,e){if(r&1&&(a(0,"div",17),ce(1,vi,18,9,"div",29,mi),s()),r&2){let t=f();c(),pe(t.allAppointments)}}function Pi(r,e){if(r&1){let t=D();a(0,"div",18)(1,"div",47),u(2,"i",48),a(3,"p"),p(4,"No appointments found in Firebase"),s(),a(5,"button",49),b("click",function(){M(t);let i=f();return y(i.navigateToBooking())}),p(6,"Book an Appointment"),s()()()}}var kt=class r{constructor(e,t,n,i){this.appointmentService=e;this.router=t;this.profileService=n;this.storage=i;this.currentMonth=new Date().toLocaleString("default",{month:"long"})}datesContainer;authService=G(q);userName="";profileImage=null;profileSubscription;authSubscription;currentSlideIndex=0;slides=[{title:"Book your appointment",description:"Convenient, accessible, efficient healthcare scheduling online platform!",image:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Vc0Pzs3DNZSgP5C6q7kjk8g6nRLyPJ.png",buttonText:"Book Now"},{title:"Mental Health Awareness",description:"Your Health, Your Wealth Regular Checkups Save Lives!",image:"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/image-Vc0Pzs3DNZSgP5C6q7kjk8g6nRLyPJ.png",buttonText:"Book Now"}];dates=[];selectedDate=null;currentMonth="";currentAppointment=null;upcomingAppointments=[];appointmentsMap={};allAppointments=[];times=["09:00 AM","10:00 AM","11:00 AM","12:00 PM","13:00 PM","14:00 PM","15:00 PM","16:00 PM","17:00 PM"];loadUserProfile(){return J(this,null,function*(){try{let e=yield this.authService.getCurrentUser();if(e){console.log("User data from Firebase:",e),this.userName=e.displayName||"",this.profileImage=e.profileImage,e.displayName&&this.storage.setItem("user_displayname",e.displayName);return}let t=this.profileService.getCurrentProfile();if(t){console.log("Profile data from profile service:",t),this.userName=t.fullName,this.profileImage=t.profilePicture||null;return}let n=this.storage.getItem("user_displayname");n?(console.log("Username from storage:",n),this.userName=n):(console.warn("No user data found from any source"),this.router.navigate(["/mobile/login"]))}catch(e){console.error("Error loading user profile:",e)}})}ngOnInit(){this.loadUserProfile(),this.generateDates(),this.loadAppointments(),this.startSlideshow(),this.appointmentService.appointments$.subscribe(()=>{this.loadAppointments()}),this.profileSubscription=this.profileService.currentProfile$.subscribe(e=>{e&&(console.log("Profile updated in dashboard:",e),this.userName=e.fullName,this.profileImage=e.profilePicture||null)}),this.authSubscription=this.authService.currentUser$.subscribe(e=>{e&&(console.log("Firebase user updated:",e),this.userName=e.displayName,this.profileImage=e.profileImage,e.displayName&&this.storage.setItem("user_displayname",e.displayName))})}ngOnDestroy(){this.profileSubscription&&this.profileSubscription.unsubscribe(),this.authSubscription&&this.authSubscription.unsubscribe()}startSlideshow(){setInterval(()=>{this.nextSlide()},5e3)}nextSlide(){this.currentSlideIndex=(this.currentSlideIndex+1)%this.slides.length}previousSlide(){this.currentSlideIndex=(this.currentSlideIndex-1+this.slides.length)%this.slides.length}navigateToBooking(){this.router.navigate(["/mobile/appointment-booking"])}navigateToAppointmentDetails(e){this.router.navigate(["/mobile/appointment-details"],{state:{appointment:e}})}generateDates(){let e=new Date;for(let t=-3;t<4;t++){let n=new Date(e);n.setDate(e.getDate()+t),this.dates.push({day:n.getDate(),weekday:n.toLocaleString("en-US",{weekday:"short"}),fullDate:n.toISOString().split("T")[0]})}}loadAppointments(){console.log("Loading appointments for patient dashboard from Firebase..."),this.appointmentService.refreshAppointments().subscribe({next:e=>{console.log(`Successfully loaded ${e.length} appointments from Firebase`),this.currentAppointment=this.appointmentService.getCurrentAppointment(),this.upcomingAppointments=this.appointmentService.getUpcomingAppointments(),console.log("Upcoming appointments loaded:",this.upcomingAppointments.length),this.allAppointments=e,console.log("All appointments loaded from Firebase:",this.allAppointments.length),this.allAppointments.length===0?console.log("No appointments found in Firebase for this patient."):(console.log("Appointment statuses from Firebase:",this.allAppointments.map(t=>t.status).join(", ")),console.log("First appointment details:",JSON.stringify(this.allAppointments[0]))),this.allAppointments.sort((t,n)=>{let i=new Date(t.appointmentDate);return new Date(n.appointmentDate).getTime()-i.getTime()}),this.populateAppointmentsMap(),this.selectCurrentDate()},error:e=>{console.error("Error refreshing appointments from Firebase:",e),this.allAppointments=this.appointmentService.getAllAppointments(),console.log("Fallback loaded",this.allAppointments.length,"appointments"),this.populateAppointmentsMap(),this.selectCurrentDate()}})}populateAppointmentsMap(){this.appointmentsMap={},this.dates.forEach(e=>{let t=this.appointmentService.getAppointmentsByDate(new Date(e.fullDate));t.length>0&&(console.log(`Found ${t.length} appointments for ${e.fullDate}`),this.appointmentsMap[e.fullDate]=t.sort((n,i)=>{let o=this.parseTime(n.appointmentTime).getTime(),l=this.parseTime(i.appointmentTime).getTime();return o-l}))})}selectCurrentDate(){if(this.currentAppointment){let e=this.currentAppointment.appointmentDate;this.selectedDate=this.dates.find(t=>new Date(t.fullDate).getDate()===new Date(e).getDate())||null}else this.selectedDate=this.dates.find(e=>new Date(e.fullDate).getDate()===new Date().getDate())||null}getGroupedAppointments(e){let t=this.appointmentsMap[e]||[],n=new Map;return t.forEach(i=>{let o=n.get(i.appointmentTime)||[];n.set(i.appointmentTime,[...o,i])}),Array.from(n.entries()).map(([i,o])=>({time:i,appointments:o})).sort((i,o)=>{let l=this.parseTime(i.time).getTime(),d=this.parseTime(o.time).getTime();return l-d})}selectDate(e){this.selectedDate=e}hasAppointmentsForDate(e){return!!(this.appointmentsMap[e]&&this.appointmentsMap[e].length>0)}parseTime(e){let t=new Date;if(e.includes("AM")||e.includes("PM")){let[o,l]=e.split(" "),[d,m]=o.split(":").map(Number);return l==="PM"&&d<12?d+=12:l==="AM"&&d===12&&(d=0),t.setHours(d,m,0,0),t}let[n,i]=e.split(":").map(Number);return t.setHours(n,i,0,0),t}hasAppointmentAt(e){return this.selectedDate&&this.appointmentsMap[this.selectedDate.fullDate]?.some(t=>t.appointmentTime===e)||!1}getAppointmentAt(e){return this.selectedDate&&this.appointmentsMap[this.selectedDate.fullDate]?.find(t=>t.appointmentTime===e)||null}formatTime(e){if(!e)return"";if(e.includes("AM")||e.includes("PM"))return e;let[t,n]=e.split(":").map(Number),i=t>=12?"PM":"AM";return`${t%12||12}:${n.toString().padStart(2,"0")} ${i}`}getStatusColor(e){switch(e){case"Pending":return"#eab308";case"Approved":return"#22c55e";case"Rejected":return"#ef4444";case"Completed":return"#199a8e";case"Cancelled":return"#6b7280";default:return"#6b7280"}}getStatusClass(e){switch(e){case"Approved":return"status-approved";case"Pending":return"status-pending";case"Completed":return"status-completed";case"Cancelled":case"Rejected":return"status-cancelled";case"Available":return"status-available";default:return""}}getMonthShort(e){return new Date(e).toLocaleString("default",{month:"short"})}getDayOfMonth(e){return new Date(e).getDate()}static \u0275fac=function(t){return new(t||r)(P(le),P(j),P(Me),P(ht))};static \u0275cmp=S({type:r,selectors:[["app-patient-dashboard"]],viewQuery:function(t,n){if(t&1&&Yt(pi,5),t&2){let i;Xt(i=Qt())&&(n.datesContainer=i.first)}},decls:27,vars:7,consts:[[1,"dashboard-container"],[1,"header"],[1,"profile-pic-container"],["alt","",1,"profile-pic",3,"src"],[1,"profile-pic-placeholder"],[1,"welcome-container"],[1,"welcome-text"],[1,"patient-name"],[1,"slideshow-container"],[1,"slides"],[1,"slide",3,"active"],[1,"all-appointments-section"],[1,"section-header"],[1,"section-title"],[1,"section-actions"],[4,"ngIf"],[1,"appointments-container"],[1,"appointment-list"],[1,"no-appointments-container"],[1,"development-tools-section",2,"margin-top","2rem","padding","0 1rem"],[1,"bi","bi-person"],[1,"slide"],[1,"slide-content"],[1,"slide-info"],[1,"slide-text"],[1,"slide-button"],[1,"slide-image",3,"src","alt"],[1,"slide-button",3,"click"],[1,"appointment-count"],[1,"booked-appointment-card"],[1,"booked-appointment-card",3,"click"],[1,"appointment-date-badge"],[1,"month"],[1,"day"],[1,"appointment-details"],[1,"doctor-name"],[1,"appointment-info"],[1,"time"],[1,"bi","bi-clock"],["class","specialization",4,"ngIf"],["class","reason",4,"ngIf"],[1,"appointment-status-container"],[1,"appointment-status-badge"],[1,"specialization"],[1,"bi","bi-briefcase-medical"],[1,"reason"],[1,"bi","bi-chat-left-text"],[1,"empty-state"],[1,"bi","bi-calendar-x","empty-icon"],[1,"book-now-btn",3,"click"]],template:function(t,n){t&1&&(u(0,"app-firebase-reset-button"),a(1,"div",0),u(2,"app-navbar"),a(3,"header",1)(4,"div",2),u(5,"img",3),x(6,gi,2,0,"div",4),s(),a(7,"div",5)(8,"h1",6),p(9,"Welcome"),s(),a(10,"p",7),p(11),s()()(),a(12,"div",8)(13,"div",9),ce(14,hi,9,6,"div",10,di),s()(),a(16,"div",11)(17,"div",12)(18,"h2",13),p(19,"All Booked Appointments"),s(),a(20,"div",14),x(21,bi,3,1,"ng-container",15),s()(),a(22,"div",16),x(23,Ci,3,0,"div",17)(24,Pi,7,0,"div",18),s()(),a(25,"div",19),u(26,"app-firebase-reset"),s()()),t&2&&(c(5),g("src",n.profileImage||"",ee),c(),te(n.profileImage?-1:6),c(5),O(n.userName),c(2),Ze("transform","translateX(-"+n.currentSlideIndex*100+"%)"),c(),pe(n.slides),c(7),g("ngIf",n.allAppointments&&n.allAppointments.length>0),c(2),te(n.allAppointments&&n.allAppointments.length>0?23:24))},dependencies:[V,U,nn,ie,H,Cn,Pn],styles:['.dashboard-container[_ngcontent-%COMP%]{padding:20px;font-family:Raleway,sans-serif;background-color:#fff;max-width:100%;width:100%;margin:0 auto;min-height:100vh}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px 0;flex-direction:row-reverse}.welcome-text[_ngcontent-%COMP%]{font-weight:150;font-size:20px;color:#000;margin:0 0 8px}.patient-name[_ngcontent-%COMP%]{font-weight:400;font-size:14px;color:#40b3a2;margin:0}.profile-pic-container[_ngcontent-%COMP%]{position:relative;width:40px;height:40px;border-radius:50%;overflow:hidden;box-shadow:0 2px 4px #0000001a;background-color:#efebeb}.profile-pic[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:50%}.profile-pic-placeholder[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:#efebeb;border-radius:50%;color:#40b3a2}.profile-pic-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:24px}.slideshow-container[_ngcontent-%COMP%]{position:relative;width:100%;overflow:hidden;border-radius:15px;margin:20px 0}.slides[_ngcontent-%COMP%]{display:flex;transition:transform .5s ease-in-out}.slide[_ngcontent-%COMP%]{min-width:100%;background:#40b3a2;padding:24px;color:#fff;height:180px;border-radius:15px}.slide-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;height:100%;position:relative}.slide-info[_ngcontent-%COMP%]{max-width:70%;z-index:2}.slide[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-size:13px;font-weight:600;margin:0 0 8px;text-transform:uppercase;letter-spacing:.5px}.slide-text[_ngcontent-%COMP%]{font-size:14px;opacity:.9;margin-bottom:15px;line-height:1.4;max-width:90%}.slide-button[_ngcontent-%COMP%]{background:#fff;color:#40b3a2;border:none;border-radius:25px;padding:8px 20px;font-weight:500;font-size:14px;cursor:pointer;transition:all .3s ease;margin-top:10px}.slide-button[_ngcontent-%COMP%]:hover{background:#ffffffe6}.slide-image[_ngcontent-%COMP%]{position:absolute;right:-20px;top:50%;transform:translateY(-50%);height:180px;object-fit:contain;z-index:1}.schedule-section[_ngcontent-%COMP%]{background-color:#40b3a20d;border-radius:20px;padding:20px;margin-top:20px}.schedule-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.schedule-title[_ngcontent-%COMP%]{color:#40b3a2;font-size:18px;font-weight:500;margin:0}.schedule-month[_ngcontent-%COMP%]{color:#666;font-size:14px}.dates-wrapper[_ngcontent-%COMP%]{background-color:#fff;border-radius:15px;padding:10px;margin-bottom:15px}.dates-scrollable[_ngcontent-%COMP%]{display:flex;gap:15px;padding:5px 0;overflow-x:auto;scrollbar-width:none;-ms-overflow-style:none}.dates-scrollable[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.date-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:8px;min-width:45px;cursor:pointer;border-radius:10px;transition:all .3s ease}.date-item[_ngcontent-%COMP%]   .date-day[_ngcontent-%COMP%]{font-size:16px;font-weight:500}.date-item[_ngcontent-%COMP%]   .date-weekday[_ngcontent-%COMP%]{font-size:12px;color:#666}.date-item.active[_ngcontent-%COMP%]{background:#40b3a2;color:#fff}.date-item.active[_ngcontent-%COMP%]   .date-weekday[_ngcontent-%COMP%]{color:#fffc}.date-item.has-appointment[_ngcontent-%COMP%]:after{content:"";display:block;width:4px;height:4px;background:#40b3a2;border-radius:50%;margin-top:4px}.schedule-grid[_ngcontent-%COMP%]{background-color:#fff;border-radius:15px;padding:15px}.appointments-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px}.time-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.time-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#40b3a2;padding:0 4px}.appointments-group[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px}.appointment-card[_ngcontent-%COMP%]{background:#40b3a21a;border-radius:8px;padding:12px 16px;display:flex;justify-content:space-between;align-items:center}.doctor-name[_ngcontent-%COMP%]{font-size:14px;color:#333;font-weight:500}.appointment-status[_ngcontent-%COMP%]{font-size:12px;padding:4px 12px;border-radius:12px;font-weight:500}.appointment-status.confirmed[_ngcontent-%COMP%]{background:#34d39933;color:#34d399}.appointment-status.cancelled[_ngcontent-%COMP%]{background:#ef444433;color:#ef4444}.no-appointments[_ngcontent-%COMP%]{color:#28b2a7;font-size:14px;text-align:center;padding:20px}.all-appointments-section[_ngcontent-%COMP%]{background-color:#40b3a20d;border-radius:20px;padding:20px;margin-top:30px;margin-bottom:70px}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.section-title[_ngcontent-%COMP%]{color:#30b3a2;font-size:18px;font-weight:600;margin:0}.section-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.view-all-btn[_ngcontent-%COMP%]{background:#40b3a233;border:none;color:#40b3a2;border-radius:20px;padding:7px 14px;font-size:13px;display:flex;align-items:center;gap:5px;font-weight:500;cursor:pointer;transition:all .3s ease}.view-all-btn[_ngcontent-%COMP%]:hover{background:#40b3a24d}.appointments-container[_ngcontent-%COMP%]{background-color:#fff;border-radius:15px;padding:5px}.appointment-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.booked-appointment-card[_ngcontent-%COMP%]{position:relative;border-radius:16px;background:#fff;padding:16px;margin-bottom:16px;display:flex;align-items:center;box-shadow:0 2px 4px #0000000d;border:1px solid #f3f4f6;transition:all .2s ease}.booked-appointment-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 6px #0000001a;border-color:#40b3a24d}.appointment-date-badge[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;background:#f9fafb;border-radius:12px;min-width:60px;min-height:60px;margin-right:16px;padding:8px}.month[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#6b7280;text-transform:uppercase}.day[_ngcontent-%COMP%]{font-size:18px;font-weight:700;color:#111827}.appointment-details[_ngcontent-%COMP%]{flex:1;min-width:0}.appointment-details[_ngcontent-%COMP%]   .doctor-name[_ngcontent-%COMP%]{font-size:14px;font-weight:600;color:#333;margin:0 0 5px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.appointment-info[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:12px;margin-top:4px}.appointment-info[_ngcontent-%COMP%]   .time[_ngcontent-%COMP%], .appointment-info[_ngcontent-%COMP%]   .specialization[_ngcontent-%COMP%], .appointment-info[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-size:12px;color:#666}.reason[_ngcontent-%COMP%]{max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.appointment-info[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:12px;color:#40b3a2}.appointment-status-container[_ngcontent-%COMP%]{display:flex;align-items:center}.appointment-status-badge[_ngcontent-%COMP%]{padding:4px 12px;border-radius:16px;font-size:12px;font-weight:600;display:inline-block;text-align:center;min-width:80px}.status-pending[_ngcontent-%COMP%]{background-color:#fef3c7;color:#d97706}.status-approved[_ngcontent-%COMP%]{background-color:#dcfce7;color:#16a34a}.status-completed[_ngcontent-%COMP%]{background-color:#dbeafe;color:#2563eb}.status-cancelled[_ngcontent-%COMP%], .status-rejected[_ngcontent-%COMP%]{background-color:#fee2e2;color:#dc2626}.status-available[_ngcontent-%COMP%]{background-color:#f3f4f6;color:#4b5563}.no-appointments-container[_ngcontent-%COMP%]{padding:30px 20px;text-align:center}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:10px}.empty-icon[_ngcontent-%COMP%]{font-size:32px;color:#ccc}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#888;margin:0}.book-now-btn[_ngcontent-%COMP%]{margin-top:15px;background:#40b3a2;color:#fff;border:none;padding:10px 20px;border-radius:25px;font-weight:500;cursor:pointer;transition:background .3s ease}.book-now-btn[_ngcontent-%COMP%]:hover{background:#389e8f}.appointment-status.pending[_ngcontent-%COMP%]{background:#fbbf2433;color:#fbbf24}.appointment-status.approved[_ngcontent-%COMP%]{background:#34d39933;color:#34d399}.appointment-status.completed[_ngcontent-%COMP%]{background:#199a8e33;color:#199a8e}.appointment-status.cancelled[_ngcontent-%COMP%], .appointment-status.rejected[_ngcontent-%COMP%]{background:#ef444433;color:#ef4444}@media (max-width: 576px){.section-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:10px}.booked-appointment-card[_ngcontent-%COMP%]{padding:10px;gap:10px}.appointment-date-badge[_ngcontent-%COMP%]{min-width:40px;height:40px}.appointment-info[_ngcontent-%COMP%]{flex-direction:column;gap:2px}}.appointment-count[_ngcontent-%COMP%]{font-size:14px;color:#666;background-color:#40b3a21a;padding:4px 12px;border-radius:16px}']})};var Be=class r{constructor(e,t,n,i,o){this.http=e;this.firestore=t;this.storage=n;this.authService=i;this.ngZone=o}medicalRecordsCollection="medicalRecords";getMedicalRecords(e){return this.ngZone.runOutsideAngular(()=>{if(!e){let i=this.authService.getCurrentUser();if(i?.role==="patient"&&i.uid)e=i.uid;else return this.ngZone.run(()=>(console.warn("No patient ID provided and current user is not a patient"),w([])))}let t=_e(this.firestore,this.medicalRecordsCollection),n=ye(t,ve("patientId","==",e),qe("date","desc"));return Lt(n,{idField:"id"}).pipe(E(i=>this.ngZone.run(()=>i.map(o=>F(I({},o),{date:o.date.toDate(),nextVisit:o.nextVisit?o.nextVisit.toDate():void 0})))),v(i=>this.ngZone.run(()=>(console.error("Error fetching medical records:",i),C(()=>i)))))})}getMedicalRecordsByDoctor(e){return this.ngZone.runOutsideAngular(()=>{let t=_e(this.firestore,this.medicalRecordsCollection),n=ye(t,ve("doctorId","==",e),qe("date","desc"));return Lt(n,{idField:"id"}).pipe(E(i=>this.ngZone.run(()=>i.map(o=>F(I({},o),{date:o.date.toDate(),nextVisit:o.nextVisit?o.nextVisit.toDate():void 0})))),v(i=>this.ngZone.run(()=>(console.error("Error fetching medical records by doctor:",i),C(()=>i)))))})}getMedicalRecordById(e){return this.ngZone.runOutsideAngular(()=>{let t=W(this.firestore,`${this.medicalRecordsCollection}/${e}`);return k(fe(t)).pipe(E(n=>this.ngZone.run(()=>{if(n.exists()){let i=n.data();return F(I({id:n.id},i),{date:i.date.toDate(),nextVisit:i.nextVisit?i.nextVisit.toDate():void 0})}else throw new Error("Medical record not found")})),v(n=>this.ngZone.run(()=>(console.error("Error fetching medical record:",n),C(()=>n)))))})}addMedicalRecord(e,t){return this.ngZone.runOutsideAngular(()=>t&&t.length>0?this.uploadFiles(t,e.patientId,e.doctorId).pipe(B(n=>{let i=F(I({},e),{attachments:n});return this.saveRecord(i)})):this.saveRecord(e))}saveRecord(e){return this.ngZone.runOutsideAngular(()=>{let t=_e(this.firestore,this.medicalRecordsCollection);return k(ct(t,e)).pipe(E(n=>this.ngZone.run(()=>n.id)),v(n=>this.ngZone.run(()=>(console.error("Error adding medical record:",n),C(()=>n)))))})}updateMedicalRecord(e,t,n){return this.ngZone.runOutsideAngular(()=>{let i=W(this.firestore,`${this.medicalRecordsCollection}/${e}`);return n&&n.length>0?this.getMedicalRecordById(e).pipe(B(o=>this.ngZone.run(()=>{let l=t.patientId||o.patientId,d=t.doctorId||o.doctorId;return this.uploadFiles(n,l,d).pipe(B(m=>{let h=o.attachments||[],_=F(I({},t),{attachments:[...h,...m]});return this.ngZone.runOutsideAngular(()=>k(Ge(i,_)))}))})),v(o=>this.ngZone.run(()=>(console.error("Error updating medical record with new files:",o),C(()=>o))))):k(Ge(i,t)).pipe(v(o=>this.ngZone.run(()=>(console.error("Error updating medical record:",o),C(()=>o)))))})}deleteMedicalRecord(e){return this.ngZone.runOutsideAngular(()=>this.getMedicalRecordById(e).pipe(B(t=>this.ngZone.run(()=>{let n=W(this.firestore,`${this.medicalRecordsCollection}/${e}`);if(t.attachments&&t.attachments.length>0){let i=t.attachments.map(o=>this.ngZone.runOutsideAngular(()=>{let l=Vt(this.storage,o);return gn(l)}));return k(Promise.all(i)).pipe(B(()=>this.ngZone.runOutsideAngular(()=>k(Bt(n)))))}else return this.ngZone.runOutsideAngular(()=>k(Bt(n)))})),v(t=>this.ngZone.run(()=>(console.error("Error deleting medical record:",t),C(()=>t))))))}uploadFiles(e,t,n){return this.ngZone.runOutsideAngular(()=>{if(!e||e.length===0)return w([]);let i=e.map(o=>this.uploadFile(o,t,n));return $e(i)})}uploadFile(e,t,n){return this.ngZone.runOutsideAngular(()=>{let i=`medical-records/${Date.now()}_${e.name}`,o=Vt(this.storage,i),d=fn(o,e,{customMetadata:{patientId:t,doctorId:n}});return new Se(m=>{d.on("state_changed",h=>{this.ngZone.run(()=>{let _=h.bytesTransferred/h.totalBytes*100;console.log("Upload is "+_+"% done")})},h=>{this.ngZone.run(()=>{console.error("Upload failed:",h),m.error(h)})},()=>{un(d.snapshot.ref).then(h=>{this.ngZone.run(()=>{m.next(h),m.complete()})}).catch(h=>{this.ngZone.run(()=>{m.error(h)})})})})})}downloadAttachment(e){return this.http.get(e,{responseType:"blob"}).pipe(v(t=>(console.error("Error downloading attachment:",t),C(()=>t))))}downloadRecord(e){return this.getMedicalRecordById(e).pipe(B(t=>{if(t.attachments&&t.attachments.length>0)return this.downloadAttachment(t.attachments[0]);{let n=`
            Medical Record ID: ${t.id}
            Patient ID: ${t.patientId}
            Doctor: ${t.doctorName||t.doctorId}
            Date: ${t.date.toLocaleDateString()}
            Diagnosis: ${t.diagnosis}
            Treatment: ${t.treatment}
            Prescription: ${t.prescription||"None"}
            Notes: ${t.notes||"None"}
            Next Visit: ${t.nextVisit?t.nextVisit.toLocaleDateString():"Not scheduled"}
          `,i=new Blob([n],{type:"text/plain"});return w(i)}}),v(t=>(console.error("Error downloading medical record:",t),C(()=>t))))}static \u0275fac=function(t){return new(t||r)(z(xe),z(Ne),z(mn),z(vn),z(be))};static \u0275prov=X({token:r,factory:r.\u0275fac,providedIn:"root"})};var yi=(r,e)=>e.id;function Oi(r,e){r&1&&(a(0,"div",1),u(1,"div",4),a(2,"p"),p(3,"Loading your medical records..."),s()())}function wi(r,e){if(r&1){let t=D();a(0,"div",2),Ie(),a(1,"svg",5),u(2,"circle",6)(3,"line",7)(4,"line",8),s(),He(),a(5,"p"),p(6),s(),a(7,"button",9),b("click",function(){M(t);let i=f();return y(i.loadRecords())}),p(8,"Try Again"),s()()}if(r&2){let t=f();c(6),O(t.error)}}function Si(r,e){if(r&1&&(a(0,"div",19)(1,"strong"),p(2,"Next Visit:"),s(),a(3,"p"),p(4),s()()),r&2){let t=f().$implicit,n=f(2);c(4),O(n.formatDate(t.nextVisit))}}function Ii(r,e){if(r&1){let t=D();a(0,"button",22),b("click",function(){M(t);let i=f().$implicit,o=f(2);return y(o.downloadRecord(i.id))}),Ie(),a(1,"svg",23),u(2,"path",24)(3,"polyline",25)(4,"line",26),s()()}}function ki(r,e){if(r&1&&(a(0,"div",14)(1,"div",15),u(2,"div",16),a(3,"div",17)(4,"h3"),p(5,"Medical Record"),s(),a(6,"div",18)(7,"div",19)(8,"strong"),p(9,"Diagnosis:"),s(),a(10,"p"),p(11),s()(),a(12,"div",19)(13,"strong"),p(14,"Treatment:"),s(),a(15,"p"),p(16),s()(),x(17,Si,5,1,"div",19),s(),a(18,"span",20),p(19),s()(),x(20,Ii,5,0,"button",21),s()()),r&2){let t=e.$implicit,n=f(2);c(11),O(t.diagnosis),c(5),O(t.treatment),c(),te(t.nextVisit?17:-1),c(2),T("Date: ",n.formatDate(t.date),""),c(),te(t.id?20:-1)}}function Ai(r,e){if(r&1){let t=D();a(0,"div",10)(1,"span"),p(2,"Sort By:"),s(),a(3,"div",11)(4,"button",12),b("click",function(){M(t);let i=f();return y(i.setFilter("newest"))}),p(5," Newest "),s(),a(6,"button",12),b("click",function(){M(t);let i=f();return y(i.setFilter("oldest"))}),p(7," Oldest "),s()()(),a(8,"div",13),ce(9,ki,21,5,"div",14,yi),s()}if(r&2){let t=f();c(4),A("active",t.currentFilter==="newest"),c(2),A("active",t.currentFilter==="oldest"),c(3),pe(t.filteredRecords)}}function Di(r,e){r&1&&(a(0,"div",3),Ie(),a(1,"svg",27),u(2,"path",28)(3,"polyline",29),s(),He(),a(4,"p"),p(5,"No medical records available"),s(),a(6,"p",30),p(7,"Your doctor hasn't sent any records yet."),s()())}var At=class r{constructor(e){this.medicalRecordsService=e}currentFilter="newest";records=[];loading=!0;error=null;ngOnInit(){this.loadRecords()}loadRecords(){this.loading=!0,this.error=null,this.medicalRecordsService.getMedicalRecords().subscribe(e=>{this.records=e,this.loading=!1},e=>{console.error("Error fetching medical records:",e),this.error="Failed to load medical records. Please try again later.",this.loading=!1})}get filteredRecords(){return[...this.records].sort((e,t)=>{let n=new Date(e.date).getTime(),i=new Date(t.date).getTime();return this.currentFilter==="newest"?i-n:n-i})}setFilter(e){this.currentFilter=e}downloadRecord(e){if(!e){console.error("Record ID is required for download");return}this.medicalRecordsService.downloadRecord(e).subscribe(t=>{let n=window.URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download=`medical_record_${e}.pdf`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(n)},t=>{console.error("Error downloading file:",t)})}formatDate(e){return e?(e instanceof Date?e:new Date(e)).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}):""}static \u0275fac=function(t){return new(t||r)(P(Be))};static \u0275cmp=S({type:r,selectors:[["app-medical-record"]],features:[et([Be])],decls:9,vars:1,consts:[[1,"medical-records-container"],[1,"loading-state"],[1,"error-state"],[1,"no-records"],[1,"spinner"],["width","48","height","48","viewBox","0 0 24 24","fill","none","stroke","#e53935","stroke-width","2"],["cx","12","cy","12","r","10"],["x1","12","y1","8","x2","12","y2","12"],["x1","12","y1","16","x2","12","y2","16"],[1,"retry-button",3,"click"],[1,"search-filters"],[1,"filter-buttons"],[3,"click"],[1,"records-list"],[1,"record-card"],[1,"record-content"],[1,"record-bullet"],[1,"record-details"],[1,"record-info"],[1,"info-item"],[1,"record-date"],["title","Download record",1,"download-button"],["title","Download record",1,"download-button",3,"click"],["width","20","height","20","viewBox","0 0 24 24","fill","none","stroke","currentColor","stroke-width","2"],["d","M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"],["points","7 10 12 15 17 10"],["x1","12","y1","15","x2","12","y2","3"],["width","48","height","48","viewBox","0 0 24 24","fill","none","stroke","#14B8A6","stroke-width","2"],["d","M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"],["points","13 2 13 9 20 9"],[1,"sub-text"]],template:function(t,n){t&1&&(a(0,"div",0)(1,"header"),u(2,"app-navbar"),a(3,"h1"),p(4,"Medical Record"),s()(),x(5,Oi,4,0,"div",1)(6,wi,9,1,"div",2)(7,Ai,11,4)(8,Di,8,0,"div",3),s()),t&2&&(c(5),te(n.loading?5:n.error?6:n.records.length>0?7:8))},dependencies:[V,ie,H],styles:[".medical-records-container[_ngcontent-%COMP%]{padding:16px;background-color:#f8f9fa;min-height:100vh}header[_ngcontent-%COMP%]{margin-bottom:24px}h1[_ngcontent-%COMP%]{font-size:1.75rem;font-weight:600;color:#333;margin-top:16px;margin-bottom:0}.search-filters[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:16px;background-color:#fff;padding:12px 16px;border-radius:8px;box-shadow:0 2px 4px #0000000d}.search-filters[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#666;margin-right:12px;font-size:.9rem}.filter-buttons[_ngcontent-%COMP%]{display:flex;gap:8px}.filter-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{background-color:#f0f0f0;border:none;padding:6px 12px;border-radius:20px;font-size:.85rem;color:#666;cursor:pointer;transition:all .2s ease}.filter-buttons[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background-color:#4a6cf7;color:#fff}.records-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.record-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #00000014}.record-content[_ngcontent-%COMP%]{display:flex;padding:16px;position:relative}.record-bullet[_ngcontent-%COMP%]{width:12px;height:12px;background-color:#4a6cf7;border-radius:50%;margin-right:12px;margin-top:6px;flex-shrink:0}.record-details[_ngcontent-%COMP%]{flex:1}.record-details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;font-size:1.1rem;color:#333;font-weight:600}.record-info[_ngcontent-%COMP%]{margin-bottom:12px}.info-item[_ngcontent-%COMP%]{margin-bottom:8px}.info-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{display:block;font-size:.9rem;color:#666;margin-bottom:2px}.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#333;font-size:.95rem;line-height:1.4}.record-date[_ngcontent-%COMP%]{display:block;font-size:.8rem;color:#888;margin-top:8px}.download-button[_ngcontent-%COMP%]{background:none;border:none;color:#4a6cf7;cursor:pointer;padding:8px;border-radius:50%;display:flex;align-items:center;justify-content:center;position:absolute;top:12px;right:12px;transition:background-color .2s ease}.download-button[_ngcontent-%COMP%]:hover{background-color:#4a6cf71a}.no-records[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 16px;text-align:center;background-color:#fff;border-radius:12px;box-shadow:0 2px 8px #00000014;margin-top:24px}.no-records[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-bottom:16px;color:#14b8a6}.no-records[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#333;font-weight:500;font-size:1.1rem}.no-records[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{margin-top:8px;color:#666;font-size:.9rem;font-weight:400}.loading-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 16px;text-align:center;background-color:#fff;border-radius:12px;box-shadow:0 2px 8px #00000014;margin-top:24px}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #4a6cf7;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:16px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.error-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:48px 16px;text-align:center;background-color:#fff;border-radius:12px;box-shadow:0 2px 8px #00000014;margin-top:24px}.error-state[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-bottom:16px;color:#e53935}.error-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 16px;color:#333;font-weight:500;font-size:1rem}.retry-button[_ngcontent-%COMP%]{background-color:#4a6cf7;color:#fff;border:none;padding:8px 16px;border-radius:4px;font-size:.9rem;cursor:pointer;transition:background-color .2s ease}.retry-button[_ngcontent-%COMP%]:hover{background-color:#3a5ce5}.bottom-nav[_ngcontent-%COMP%]{position:fixed;bottom:clamp(15px,4vh,20px);left:0;width:100%;display:flex;justify-content:space-around;padding:clamp(8px,2vh,10px) clamp(15px,4vw,20px);background-color:#fff;box-shadow:0 -2px 10px #0000001a;z-index:1000}.nav-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(6px,1.5vw,8px);background:none;border:none;color:silver;font-size:clamp(10px,2.5vw,12px);cursor:pointer;padding:clamp(6px,1.5vh,8px) clamp(12px,3vw,16px);border-radius:20px;transition:all .3s ease}.nav-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:clamp(16px,4vw,20px)}.nav-button.active[_ngcontent-%COMP%]{color:#00a693;background-color:#e6f7f5}.nav-label[_ngcontent-%COMP%]{font-size:clamp(12px,3vw,14px)}@media (min-width: 768px){.medical-records-container[_ngcontent-%COMP%]{padding:30px;margin-top:20px}.records-list[_ngcontent-%COMP%]{max-width:700px;margin:0 auto}.record-card[_ngcontent-%COMP%]{padding:20px}.filter-buttons[_ngcontent-%COMP%]{justify-content:flex-start}}@media (max-width: 360px){.search-filters[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.no-records[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{margin-bottom:8px}.no-records[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px;font-weight:500}.no-records[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-size:14px;color:#999}}@media (max-width: 480px){.medical-records-container[_ngcontent-%COMP%]{padding:16px}.header[_ngcontent-%COMP%]{margin-bottom:20px}h1[_ngcontent-%COMP%]{font-size:20px}}"]})};function Ti(r,e){if(r&1&&(a(0,"div",7)(1,"span"),p(2),s()()),r&2){let t=f();c(2),O(t.error)}}function Ei(r,e){r&1&&(a(0,"div",8),u(1,"div",9),a(2,"p"),p(3,"Processing your request..."),s()())}function Ni(r,e){if(r&1&&(a(0,"div",25)(1,"h3"),p(2,"Doctor's Notes"),s(),a(3,"p",26),p(4),s()()),r&2){let t=f(3);c(4),O(t.appointment.doctorNotes)}}function Fi(r,e){if(r&1){let t=D();a(0,"div",27)(1,"button",28),b("click",function(){M(t);let i=f(3);return y(i.cancelAppointment())}),p(2),s()()}if(r&2){let t=f(3);c(),g("disabled",t.isLoading),c(),T(" ",t.isLoading?"Cancelling...":"Cancel Appointment"," ")}}function zi(r,e){if(r&1&&(a(0,"div")(1,"div",16)(2,"div",17)(3,"span",18),p(4,"Date"),s(),a(5,"span",19),p(6),s()(),a(7,"div",17)(8,"span",18),p(9,"Time"),s(),a(10,"span",19),p(11),s()(),a(12,"div",17)(13,"span",18),p(14,"Duration"),s(),a(15,"span",19),p(16),s()(),a(17,"div",17)(18,"span",18),p(19,"Booking Status"),s(),a(20,"span",20),p(21),s()()(),a(22,"div",21)(23,"h3"),p(24,"Medical Concern"),s(),a(25,"p",22),p(26),s()(),x(27,Ni,5,1,"div",23)(28,Fi,3,2,"div",24),s()),r&2){let t=f(2);c(6),O(t.formatDate(t.appointment.appointmentDate)),c(5),O(t.formatTime(t.appointment.appointmentTime)),c(5),T("",t.appointment.durationMinutes," minutes"),c(4),ke("status-"+t.appointment.status.toLowerCase()),c(),T(" ",t.appointment.status," "),c(5),O(t.appointment.reasonForVisit||"General Consultation"),c(),g("ngIf",t.appointment.doctorNotes),c(),g("ngIf",t.appointment.status==="Pending"||t.appointment.status==="Approved")}}function Ri(r,e){r&1&&(a(0,"div",29)(1,"p"),p(2,"No appointment details available."),s(),a(3,"button",30),p(4,"Book an Appointment"),s()())}function Ui(r,e){if(r&1&&(a(0,"div",10)(1,"div",11),u(2,"img",12),a(3,"div",13)(4,"h2"),p(5),s(),a(6,"p",14),p(7),s()()(),x(8,zi,29,9,"div",15)(9,Ri,5,0,"ng-template",null,0,tt),s()),r&2){let t=Je(10),n=f();c(2),g("src",n.doctorImage,ee),c(3),T("Dr. ",n.appointment==null?null:n.appointment.doctorName,""),c(2),O((n.appointment==null?null:n.appointment.specialization)||"General Practitioner"),c(),g("ngIf",n.appointment)("ngIfElse",t)}}var Dt=class r{constructor(e,t,n){this.appointmentService=e;this.router=t;this.route=n}appointment=null;doctorImage="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Mask%20group-ADQ5qIcUBxrq7579C7xyRng7wHXK2p.png";isLoading=!1;error=null;ngOnInit(){this.loadAppointment()}loadAppointment(){let t=this.router.getCurrentNavigation()?.extras.state;if(t?.appointment){console.log("Appointment from navigation state:",t.appointment),this.appointment=t.appointment,this.appointmentService.setCurrentAppointment(this.appointment);return}this.appointment=this.appointmentService.getCurrentAppointment(),this.appointment?console.log("Loaded appointment from service:",this.appointment):(console.log("No appointment found in state or service, redirecting to booking"),this.router.navigate(["/mobile/appointment-booking"]))}formatDate(e){if(!e)return"No date available";try{return(e instanceof Date?e:new Date(e)).toLocaleDateString("en-US",{weekday:"long",day:"numeric",month:"long",year:"numeric"})}catch(t){return console.error("Error formatting date:",t),"Invalid date"}}formatTime(e){if(!e)return"No time available";try{if(e.includes("AM")||e.includes("PM"))return e;let[t,n]=e.split(":").map(Number),i=t>=12?"PM":"AM";return`${t%12||12}:${n.toString().padStart(2,"0")} ${i}`}catch(t){return console.error("Error formatting time:",t),e}}getStatusColor(e){switch(e){case"Pending":return"#eab308";case"Approved":return"#22c55e";case"Rejected":case"Cancelled":return"#ef4444";case"Completed":return"#3b82f6";default:return"#6b7280"}}cancelAppointment(){if(!this.appointment){this.error="Cannot cancel: No appointment details available";return}if(this.appointment.status!=="Pending"&&this.appointment.status!=="Approved"){this.error=`Cannot cancel appointment with status: ${this.appointment.status}`;return}this.isLoading=!0,this.error=null,console.log(`Cancelling appointment with ID: ${this.appointment.appointmentId}`),this.appointmentService.cancelAppointment(this.appointment.appointmentId,"Cancelled by patient").subscribe({next:()=>{console.log("Appointment cancelled successfully"),this.isLoading=!1,this.appointment&&(this.appointment.status="Cancelled"),setTimeout(()=>{this.router.navigate(["/mobile/patient-dashboard"])},1500)},error:e=>{console.error("Error cancelling appointment:",e),this.error="Failed to cancel appointment. Please try again.",this.isLoading=!1}})}static \u0275fac=function(t){return new(t||r)(P(le),P(j),P(on))};static \u0275cmp=S({type:r,selectors:[["app-appointment-details"]],decls:8,vars:3,consts:[["noAppointment",""],[1,"appointment-screen"],["routerLink","/mobile/patient-dashboard"],[1,"title"],["class","error-alert",4,"ngIf"],["class","loading-container",4,"ngIf"],["class","content",4,"ngIf"],[1,"error-alert"],[1,"loading-container"],[1,"loading-spinner"],[1,"content"],[1,"doctor-info"],["alt","Doctor",1,"doctor-image",3,"src"],[1,"doctor-details"],[1,"subtitle"],[4,"ngIf","ngIfElse"],[1,"details-grid"],[1,"detail-row"],[1,"label"],[1,"value"],[1,"value","status"],[1,"problem-section"],[1,"problem-text"],["class","notes-section",4,"ngIf"],["class","action-buttons",4,"ngIf"],[1,"notes-section"],[1,"notes-text"],[1,"action-buttons"],[1,"cancel-button",3,"click","disabled"],[1,"no-appointment"],["routerLink","/mobile/appointment-booking",1,"book-button"]],template:function(t,n){t&1&&(a(0,"div",1),u(1,"app-back-button",2),a(2,"h1",3),p(3,"Appointment Details"),s(),x(4,Ti,3,1,"div",4)(5,Ei,4,0,"div",5)(6,Ui,11,5,"div",6),u(7,"app-navbar"),s()),t&2&&(c(4),g("ngIf",n.error),c(),g("ngIf",n.isLoading),c(),g("ngIf",!n.isLoading))},dependencies:[V,U,Q,$,H],styles:[".appointment-screen[_ngcontent-%COMP%]{max-width:480px;margin:0 auto;padding:20px 20px 60px;font-family:system-ui,-apple-system,sans-serif;width:100%;box-sizing:border-box;position:relative;min-height:100vh;background:#f8f9fa}.content[_ngcontent-%COMP%]{padding:20px;width:auto;max-width:100%;box-sizing:border-box;margin-top:15px;background:#fff;border-radius:16px;margin-bottom:70px}.title[_ngcontent-%COMP%]{font-size:24px;font-weight:700;margin:16px 0 24px;color:#111827;text-align:center;position:absolute;left:50%;transform:translate(-50%);top:8px}.error-alert[_ngcontent-%COMP%]{background:#fee2e2;color:#b91c1c;padding:12px 16px;border-radius:8px;margin-bottom:16px}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;padding:32px}.loading-spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:3px solid #f3f3f3;border-top:3px solid #199a8e;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:16px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.doctor-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;background-color:#f0fdfc;padding:16px;border-radius:16px;margin-bottom:24px;border-bottom:1px solid #e5e7eb}.doctor-image[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:50%;object-fit:cover;margin-right:16px}.doctor-details[_ngcontent-%COMP%]{flex:1}.doctor-details[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;font-weight:600;margin:0 0 4px;color:#111827}.subtitle[_ngcontent-%COMP%]{font-size:14px;color:#6b7280;margin:0}.details-grid[_ngcontent-%COMP%]{display:grid;gap:16px;border-top:1px solid #EAEAEA;border-bottom:1px solid #EAEAEA;padding:32px 0;margin-bottom:24px}.detail-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px 0;margin-bottom:24px}.detail-row[_ngcontent-%COMP%]:last-child{margin-bottom:0}.label[_ngcontent-%COMP%]{color:#6b7280;font-size:14px}.value[_ngcontent-%COMP%]{font-weight:500;font-size:14px;color:#111827}.value.status[_ngcontent-%COMP%]{font-weight:600;padding:4px 12px;border-radius:12px;display:inline-block}.status-pending[_ngcontent-%COMP%]{background-color:#fef3c7;color:#d97706}.status-approved[_ngcontent-%COMP%]{background-color:#dcfce7;color:#16a34a}.status-completed[_ngcontent-%COMP%]{background-color:#dbeafe;color:#2563eb}.status-cancelled[_ngcontent-%COMP%]{background-color:#fee2e2;color:#dc2626}.status-available[_ngcontent-%COMP%]{background-color:#f3f4f6;color:#4b5563}.problem-section[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]{margin-bottom:24px;width:100%}.problem-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .notes-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:16px;font-weight:700;margin:0 0 12px;color:#374151}.problem-text[_ngcontent-%COMP%], .notes-text[_ngcontent-%COMP%]{background-color:#f9fafb;border:none;border-radius:8px;padding:12px;font-size:14px;line-height:1.5;color:#6b7280;white-space:normal;word-wrap:break-word;overflow-wrap:break-word;width:auto;max-width:100%}.action-buttons[_ngcontent-%COMP%]{margin-top:24px}.cancel-button[_ngcontent-%COMP%]{width:100%;padding:12px;border:none;border-radius:12px;background:#fee2e2;color:#b91c1c;font-size:16px;font-weight:600;cursor:pointer}.cancel-button[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.book-button[_ngcontent-%COMP%]{width:100%;padding:12px;border:none;border-radius:12px;background:#199a8e;color:#fff;font-size:16px;font-weight:600;cursor:pointer}.no-appointment[_ngcontent-%COMP%]{text-align:center;padding:32px 16px}.no-appointment[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin:0 0 16px}@media (max-width: 480px){.appointment-screen[_ngcontent-%COMP%]{padding:0 0 60px}.content[_ngcontent-%COMP%]{padding:20px}}@media (max-width: 400px){.title[_ngcontent-%COMP%]{font-size:20px}.content[_ngcontent-%COMP%]{padding:16px}.doctor-image[_ngcontent-%COMP%]{width:48px;height:48px}.doctor-details[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:16px}.subtitle[_ngcontent-%COMP%]{font-size:12px}}"]})};var Wt=(r,e)=>e.id;function ji(r,e){if(r&1){let t=D();a(0,"div",13)(1,"div",14),u(2,"i",15),p(3," Doctor "),s(),a(4,"div",16)(5,"div",17),p(6),s(),a(7,"div",18),p(8),s(),a(9,"button",19),b("click",function(){let i=M(t).$implicit,o=f();return y(o.toggleDoctorAvailability(i.id))}),u(10,"i",20),p(11),s()()()}if(r&2){let t=e.$implicit,n=f();A("selected",n.selectedDoctorId===t.id),c(6),We("Dr. ",t.firstname," ",t.lastname||"",""),c(2),O(t.email),c(2),A("bi-calendar-check",n.selectedDoctorId!==t.id)("bi-calendar-x",n.selectedDoctorId===t.id),c(),T(" ",n.selectedDoctorId===t.id?"Close Availability":"View Availability"," ")}}function Li(r,e){r&1&&(a(0,"div",6)(1,"div",21),u(2,"i",22),s(),a(3,"h3"),p(4,"No Doctors Found"),s(),a(5,"p"),p(6,"You don't have any doctors assigned to your account yet."),s()())}function Bi(r,e){r&1&&(a(0,"div",26),u(1,"i",27),p(2," Appointment booked successfully! "),s())}function Vi(r,e){if(r&1&&(a(0,"div",28),u(1,"i",29),p(2),s()),r&2){let t=f(2);c(2),T(" ",t.bookingError," ")}}function $i(r,e){if(r&1&&(a(0,"div",23),x(1,Bi,3,0,"div",24)(2,Vi,3,1,"div",25),s()),r&2){let t=f();c(),g("ngIf",t.bookingSuccess),c(),g("ngIf",t.bookingError)}}function Hi(r,e){if(r&1){let t=D();a(0,"div",40)(1,"div",41)(2,"div",42),p(3),s(),a(4,"div",43),p(5),s()(),a(6,"button",44),b("click",function(){let i=M(t).$implicit,o=f(3);return y(o.bookAppointment(i))}),p(7," Book "),s()()}if(r&2){let t=e.$implicit,n=f(3);c(3),We(" ",n.formatTime(t.start_time)," - ",n.formatTime(t.end_time)," "),c(),A("available",n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time))("booked",!n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time)),c(),T(" ",n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time)?"Available":"Booked"," "),c(),g("disabled",!n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time))}}function Zi(r,e){if(r&1){let t=D();a(0,"div",32)(1,"div",33),b("click",function(){let i=M(t).index,o=f(2);return y(o.toggleDateGroup(i))}),a(2,"div",34),u(3,"i",35),p(4),a(5,"span",36),p(6),s()(),u(7,"i",37),s(),a(8,"div",38)(9,"div",39),ce(10,Hi,8,8,"div",40,Wt),s()()()}if(r&2){let t=e.$implicit,n=e.index,i=f(2);c(),A("active",i.openDateGroups[n]),c(3),T(" ",i.formatDateHeader(t.date)," "),c(2),T("",t.slots.length," slots"),c(),A("active",i.openDateGroups[n]),c(),A("active",i.openDateGroups[n]),c(2),pe(t.slots)}}function Wi(r,e){if(r&1&&(a(0,"div",30)(1,"h1"),p(2,"Doctor Availability"),s(),x(3,Zi,12,8,"div",31),s()),r&2){let t=f();c(3),g("ngForOf",t.groupedAvailability)}}function Ki(r,e){if(r&1){let t=D();a(0,"tr")(1,"td"),p(2),s(),a(3,"td"),p(4),s(),a(5,"td"),p(6),s(),a(7,"td")(8,"span",48),p(9),s()(),a(10,"td")(11,"button",49),b("click",function(){let i=M(t).$implicit,o=f(2);return y(o.bookAppointment(i))}),p(12," Book "),s()()()}if(r&2){let t=e.$implicit,n=f(2);c(2),O(n.formatDate(t.date)),c(2),O(n.formatTime(t.start_time)),c(2),O(n.formatTime(t.end_time)),c(2),A("available",n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time))("booked",!n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time)),c(),T(" ",n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time)?"Available":"Booked"," "),c(2),A("disabled",!n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time)),g("disabled",!n.isTimeSlotAvailable(t.doctor_id,t.date,t.start_time))}}function qi(r,e){if(r&1&&(a(0,"div",45)(1,"div",46)(2,"table",47)(3,"thead")(4,"tr")(5,"th"),p(6,"Date"),s(),a(7,"th"),p(8,"From"),s(),a(9,"th"),p(10,"To"),s(),a(11,"th"),p(12,"Status"),s(),a(13,"th"),p(14,"Action"),s()()(),a(15,"tbody"),ce(16,Ki,13,11,"tr",null,Wt),s()()()()),r&2){let t=f();c(16),pe(t.doctorAvailability())}}function Gi(r,e){r&1&&u(0,"div",53)}function Yi(r,e){r&1&&(Ae(0),a(1,"div",21),u(2,"i",54),s(),a(3,"h3"),p(4,"No Availability Found"),s(),a(5,"p"),p(6,"This doctor doesn't have any available slots at the moment. Please select another doctor or check back later."),s(),De())}function Xi(r,e){if(r&1&&(a(0,"div",50),x(1,Gi,1,0,"div",51)(2,Yi,7,0,"ng-container",52),s()),r&2){let t=f();c(),g("ngIf",t.isLoading),c(),g("ngIf",!t.isLoading)}}function Qi(r,e){r&1&&(a(0,"div",6)(1,"div",21),u(2,"i",55),s(),a(3,"h3"),p(4,"Select a Doctor"),s(),a(5,"p"),p(6,"Please select a doctor to view their availability."),s()())}function Ji(r,e){if(r&1){let t=D();a(0,"div",56)(1,"div",57)(2,"div",58)(3,"h2"),p(4,"Appointment Details"),s(),a(5,"button",59),b("click",function(){M(t);let i=f();return y(i.showModal=!1)}),p(6,"\xD7"),s()(),a(7,"div",60)(8,"p"),p(9,"Please provide a reason for your visit:"),s(),a(10,"textarea",61),ge("ngModelChange",function(i){M(t);let o=f();return me(o.medicalConcern,i)||(o.medicalConcern=i),y(i)}),s()(),a(11,"div",62)(12,"button",63),b("click",function(){M(t);let i=f();return y(i.showModal=!1)}),p(13,"Cancel"),s(),a(14,"button",64),b("click",function(){M(t);let i=f();return y(i.confirmBooking())}),p(15,"Book Appointment"),s()()()()}if(r&2){let t=f();c(10),de("ngModel",t.medicalConcern)}}var Tt=class r{constructor(e,t){this.appointmentService=e;this.router=t}selectedDoctorId=null;showTableView=!1;isFirstLoad=!0;groupedAvailability=[];openDateGroups=[];isLoading=!1;selectedDoctor=null;selectedDate=new Date;selectedSlot=null;availableSlots=[];datePickerOpen=!1;calendarDates=[];viewMonth=new Date;showModal=!1;medicalConcern="";bookingSuccess=!1;bookingError="";showWeeklySchedule=!1;weeklySchedule=[];doctorSchedules=[];fetchingSchedules=!1;weekDays=["S","M","T","W","T","F","S"];dateLabels=[];destroy$=new Kt;myDoctors=Ut([]);db=G(ae);doctorAvailability=Ut([]);firebaseService=G(_t);ngZone=G(be);slotToBook=null;ngOnInit(){this.findMyDoctors(),this.isFirstLoad=!0}toggleDoctorAvailability(e){if(this.isFirstLoad=!1,this.selectedDoctorId===e){this.selectedDoctorId=null,this.groupedAvailability=[],this.doctorAvailability.set([]);return}this.onViewDoctorAvailability(e)}onViewDoctorAvailability(e){this.isLoading=!0,this.selectedDoctorId=e,this.isFirstLoad=!1,console.log(`Fetching availability for doctor ID: ${e}`),this.validateAndStoreDoctorId(e),this.firebaseService.syncAvailabilityData(e).pipe(Qe(this.destroy$),v(t=>(console.error("Error synchronizing availability data:",t),w(null)))).subscribe({next:()=>{console.log("Availability data synchronized, now loading data"),this.firebaseService.loadDoctorAvailability(e).pipe(Qe(this.destroy$),Xe(()=>this.isLoading=!1)).subscribe({next:t=>{if(console.log(`Loaded ${t.length} availability slots from Firebase after sync`),t.length>0){let n=[...t].sort((o,l)=>{let d=new Date(o.date).getTime()-new Date(l.date).getTime();return d!==0?d:o.start_time.localeCompare(l.start_time)});this.groupAvailabilityByDate(n);let i=this.db.userTable().find(o=>o.id===e);i&&console.log(`Viewing availability for Dr. ${i.firstname} ${i.lastname}`),this.doctorAvailability.set(n)}else console.log("No availability slots found for this doctor"),this.groupedAvailability=[],this.doctorAvailability.set([])},error:t=>{console.error("Error loading availability from Firebase:",t),this.isLoading=!1,this.groupedAvailability=[],this.doctorAvailability.set([])}})},error:t=>{console.error("Failed to synchronize availability data:",t),this.isLoading=!1,this.loadDoctorAvailabilityDirectly(e)}})}loadDoctorAvailabilityDirectly(e){this.firebaseService.loadDoctorAvailability(e).pipe(Qe(this.destroy$),Xe(()=>this.isLoading=!1)).subscribe({next:t=>{if(console.log(`Loaded ${t.length} availability slots directly from Firebase`),t.length>0){let n=[...t].sort((i,o)=>{let l=new Date(i.date).getTime()-new Date(o.date).getTime();return l!==0?l:i.start_time.localeCompare(o.start_time)});this.groupAvailabilityByDate(n),this.doctorAvailability.set(n)}else console.log("No availability slots found for this doctor"),this.groupedAvailability=[],this.doctorAvailability.set([])},error:t=>{console.error("Error loading availability directly from Firebase:",t),this.groupedAvailability=[],this.doctorAvailability.set([])}})}validateAndStoreDoctorId(e){if(!e){console.error("Invalid doctor ID (empty or null)");return}let t=this.db.userTable().find(n=>n.id===e);t?console.log(`Found doctor: ${t.firstname} ${t.lastname}`):console.warn(`Doctor with ID ${e} not found in user table`)}groupAvailabilityByDate(e){let t={};e.forEach(n=>{t[n.date]||(t[n.date]=[]),t[n.date].push(n)}),this.groupedAvailability=Object.keys(t).map(n=>({date:n,slots:t[n]})),this.openDateGroups=this.groupedAvailability.map((n,i)=>i===0)}toggleDateGroup(e){this.openDateGroups[e]=!this.openDateGroups[e]}formatDateHeader(e){return new Date(e).toLocaleDateString("en-US",{weekday:"short",month:"short",day:"numeric"})}formatDate(e){return new Date(e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}formatTime(e){if(!e)return"";let[t,n]=e.split(":").map(Number),i=t>=12?"PM":"AM";return`${t%12||12}:${n.toString().padStart(2,"0")} ${i}`}isTimeSlotAvailable(e,t,n){return!this.db.appointmentTable().find(o=>o.doctor_id===e&&o.date===t&&o.time===n)}findMyDoctors(){let e=this.db.current_patient()?.id;if(!e){console.error("No current patient ID found");return}console.log("Finding doctors for patient ID:",e),this.myDoctors.set([]),this.queryDoctorPatientCollection(e,"doctorPatient"),this.queryDoctorPatientCollection(e,"doctorPatients"),this.findDoctorsWithThisPatient(e)}queryDoctorPatientCollection(e,t){let n=at(rt(this.appointmentService.firestore,t),Ke("patientId","==",e)),i=at(rt(this.appointmentService.firestore,t),Ke("patient_id","==",e));console.log(`Querying ${t} collection for patient ID: ${e}`),this.ngZone.runOutsideAngular(()=>{$e([k(st(n)),k(st(i))]).pipe(E(([o,l])=>{let d=[...o.docs,...l.docs];if(d.length===0)return console.log(`No doctor-patient relationships found in ${t} collection`),[];console.log(`Found doctor-patient relationships in ${t}:`,d.length);let m=new Set;return d.forEach(h=>{let _=h.data(),N=_.doctorId||_.doctor_id;N&&(m.add(N),console.log(`Adding doctor ID from relationship: ${N}`))}),Array.from(m)}),B(o=>{if(o.length===0)return w([]);let l=o.map(d=>{let m=pn(this.appointmentService.firestore,`users/${d}`);return k(dn(m)).pipe(E(h=>{if(!h.exists())return console.log(`Doctor document for ID ${d} not found`),null;let _=h.data();return _?{id:d,firstname:_.firstName||_.firstname||"",lastname:_.lastName||_.lastname||"",email:_.email||"",role:"DOCTOR",password:""}:null}),v(h=>(console.error("Error fetching doctor details:",h),w(null))))});return $e(l)}),E(o=>o.filter(l=>l!==null))).subscribe({next:o=>{this.ngZone.run(()=>{if(o.length>0){let d=[...this.myDoctors()];o.forEach(m=>{d.some(h=>h.id===m.id)||d.push(m)}),this.myDoctors.set(d),this.updateLocalStorage(d,e)}})},error:o=>{this.ngZone.run(()=>{console.error("Error loading doctors:",o)})}})})}updateLocalStorage(e,t){let i=[...this.db.userTable()];e.forEach(l=>{let d=i.findIndex(m=>m.id===l.id);d===-1?i.push(l):i[d]=l}),this.db.userTable.set(i),this.db.storage.setItem("USER_TABLE",i);let o=e.map(l=>({id:crypto.randomUUID?crypto.randomUUID():`${Date.now()}`,doctor_id:l.id,patient_id:t}));this.db.doctorPatientTable.set(o),this.db.storage.setItem("DOCTOR_PATIENT_TABLE",o)}findDoctorsWithThisPatient(e){console.log(`Looking for doctors that have patient ${e} in their assignedPatients array`),this.ngZone.runOutsideAngular(()=>{let t=at(rt(this.appointmentService.firestore,"users"),Ke("role","==","DOCTOR"),Ke("assignedPatients","array-contains",e));k(st(t)).pipe(E(n=>{if(n.empty)return console.log("No doctors found with this patient in their assignedPatients array"),[];console.log(`Found ${n.size} doctors with this patient in assignedPatients`);let i=[];return n.forEach(o=>{let l=o.data();console.log("Doctor found with patient in assignedPatients:",o.id,l);let d={id:o.id,firstname:l.firstName||l.firstname||"",lastname:l.lastName||l.lastname||"",email:l.email||"",role:"DOCTOR",password:""};i.push(d)}),i}),v(n=>(console.error("Error finding doctors with this patient:",n),w([])))).subscribe({next:n=>{this.ngZone.run(()=>{if(n.length===0){console.log("No additional doctors found via assignedPatients array");return}console.log("Found additional doctors via assignedPatients array:",n);let i=this.myDoctors(),o=new Set(i.map(d=>d.id)),l=[...i];for(let d of n)if(d.id&&!o.has(d.id)&&(l.push(d),o.add(d.id),this.db.userTable().some(h=>h.id===d.id)||(this.db.userTable.update(h=>[...h,d]),this.db.storage.setItem("USER_TABLE",this.db.userTable())),!this.db.doctorPatientTable().find(h=>h.doctor_id===d.id&&h.patient_id===e))){let h={id:crypto.randomUUID?crypto.randomUUID():`${Date.now()}`,doctor_id:d.id,patient_id:e};this.db.addDoctorPatient(h)}this.myDoctors.set(l)})},error:n=>{this.ngZone.run(()=>{console.error("Error processing doctors with this patient:",n)})}})})}bookAppointment(e){this.slotToBook=e,this.showModal=!0,this.medicalConcern=""}confirmBooking(){if(!this.slotToBook){console.error("No appointment slot selected");return}let e=this.slotToBook;this.showModal=!1,this.isLoading=!0;let t=this.db.current_patient();if(!t||!t.id){console.error("No current patient ID found"),this.bookingError="Patient information not found. Please log in again.",setTimeout(()=>{this.bookingError=""},3e3),this.isLoading=!1;return}let n=this.medicalConcern.trim()||"General Consultation",i={id:this.db.generateId(),date:e.date,time:e.start_time,doctor_id:e.doctor_id,patient_id:t.id,status:"Pending",patientName:`${t.firstname} ${t.lastname}`,reasonForVisit:n};if(this.db.appointmentTable().find(d=>d.doctor_id==i.doctor_id&&i.patient_id&&d.date==i.date&&d.time==i.time)){this.bookingError="You already have an appointment at this time.",setTimeout(()=>{this.bookingError=""},3e3),this.isLoading=!1;return}let l={date:e.date,time:e.start_time,doctor_id:e.doctor_id,patient_id:this.db.current_patient()?.id||"",status:"Pending",patientName:`${this.db.current_patient()?.firstname} ${this.db.current_patient()?.lastname}`,reasonForVisit:n};this.firebaseService.addAppointment(l).pipe(Xe(()=>this.isLoading=!1)).subscribe({next:d=>{console.log("Successfully booked appointment with ID:",d);let m=F(I({},l),{id:d});this.db.addAppointment(m),this.bookingSuccess=!0,this.firebaseService.removeAvailability(e.id).subscribe({next:()=>{console.log("Availability slot removed after booking"),this.onViewDoctorAvailability(e.doctor_id)},error:h=>{console.error("Error removing availability slot:",h)}}),setTimeout(()=>{this.bookingSuccess=!1},3e3)},error:d=>{console.error("Error booking appointment in Firebase:",d),this.db.addAppointment(i),this.bookingSuccess=!0,this.onViewDoctorAvailability(e.doctor_id),setTimeout(()=>{this.bookingSuccess=!1},3e3)}})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}static \u0275fac=function(t){return new(t||r)(P(le),P(j))};static \u0275cmp=S({type:r,selectors:[["app-appointment-booking"]],decls:18,vars:7,consts:[[1,"appointment-container"],["routerLink","/patient-dashboard"],[1,"header-title"],[1,"availableTimeSlots"],[1,"doctor-cards"],[1,"doctor-card",3,"selected"],[1,"empty-state"],["class","message-container",4,"ngIf"],["class","availability-section",4,"ngIf"],["class","availability-table-container",4,"ngIf"],["class","no-availability",4,"ngIf"],["class","empty-state",4,"ngIf"],["class","booking-modal",4,"ngIf"],[1,"doctor-card"],[1,"doctor-card-header"],[1,"bi","bi-person-badge"],[1,"doctor-card-body"],[1,"doctor-name"],[1,"doctor-email"],[1,"view-availability-btn",3,"click"],[1,"bi"],[1,"empty-state-icon"],[1,"bi","bi-clipboard-x"],[1,"message-container"],["class","message success",4,"ngIf"],["class","message error",4,"ngIf"],[1,"message","success"],[1,"bi","bi-check-circle-fill"],[1,"message","error"],[1,"bi","bi-exclamation-circle-fill"],[1,"availability-section"],["class","date-group-container",4,"ngFor","ngForOf"],[1,"date-group-container"],[1,"date-dropdown-header",3,"click"],[1,"date-title"],[1,"bi","bi-calendar-event"],[1,"slot-count"],[1,"bi","bi-chevron-down","chevron-icon"],[1,"date-dropdown-content"],[1,"time-slots-grid"],[1,"time-slot-item"],[1,"time-slot-info"],[1,"time-slot-range"],[1,"time-slot-status"],[1,"book-slot-btn",3,"click","disabled"],[1,"availability-table-container"],[1,"table-container"],[1,"availability-table"],[1,"status"],[3,"click","disabled"],[1,"no-availability"],["class","loading-indicator",4,"ngIf"],[4,"ngIf"],[1,"loading-indicator"],[1,"bi","bi-calendar-x"],[1,"bi","bi-person-lines-fill"],[1,"booking-modal"],[1,"modal-content"],[1,"modal-header"],[1,"close-btn",3,"click"],[1,"modal-body"],["placeholder","Describe your medical concerns...","rows","5",1,"reason-textarea",3,"ngModelChange","ngModel"],[1,"modal-footer"],[1,"cancel-btn",3,"click"],[1,"confirm-btn",3,"click"]],template:function(t,n){t&1&&(a(0,"div",0),u(1,"app-back-button",1),a(2,"h1",2),p(3,"Book Appointment"),s(),u(4,"app-navbar"),a(5,"div",3)(6,"h1"),p(7,"My Doctor(s)"),s()(),a(8,"div",4),ce(9,ji,12,10,"div",5,Wt),x(11,Li,7,0,"div",6),s(),x(12,$i,3,2,"div",7)(13,Wi,4,1,"div",8)(14,qi,18,0,"div",9)(15,Xi,3,2,"div",10)(16,Qi,7,0,"div",11)(17,Ji,16,1,"div",12),s()),t&2&&(c(9),pe(n.myDoctors()),c(2),te(n.myDoctors().length===0?11:-1),c(),g("ngIf",n.bookingSuccess||n.bookingError),c(),g("ngIf",n.groupedAvailability&&n.groupedAvailability.length>0&&n.selectedDoctorId),c(),g("ngIf",n.showTableView&&n.doctorAvailability().length>0&&n.selectedDoctorId),c(),g("ngIf",n.doctorAvailability().length===0&&n.selectedDoctorId),c(),g("ngIf",!n.selectedDoctorId&&n.myDoctors().length>0&&!n.isFirstLoad),c(),g("ngIf",n.showModal))},dependencies:[V,Ee,U,Ce,oe,re,ze,$,Q,H],styles:['.message-container[_ngcontent-%COMP%]{position:fixed;top:80px;right:20px;z-index:100;max-width:350px;animation:_ngcontent-%COMP%_slideIn .3s ease-out}.message[_ngcontent-%COMP%]{padding:15px;margin-bottom:15px;border-radius:8px;box-shadow:0 4px 12px #00000026;display:flex;align-items:center;gap:10px;font-weight:500}.message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.2rem}.message.success[_ngcontent-%COMP%]{background-color:#e2f8f5;border-left:4px solid #199a8e;color:#0f766e}.message.error[_ngcontent-%COMP%]{background-color:#fee2e2;border-left:4px solid #dc2626;color:#b91c1c}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translate(30px)}to{opacity:1;transform:translate(0)}}@media (max-width: 576px){.message-container[_ngcontent-%COMP%]{top:70px;right:10px;max-width:calc(100% - 20px)}}.appointment-container[_ngcontent-%COMP%]{max-width:100%;margin:0 auto;padding:20px 20px 80px;font-family:system-ui,-apple-system,sans-serif;width:100%;box-sizing:border-box;position:relative;min-height:100vh;background-color:#f8f9fa;color:#334155}.header-title[_ngcontent-%COMP%]{font-size:1.6rem;font-weight:700;margin:16px 0 30px;color:#199a8e;text-align:center}.availableTimeSlots[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:#199a8e;margin:25px 0 15px;padding-bottom:8px;border-bottom:2px solid #199a8e}.doctor-cards[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:20px;margin-bottom:30px}.doctor-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;box-shadow:0 4px 6px #0000000d;overflow:hidden;transition:transform .2s ease,box-shadow .2s ease;cursor:pointer;border:1px solid #e2e8f0}.doctor-card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 10px 15px #0000001a}.doctor-card.selected[_ngcontent-%COMP%]{border:2px solid #199a8e}.doctor-card-header[_ngcontent-%COMP%]{padding:15px;background-color:#199a8e;color:#fff}.doctor-card-body[_ngcontent-%COMP%]{padding:15px}.doctor-name[_ngcontent-%COMP%]{font-size:1.1rem;font-weight:600;margin-bottom:5px}.doctor-email[_ngcontent-%COMP%]{font-size:.9rem;color:#64748b;margin-bottom:15px}.view-availability-btn[_ngcontent-%COMP%]{display:block;width:100%;padding:10px;background-color:#199a8e;color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:background-color .2s;text-align:center}.view-availability-btn[_ngcontent-%COMP%]:hover{background-color:#0f766e}table[_ngcontent-%COMP%]{width:100%;border-collapse:separate;border-spacing:0;margin-bottom:30px;background-color:#fff;border-radius:10px;box-shadow:0 4px 6px #0000000d;overflow:hidden}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]{background-color:#199a8e}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:14px;text-align:left}table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{color:#fff;font-weight:600;text-transform:uppercase;font-size:.85rem;letter-spacing:.5px}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{border-bottom:1px solid #e2e8f0;transition:background-color .2s ease}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:last-child{border-bottom:none}table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8fafc}table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 16px;background-color:#199a8e;color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;box-shadow:0 2px 4px #199a8e4d}table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#0f766e;transform:translateY(-1px)}table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:active{transform:translateY(0)}.availability-section[_ngcontent-%COMP%]{margin-top:30px;padding:20px;background-color:#fff;border-radius:10px;box-shadow:0 4px 6px #0000000d}.availability-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px;color:#199a8e;font-weight:600;border-bottom:2px solid #199a8e;padding-bottom:10px}.availability-table[_ngcontent-%COMP%]{width:100%;overflow:visible;margin-bottom:15px;border-radius:8px;overflow:hidden}.availability-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#199a8e;color:#fff;font-weight:600;font-size:.9rem;text-align:left;padding:14px}.availability-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:14px;border-bottom:1px solid #e2e8f0;font-size:.95rem}.availability-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:last-child   td[_ngcontent-%COMP%]{border-bottom:none}.availability-table[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#f8fafc}.date-group-container[_ngcontent-%COMP%]{margin-bottom:15px}.date-dropdown-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:15px;background-color:#f1f5f9;border-radius:8px;cursor:pointer;margin-bottom:1px;transition:background-color .2s;border-left:4px solid #199a8e}.date-dropdown-header[_ngcontent-%COMP%]:hover{background-color:#e2e8f0}.date-dropdown-header.active[_ngcontent-%COMP%]{background-color:#e2f8f5}.date-title[_ngcontent-%COMP%]{font-weight:600;color:#334155;display:flex;align-items:center;gap:10px}.slot-count[_ngcontent-%COMP%]{font-size:.8rem;background-color:#199a8e;color:#fff;padding:2px 8px;border-radius:10px}.chevron-icon[_ngcontent-%COMP%]{transition:transform .3s ease}.chevron-icon.active[_ngcontent-%COMP%]{transform:rotate(180deg)}.date-dropdown-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:0 0 8px 8px;overflow:hidden;max-height:0;opacity:0;transition:all .3s ease;box-shadow:0 4px 6px #0000000d}.date-dropdown-content.active[_ngcontent-%COMP%]{max-height:800px;opacity:1;padding:15px;border:1px solid #e2e8f0;border-top:none}.time-slot-item[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:12px;background-color:#f8fafc;border-radius:8px;border:1px solid #e2e8f0;transition:all .2s ease}.time-slot-item[_ngcontent-%COMP%]:hover{background-color:#e2f8f5;border-color:#199a8e}.time-slot-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.time-slot-range[_ngcontent-%COMP%]{font-weight:500;color:#334155}.time-slot-status[_ngcontent-%COMP%]{font-size:.75rem}.time-slot-status.available[_ngcontent-%COMP%]{color:#16a34a}.time-slot-status.booked[_ngcontent-%COMP%]{color:#dc2626}.book-slot-btn[_ngcontent-%COMP%]{padding:6px 12px;background-color:#199a8e;color:#fff;border:none;border-radius:4px;cursor:pointer;transition:background-color .2s;font-size:.8rem}.book-slot-btn[_ngcontent-%COMP%]:hover{background-color:#0f766e}.book-slot-btn[_ngcontent-%COMP%]:disabled{background-color:#94a3b8;cursor:not-allowed}.status[_ngcontent-%COMP%]{display:inline-block;padding:6px 12px;border-radius:50px;font-weight:500;font-size:.85rem;text-align:center}.status.available[_ngcontent-%COMP%]{background-color:#dcfce7;color:#16a34a}.status.booked[_ngcontent-%COMP%]{background-color:#fee2e2;color:#dc2626}.availability-table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:8px 16px;background-color:#199a8e;color:#fff;border:none;border-radius:6px;font-weight:500;cursor:pointer;transition:all .2s ease;width:100%;max-width:120px}.availability-table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover{background-color:#0f766e;box-shadow:0 2px 5px #2563eb4d}.availability-table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled, .availability-table[_ngcontent-%COMP%]   button.disabled[_ngcontent-%COMP%]{background-color:#94a3b8;cursor:not-allowed;box-shadow:none;opacity:.7}.no-availability[_ngcontent-%COMP%]{padding:25px;text-align:center;background-color:#fff;border-radius:10px;margin:30px 0;color:#64748b;font-style:italic;box-shadow:0 4px 6px #0000000d}.table-container[_ngcontent-%COMP%]{width:100%;overflow-x:auto;-webkit-overflow-scrolling:touch;margin-bottom:20px;border-radius:10px;box-shadow:0 4px 6px #0000000d}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.appointment-container[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeIn .4s ease-out}.loading-indicator[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:150px}.loading-indicator[_ngcontent-%COMP%]:after{content:"";width:40px;height:40px;border:4px solid #e2e8f0;border-top:4px solid #199a8e;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.appointment-container[_ngcontent-%COMP%]{padding:15px 15px 70px}.header-title[_ngcontent-%COMP%]{font-size:1.4rem;margin:15px 0 25px}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:12px 10px;font-size:.9rem}.status[_ngcontent-%COMP%]{font-size:.75rem;padding:5px 10px}.availability-section[_ngcontent-%COMP%]{padding:15px}.availability-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.3rem;margin-bottom:15px}}@media (max-width: 576px){.appointment-container[_ngcontent-%COMP%]{padding:10px 10px 70px}.header-title[_ngcontent-%COMP%]{font-size:1.3rem}.doctor-cards[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(240px,1fr))}table[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]{font-size:.85rem}table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:10px 8px}table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .availability-table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:6px 12px;font-size:.85rem}.status[_ngcontent-%COMP%]{font-size:.7rem;padding:4px 8px}.time-slots-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(120px,1fr))}.time-selection[_ngcontent-%COMP%]   .time-slots-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(auto-fill,minmax(100px,1fr))}}@media (max-width: 576px){.appointment-container[_ngcontent-%COMP%]{padding:10px 10px 70px}.doctor-cards[_ngcontent-%COMP%]{grid-template-columns:1fr}}.table-container[_ngcontent-%COMP%]::-webkit-scrollbar{height:6px}.table-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#b4b4b4;border-radius:20px}.table-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background-color:#f1f5f9}.time-selection[_ngcontent-%COMP%]{margin:24px 0;padding:20px;background:#fff;border-radius:10px;box-shadow:0 4px 6px #0000000d}.time-slots-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fill,minmax(140px,1fr));gap:12px}.time-slot[_ngcontent-%COMP%]{padding:12px 8px;background:#e2f8f5;border:1px solid #199a8e;border-radius:8px;font-size:.9rem;color:#199a8e;font-weight:500;text-align:center;cursor:pointer;transition:all .2s ease}.time-slot[_ngcontent-%COMP%]:hover{background:#199a8e;color:#fff;transform:translateY(-2px)}@keyframes _ngcontent-%COMP%_pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover, .availability-table[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover:not(:disabled){animation:_ngcontent-%COMP%_pulse .5s ease-in-out}.empty-state[_ngcontent-%COMP%]{text-align:center;padding:30px;background-color:#fff;border-radius:10px;margin-top:20px;box-shadow:0 4px 6px #0000000d}.empty-state-icon[_ngcontent-%COMP%]{font-size:3rem;color:#199a8e;margin-bottom:15px}.empty-state[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#334155;margin-bottom:10px}.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#64748b;max-width:400px;margin:0 auto}',".booking-modal[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:#00000080;display:flex;justify-content:center;align-items:center;z-index:1000}.modal-content[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px;width:90%;max-width:500px;box-shadow:0 5px 15px #0003;overflow:hidden}.modal-header[_ngcontent-%COMP%]{padding:15px 20px;border-bottom:1px solid #eee;display:flex;justify-content:space-between;align-items:center}.modal-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0;font-size:18px}.close-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:24px;cursor:pointer;color:#999}.modal-body[_ngcontent-%COMP%]{padding:20px}.reason-textarea[_ngcontent-%COMP%]{width:100%;padding:10px;border:1px solid #ddd;border-radius:8px;resize:vertical;font-family:inherit}.modal-footer[_ngcontent-%COMP%]{padding:15px 20px;border-top:1px solid #eee;display:flex;justify-content:flex-end;gap:10px}.cancel-btn[_ngcontent-%COMP%], .confirm-btn[_ngcontent-%COMP%]{padding:10px 16px;border-radius:8px;font-weight:500;cursor:pointer}.cancel-btn[_ngcontent-%COMP%]{background:none;border:1px solid #ddd}.confirm-btn[_ngcontent-%COMP%]{background-color:#4285f4;color:#fff;border:none}"]})};function eo(r,e){if(r&1&&(a(0,"div",3),p(1),s()),r&2){let t=f();c(),O(t.message)}}var Ve=class r{overlay=!1;message;static \u0275fac=function(t){return new(t||r)};static \u0275cmp=S({type:r,selectors:[["app-loading-spinner"]],inputs:{overlay:"overlay",message:"message"},decls:3,vars:3,consts:[[1,"spinner-container"],[1,"spinner"],["class","message",4,"ngIf"],[1,"message"]],template:function(t,n){t&1&&(a(0,"div",0),u(1,"div",1),x(2,eo,2,1,"div",2),s()),t&2&&(A("overlay",n.overlay),c(2),g("ngIf",n.message))},dependencies:[V,U],styles:[".spinner-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:1rem}.spinner-container.overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background-color:#fffc;z-index:9999}.spinner[_ngcontent-%COMP%]{width:40px;height:40px;border:4px solid #f3f3f3;border-top:4px solid #3498db;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.message[_ngcontent-%COMP%]{margin-top:1rem;color:#666;font-size:.875rem}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}"]})};function to(r,e){r&1&&(a(0,"span",21),p(1," Email is required "),s())}function no(r,e){r&1&&(a(0,"span",21),p(1," Invalid email format "),s())}function io(r,e){r&1&&(a(0,"span",21),p(1," Password is required "),s())}function oo(r,e){r&1&&(a(0,"span",21),p(1," Password must be at least 6 characters "),s())}function ro(r,e){if(r&1&&(a(0,"div",22),u(1,"i",23),p(2),s()),r&2){let t=f();c(2),T(" ",t.errorMessage," ")}}function ao(r,e){r&1&&u(0,"i",24)}function so(r,e){r&1&&u(0,"app-loading-spinner")}var Et=class r{constructor(e,t,n,i,o){this.fb=e;this.authService=t;this.router=n;this.db=i;this.storage=o;this.loginForm=this.fb.group({email:["",[Y.required,Y.email]],password:["",[Y.required,Y.minLength(6)]]})}loginForm;errorMessage="";loading=!1;showErrorModal=!1;errorModalTitle="Login Error";errorModalMessage="";errorModalAction="Try Again";onSubmit(){if(Object.keys(this.loginForm.controls).forEach(e=>{this.loginForm.get(e)?.markAsTouched()}),this.loginForm.valid){this.loading=!0,this.errorMessage="";let{email:e,password:t}=this.loginForm.value;this.authService.login(e,t).subscribe({next:n=>{console.log("Login successful",n);let i=this.db.current_doctor();if(!i){this.errorMessage="Error getting user information",this.loading=!1;return}let o=this.authService.getPatientId();if(!o&&n.role==="PATIENT"&&(n.id||n.userId)){let d=n.id||n.userId||"";d&&(this.storage.setItem("patient_id",d),this.storage.setItem("med_secure_patient_id",d),console.log("Patient ID set from user ID in login component:",d))}else o&&(this.storage.setItem("med_secure_patient_id",o),console.log("Patient ID set in med_secure_patient_id:",o));if(i.firstname||i.lastname){let d=`${i.firstname||""} ${i.lastname||""}`.trim();this.storage.setItem("user_displayname",d)}let l=localStorage.getItem("user_info");l?console.log("User info successfully saved to localStorage:",JSON.parse(l)):console.warn("User info not found in localStorage after login"),this.router.navigate(["/mobile/patient-dashboard"])},error:n=>{console.error("Login failed",n),this.errorMessage=typeof n=="string"?n:n?.message||"Login failed. Please check your credentials and try again.",this.loading=!1,this.showErrorModal=!0,this.errorModalMessage=this.errorMessage},complete:()=>{this.loading=!1}})}else this.errorMessage="Please fill in all required fields correctly.",this.loginForm.get("email")?.errors?this.errorMessage=this.getEmailErrorMessage():this.loginForm.get("password")?.errors&&(this.errorMessage=this.getPasswordErrorMessage())}getEmailErrorMessage(){let e=this.loginForm.controls.email;return e.hasError("required")?"Email is required.":e.hasError("email")?"Invalid email format.":""}getPasswordErrorMessage(){let e=this.loginForm.controls.password;return e.hasError("required")?"Password is required.":e.hasError("minlength")?"Password must be at least 6 characters.":""}onModalClose(){this.showErrorModal=!1}navigateToRegister(){this.router.navigate(["/mobile/register"])}static \u0275fac=function(t){return new(t||r)(P(ut),P(q),P(j),P(ae),P(ht))};static \u0275cmp=S({type:r,selectors:[["app-login"]],decls:33,vars:14,consts:[[1,"login-container"],[1,"login-card"],[1,"login-header"],[1,"bi","bi-person"],[3,"ngSubmit","formGroup"],[1,"form-group"],[1,"input-container"],[1,"bi","bi-envelope"],["type","email","formControlName","email","placeholder","Enter your email address"],["class","validation-error",4,"ngIf"],[1,"bi","bi-shield-lock"],["type","password","formControlName","password","placeholder","Enter your password"],[1,"remember-forgot"],["href","#"],["class","error-message",4,"ngIf"],["type","submit",1,"login-btn"],["class","bi bi-arrow-repeat spinner",4,"ngIf"],[1,"register-link"],["routerLink","/mobile/register"],[3,"showChange","close","show","title","message","actionText","onAction"],[4,"ngIf"],[1,"validation-error"],[1,"error-message"],[1,"bi","bi-exclamation-circle"],[1,"bi","bi-arrow-repeat","spinner"]],template:function(t,n){if(t&1&&(a(0,"div",0)(1,"div",1)(2,"div",2),u(3,"i",3),a(4,"h2"),p(5,"Welcome Back"),s()(),a(6,"form",4),b("ngSubmit",function(){return n.onSubmit()}),a(7,"div",5)(8,"div",6),u(9,"i",7)(10,"input",8),s(),x(11,to,2,0,"span",9)(12,no,2,0,"span",9),s(),a(13,"div",5)(14,"div",6),u(15,"i",10)(16,"input",11),s(),x(17,io,2,0,"span",9)(18,oo,2,0,"span",9),s(),a(19,"div",12)(20,"a",13),p(21,"Forgot password?"),s()(),x(22,ro,3,1,"div",14),a(23,"button",15),x(24,ao,1,0,"i",16),a(25,"span"),p(26),s()()(),a(27,"p",17),p(28," Don't have an account? "),a(29,"a",18),p(30,"Register"),s()(),a(31,"app-error-modal",19),b("showChange",function(o){return n.showErrorModal=o})("close",function(){return n.onModalClose()}),s(),x(32,so,1,0,"app-loading-spinner",20),s()()),t&2){let i,o,l,d;c(6),g("formGroup",n.loginForm),c(5),g("ngIf",((i=n.loginForm.get("email"))==null||i.errors==null?null:i.errors.required)&&((i=n.loginForm.get("email"))==null?null:i.touched)),c(),g("ngIf",((o=n.loginForm.get("email"))==null||o.errors==null?null:o.errors.email)&&((o=n.loginForm.get("email"))==null?null:o.touched)),c(5),g("ngIf",((l=n.loginForm.get("password"))==null||l.errors==null?null:l.errors.required)&&((l=n.loginForm.get("password"))==null?null:l.touched)),c(),g("ngIf",((d=n.loginForm.get("password"))==null||d.errors==null?null:d.errors.minlength)&&((d=n.loginForm.get("password"))==null?null:d.touched)),c(4),g("ngIf",n.errorMessage),c(2),g("ngIf",n.loading),c(2),O(n.loading?"Logging in...":"Login"),c(5),g("show",n.showErrorModal)("title",n.errorModalTitle)("message",n.errorModalMessage)("actionText",n.errorModalAction)("onAction",n.navigateToRegister.bind(n)),c(),g("ngIf",!1)}},dependencies:[V,U,ft,Re,oe,re,Fe,mt,gt,$,ie,Ve,bt],styles:[".error-message[_ngcontent-%COMP%]{background:#fef2f2;color:#dc2626;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:static;transform:none}.spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block;margin-right:8px}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.login-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.login-card[_ngcontent-%COMP%]{width:100%;max-width:480px;background:#fff;border-radius:20px;box-shadow:0 10px 25px #0000000d;padding:3rem 2.5rem;position:relative;overflow:hidden;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.login-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:2rem}.login-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.bi-person[_ngcontent-%COMP%]{font-size:50px;width:70px;height:70px;display:flex;justify-content:center;align-items:center;border-radius:50%;background-color:#e6f7f5;color:#199a8e;padding:15px;margin:0 auto 1.5rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:1.5rem;font-weight:600;margin-bottom:.5rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:2rem}.input-container[_ngcontent-%COMP%]{position:relative;width:100%}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem;display:flex;flex-direction:column}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem;z-index:1;pointer-events:none}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.5rem;border:1px solid #e5e7eb;border-radius:8px;font-size:.875rem;color:#111827;transition:all .2s;background-color:#f9fafb;height:48px;box-sizing:border-box}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a;background-color:#fff}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#6b7280}.validation-error[_ngcontent-%COMP%]{color:#dc2626;font-size:.75rem;margin-top:.25rem;display:block}.remember-forgot[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center;margin-bottom:1.5rem;font-size:.875rem}.remember-forgot[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;transition:color .2s}.remember-forgot[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#147d73}.login-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:linear-gradient(90deg,#199a8e,#1fb5a6);color:#fff;border:none;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem;margin-bottom:1rem}.login-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #199a8e26}.login-btn[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}.admin-login-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:#fff;color:#199a8e;border:2px solid #199a8e;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem}.admin-login-btn[_ngcontent-%COMP%]:hover{background:#199a8e0d}.register-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;font-size:.875rem;color:#4b5563}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;margin-left:.25rem}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@media screen and (max-width: 1200px){.login-card[_ngcontent-%COMP%]{max-width:440px;padding:2.5rem 2rem}}@media screen and (max-width: 768px){.login-container[_ngcontent-%COMP%]{padding:1.5rem}.login-card[_ngcontent-%COMP%]{max-width:400px;padding:2rem 1.5rem}.bi-person[_ngcontent-%COMP%]{font-size:40px;width:60px;height:60px;padding:12px;margin-bottom:1.25rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.4rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.95rem;margin-bottom:1.75rem}.form-group[_ngcontent-%COMP%]{margin-bottom:1.25rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.75rem 1rem .75rem 2.25rem;height:44px;font-size:.85rem}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.remember-forgot[_ngcontent-%COMP%]{font-size:.8rem;margin-bottom:1.25rem}.login-btn[_ngcontent-%COMP%]{height:44px;font-size:.95rem;padding:.75rem}}@media screen and (max-width: 480px){.login-container[_ngcontent-%COMP%]{padding:1rem}.login-card[_ngcontent-%COMP%]{max-width:100%;padding:1.75rem 1.25rem}.bi-person[_ngcontent-%COMP%]{font-size:35px;width:55px;height:55px;padding:10px;margin-bottom:1rem}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.3rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.9rem;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]{margin-bottom:1rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.7rem 1rem .7rem 2.25rem;height:42px;font-size:.8rem}.remember-forgot[_ngcontent-%COMP%]{flex-direction:column;gap:.75rem;align-items:flex-start;margin-bottom:1.5rem}.error-message[_ngcontent-%COMP%]{font-size:.8rem;padding:.6rem .8rem}}@media screen and (max-width: 320px){.login-card[_ngcontent-%COMP%]{padding:1.5rem 1rem}.bi-person[_ngcontent-%COMP%]{font-size:30px;width:50px;height:50px;padding:8px}.login-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.2rem}.login-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.85rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{padding:.65rem 1rem .65rem 2rem;height:40px}.login-btn[_ngcontent-%COMP%]{height:40px;font-size:.9rem;padding:.65rem}}"]})};function lo(r,e){r&1&&(a(0,"span",21),p(1," Full name is required "),s())}function co(r,e){r&1&&(a(0,"span",21),p(1," Please enter both your first and last name "),s())}function po(r,e){if(r&1&&(a(0,"span",21),p(1),s()),r&2){let t=f();c(),T(" ",t.emailErrorMessage," ")}}function mo(r,e){r&1&&(a(0,"span",21),p(1," Password must be at least 6 characters long "),s())}function go(r,e){r&1&&(a(0,"span",21),p(1," Please confirm your password "),s())}function uo(r,e){r&1&&(a(0,"span",21),p(1," Passwords do not match "),s())}function fo(r,e){if(r&1&&(a(0,"div",22),p(1),s()),r&2){let t=f();c(),T(" ",t.errorMessage," ")}}function ho(r,e){r&1&&u(0,"app-loading-spinner")}var Nt=class r{constructor(e,t,n,i,o){this.fb=e;this.authService=t;this.doctorService=n;this.db=i;this.router=o;this.registerForm=this.fb.group({fullname:["",[Y.required,Y.pattern(/^[a-zA-Z]+ [a-zA-Z]+/)]],email:["",[Y.required,Y.email,this.validEmailDomainValidator()]],password:["",[Y.required,Y.minLength(6)]],confirmPassword:["",Y.required]},{validators:this.passwordMatchValidator}),this.registerForm.get("email")?.valueChanges.subscribe(()=>{this.errorMessage="",this.showErrorModal=!1,this.emailTouched&&this.updateEmailErrorMessage()})}registerForm;loading=!1;errorMessage="";showErrorModal=!1;errorModalTitle="";errorModalMessage="";errorModalAction="";selectedRole="patient";emailTouched=!1;emailErrorMessage="";registrationSuccess=!1;ngOnInit(){this.authService.isLoggedIn()&&this.router.navigate(["/mobile/patient-dashboard"])}validEmailDomainValidator(){return e=>{let t=e.value;if(!t)return null;if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(t))return{invalidEmailFormat:!0};let i=t.split("@")[1];if(!i)return{invalidDomain:!0};let o=i.split(".").pop();if(!o||o.length<2||o.length>63)return{invalidTLD:!0};let l=[/com[a-z]{1,}$/,/co[a-z]{3,}$/,/org[a-z]{1,}$/,/net[a-z]{1,}$/];for(let d of l)if(d.test(o))return{invalidTLD:!0};return null}}passwordMatchValidator(e){let t=e.get("password"),n=e.get("confirmPassword");return t&&n&&t.value!==n.value?(n.setErrors({passwordMismatch:!0}),{passwordMismatch:!0}):null}showDuplicateEmailError(){this.errorModalTitle="Email Already Registered",this.errorModalMessage="An account with this email already exists. Would you like to login instead?",this.errorModalAction="Go to Login",this.showErrorModal=!0}onEmailBlur(){this.emailTouched=!0,this.updateEmailErrorMessage()}updateEmailErrorMessage(){let e=this.registerForm.get("email");e&&e.errors&&this.emailTouched?e.errors.required?this.emailErrorMessage="Email is required":e.errors.email?this.emailErrorMessage="Please enter a valid email address":e.errors.invalidEmailFormat?this.emailErrorMessage="Please enter a valid email address":e.errors.invalidDomain?this.emailErrorMessage="Invalid email domain":e.errors.invalidTLD?this.emailErrorMessage="The email extension you entered appears to be invalid. Please use a valid email address (e.g., <EMAIL>)":this.emailErrorMessage="Please enter a valid email address":this.emailErrorMessage=""}onModalClose(){if(this.showErrorModal=!1,this.registrationSuccess){this.router.navigate(["/mobile/login"]);return}this.errorMessage="",this.errorModalTitle==="Email Already Registered"&&(this.registerForm.patchValue({email:""}),this.registerForm.get("email")?.markAsUntouched(),this.emailTouched=!1,this.emailErrorMessage="")}navigateToLogin(){this.router.navigate(["/mobile/login"])}onSubmit(){if(!this.showErrorModal)if(Object.keys(this.registerForm.controls).forEach(e=>{this.registerForm.get(e)?.markAsTouched()}),this.registerForm.valid){this.loading=!0,this.errorMessage="";let t=(this.registerForm.get("fullname")?.value.trim()).split(" "),n=t[0],i=t.slice(1).join(" "),o={firstName:n,lastName:i,email:this.registerForm.get("email")?.value,password:this.registerForm.get("password")?.value,role:this.selectedRole};this.authService.register(o).subscribe({next:l=>{console.log("Registration successful:",l),this.loading=!1,this.router.navigate(["/mobile/patient-dashboard"])},error:l=>{console.error("Registration failed:",l),this.loading=!1,l.error&&l.error.message==="Email already exists"?this.showDuplicateEmailError():(this.errorModalTitle="Registration Failed",this.errorModalMessage=typeof l=="string"?l:l.error?.message||"An error occurred during registration. Please try again.",this.errorModalAction="Try Again",this.showErrorModal=!0)}})}else this.errorMessage="Please fill in all required fields correctly.",this.registerForm.get("fullname")?.errors?this.errorMessage="Please enter your full name (first and last name).":this.registerForm.get("email")?.errors?this.errorMessage=this.emailErrorMessage||"Please enter a valid email address.":this.registerForm.get("password")?.errors?this.errorMessage="Password must be at least 6 characters.":this.registerForm.hasError("passwordMismatch")&&(this.errorMessage="Passwords do not match.")}static \u0275fac=function(t){return new(t||r)(P(ut),P(q),P(xt),P(ae),P(j))};static \u0275cmp=S({type:r,selectors:[["app-register"]],features:[et([q,xt])],decls:38,vars:16,consts:[[1,"register-container"],[1,"register-card"],[1,"register-header"],[1,"bi","bi-person"],[3,"ngSubmit","formGroup"],[1,"form-group"],[1,"input-container"],[1,"bi","bi-person-circle"],["type","text","formControlName","fullname","placeholder","Enter your fullname"],["class","validation-error",4,"ngIf"],[1,"bi","bi-envelope"],["type","email","formControlName","email","placeholder","Enter your email",3,"blur"],[1,"bi","bi-shield-lock"],["type","password","formControlName","password","placeholder","Enter your password"],["type","password","formControlName","confirmPassword","placeholder","Confirm your password"],["class","error-message",4,"ngIf"],[3,"showChange","close","show","title","message","actionText","onAction"],["type","submit",1,"submit-button",3,"disabled"],[4,"ngIf"],[1,"login-link"],["routerLink","/mobile/patient-dashboard"],[1,"validation-error"],[1,"error-message"]],template:function(t,n){if(t&1&&(a(0,"div",0)(1,"div",1)(2,"div",2),u(3,"i",3),a(4,"h2"),p(5,"Create Account"),s()(),a(6,"form",4),b("ngSubmit",function(){return n.onSubmit()}),a(7,"div",5)(8,"div",6),u(9,"i",7)(10,"input",8),s(),x(11,lo,2,0,"span",9)(12,co,2,0,"span",9),s(),a(13,"div",5)(14,"div",6),u(15,"i",10),a(16,"input",11),b("blur",function(){return n.onEmailBlur()}),s()(),x(17,po,2,1,"span",9),s(),a(18,"div",5)(19,"div",6),u(20,"i",12)(21,"input",13),s(),x(22,mo,2,0,"span",9),s(),a(23,"div",5)(24,"div",6),u(25,"i",12)(26,"input",14),s(),x(27,go,2,0,"span",9)(28,uo,2,0,"span",9),s(),x(29,fo,2,1,"div",15),a(30,"app-error-modal",16),b("showChange",function(o){return n.showErrorModal=o})("close",function(){return n.onModalClose()}),s(),a(31,"button",17),p(32),x(33,ho,1,0,"app-loading-spinner",18),s()(),a(34,"p",19),p(35," Already have an account? "),a(36,"a",20),p(37,"Login"),s()()()()),t&2){let i,o,l,d,m;c(6),g("formGroup",n.registerForm),c(5),g("ngIf",((i=n.registerForm.get("fullname"))==null||i.errors==null?null:i.errors.required)&&((i=n.registerForm.get("fullname"))==null?null:i.touched)),c(),g("ngIf",((o=n.registerForm.get("fullname"))==null||o.errors==null?null:o.errors.pattern)&&((o=n.registerForm.get("fullname"))==null?null:o.touched)),c(5),g("ngIf",n.emailErrorMessage),c(5),g("ngIf",((l=n.registerForm.get("password"))==null?null:l.errors)&&((l=n.registerForm.get("password"))==null?null:l.touched)),c(5),g("ngIf",((d=n.registerForm.get("confirmPassword"))==null||d.errors==null?null:d.errors.required)&&((d=n.registerForm.get("confirmPassword"))==null?null:d.touched)),c(),g("ngIf",((m=n.registerForm.get("confirmPassword"))==null||m.errors==null?null:m.errors.passwordMismatch)&&((m=n.registerForm.get("confirmPassword"))==null?null:m.touched)),c(),g("ngIf",n.errorMessage),c(),g("show",n.showErrorModal)("title",n.errorModalTitle)("message",n.errorModalMessage)("actionText",n.errorModalAction)("onAction",n.navigateToLogin.bind(n)),c(),g("disabled",n.loading),c(),T(" ",n.loading?"Registering...":"Register"," "),c(),g("ngIf",n.loading)}},dependencies:[V,U,ft,Re,oe,re,Fe,mt,gt,$,ie,bt,Ve],styles:[`.registration-icon[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.roles[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:2rem}.toggle-switch[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:200px;background:#e6f7f5;border-radius:30px;padding:5px;position:relative}.toggle-switch[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{width:50%;text-align:center;padding:10px;cursor:pointer;font-size:14px;font-weight:600;z-index:2;position:relative;transition:all .3s ease;color:#199a8e}.toggle-switch[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{display:none}.toggle-btn[_ngcontent-%COMP%]{position:absolute;width:50%;height:calc(100% - 2px);top:1px;background:#199a8e;border-radius:30px;transition:all .3s ease;z-index:1}.toggle-switch[_ngcontent-%COMP%]   label.active[_ngcontent-%COMP%]{color:#fff}.error-message[_ngcontent-%COMP%]{color:#dc2626;font-size:.875rem;margin:.5rem 0;padding:.5rem;background-color:#fef2f2;border:1px solid #fee2e2;border-radius:6px;text-align:center}.spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite;display:inline-block;margin-right:8px}button[_ngcontent-%COMP%]:disabled{opacity:.7;cursor:not-allowed}.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.Registration-card[_ngcontent-%COMP%]{background:#fff;padding:2.5rem;border-radius:15px;box-shadow:0 4px 20px #00000014;width:600px;max-width:100%;margin:auto}.register-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}h2[_ngcontent-%COMP%]{font-size:24px;text-align:center;margin-bottom:8px;color:#2d3748}.register-card[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#111827;font-size:1.5rem;font-weight:600;margin-bottom:.5rem}h3[_ngcontent-%COMP%]{color:#2d3748;font-size:18px;margin:24px 0 16px;text-align:left}p[_ngcontent-%COMP%]{color:#6c757d;text-align:center;font-size:14px;margin-bottom:24px}.register-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;margin-bottom:2rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{color:#199a8e;font-size:1.1rem!important;margin-right:10px}svg[_ngcontent-%COMP%]{margin:0 auto 1.5rem;display:block}.input-group[_ngcontent-%COMP%]{display:flex;align-items:center;border:1px solid #E2E8F0;border-radius:8px;padding:12px;margin-bottom:16px;transition:border-color .2s;background:#fff}.input-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.input-group[_ngcontent-%COMP%]:focus-within{border-color:#199a8e}.input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{border:none;outline:none;flex:1;padding:0 8px;font-size:14px;color:#4a5568;background:transparent;width:100%}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.75rem;border:1px solid #e5e7eb;border-radius:.5rem;font-size:.9375rem;color:#374151;transition:all .2s ease}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 4px #199a8e1a}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder, .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{color:#a0aec0}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#9ca3af}small[_ngcontent-%COMP%]{color:#ef4444;display:block;text-align:left;margin:-12px 0 16px;font-size:12px}small[_ngcontent-%COMP%]{display:block;color:#ef4444;font-size:.875rem;margin-top:-1rem;margin-bottom:1rem;text-align:left;padding-left:.5rem}.register-with-licence-btn[_ngcontent-%COMP%], .register-without-licence-btn[_ngcontent-%COMP%]{background-color:#fff;color:#199a8e;border:2px solid #199A8E;margin-bottom:12px;padding:12px;width:100%;border-radius:8px;cursor:pointer;font-weight:600;font-size:14px;transition:all .3s ease}.register-with-licence-btn[_ngcontent-%COMP%]:hover, .register-without-licence-btn[_ngcontent-%COMP%]:hover{background-color:#199a8e;color:#fff;transform:translateY(-1px)}button[type=submit][_ngcontent-%COMP%]{width:100%;padding:.875rem;background:#199a8e;color:#fff;border:none;border-radius:.5rem;font-size:1rem;font-weight:500;cursor:pointer;transition:all .2s ease}button[type=submit][_ngcontent-%COMP%]:hover{background:#168276}.register-link[_ngcontent-%COMP%]{font-size:14px;margin-top:16px;color:#4a5568;text-align:center}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;font-weight:600;text-decoration:none;transition:color .2s}.register-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#147d73}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;color:#6b7280;font-size:.9375rem}select[_ngcontent-%COMP%]{appearance:none;background-image:url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23199A8E' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");background-repeat:no-repeat;background-position:right 10px center;background-size:15px;padding-right:30px!important}select.form-control[_ngcontent-%COMP%]{appearance:none;background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23199a8e' class='bi bi-chevron-down' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3E%3C/svg%3E");background-repeat:no-repeat;background-position:right 1rem center;padding-right:2.75rem}.bi-person[_ngcontent-%COMP%]{font-size:50px;width:70px;height:70px;display:flex;justify-content:center;align-items:center;border-radius:50%;background-color:#e6f7f5;color:#199a8e;padding:15px;margin:0 auto 1.5rem}@media (max-width: 640px){.Registration-card[_ngcontent-%COMP%]{padding:2rem}h2[_ngcontent-%COMP%]{font-size:20px}h3[_ngcontent-%COMP%]{font-size:16px}.input-group[_ngcontent-%COMP%]{padding:10px}svg[_ngcontent-%COMP%]{width:40px;height:40px}}@media (max-width: 480px){.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:13px}}.success-message[_ngcontent-%COMP%]{background:#ecfdf5;color:#059669;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.register-header[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]:first-of-type{margin-top:2rem}.register-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6b7280;font-size:1rem}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem;display:flex;flex-direction:column}.input-container[_ngcontent-%COMP%]{position:relative;width:100%}.register-container[_ngcontent-%COMP%]{min-height:100vh;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,#f3f4f6,#e5e7eb);padding:2rem}.register-card[_ngcontent-%COMP%]{width:100%;max-width:480px;background:#fff;border-radius:20px;box-shadow:0 10px 25px #0000000d;padding:3rem 2.5rem;position:relative;overflow:hidden;animation:_ngcontent-%COMP%_fadeIn .5s ease-out}.register-header[_ngcontent-%COMP%]{text-align:center;display:flex;flex-direction:column;align-items:center}.register-header[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.5rem;color:#199a8e;margin-bottom:1rem}.register-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#1f2937;font-size:1.75rem;font-weight:600;margin-bottom:2rem}.roles[_ngcontent-%COMP%]{margin-bottom:2rem}.form-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#199a8e;font-size:1.1rem;z-index:1;pointer-events:none}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{width:100%;padding:.875rem 1rem .875rem 2.5rem;border:1px solid #e5e7eb;border-radius:8px;font-size:.875rem;color:#111827;transition:all .2s;background-color:#f9fafb;height:48px;box-sizing:border-box}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .form-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus{outline:none;border-color:#199a8e;box-shadow:0 0 0 3px #199a8e1a;background-color:#fff}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#6b7280}.validation-error[_ngcontent-%COMP%]{display:block;color:#dc2626;font-size:.75rem;margin-top:.25rem;order:1}.error-message[_ngcontent-%COMP%]{background:#fef2f2;color:#dc2626;padding:.75rem 1rem;border-radius:8px;font-size:.875rem;margin:1rem 0;display:flex;align-items:center;gap:.5rem}.error-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{position:static;transform:none}.register-btn[_ngcontent-%COMP%]{width:100%;padding:.875rem;background:linear-gradient(90deg,#199a8e,#1fb5a6);color:#fff;border:none;border-radius:12px;font-size:1rem;font-weight:500;cursor:pointer;transition:all .3s ease;height:48px;display:flex;align-items:center;justify-content:center;gap:.5rem}.register-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #199a8e26}.register-btn[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_spin 1s linear infinite}.login-link[_ngcontent-%COMP%]{text-align:center;margin-top:1.5rem;font-size:.875rem;color:#4b5563}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#199a8e;text-decoration:none;font-weight:500;margin-left:.25rem}.login-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}@media screen and (max-width: 1200px){.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{max-width:550px;padding:2.5rem 2rem}.register-card[_ngcontent-%COMP%]   i.bi-person[_ngcontent-%COMP%]{font-size:2.25rem}}@media screen and (max-width: 768px){.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{padding:1.5rem}.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{max-width:500px;padding:2rem 1.5rem}h2[_ngcontent-%COMP%]{font-size:22px;margin-bottom:6px}h3[_ngcontent-%COMP%]{font-size:16px;margin:20px 0 14px}p[_ngcontent-%COMP%]{font-size:13px;margin-bottom:20px}.input-group[_ngcontent-%COMP%]{padding:10px;margin-bottom:14px}.input-group[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.9rem}.register-btn[_ngcontent-%COMP%]{height:44px;font-size:.95rem;padding:.75rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:1rem!important}}@media screen and (max-width: 480px){.registration-container[_ngcontent-%COMP%], .register-container[_ngcontent-%COMP%]{padding:1rem}.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{padding:1.75rem 1.25rem}h2[_ngcontent-%COMP%]{font-size:20px}h3[_ngcontent-%COMP%]{font-size:15px;margin:18px 0 12px}p[_ngcontent-%COMP%]{font-size:12px;margin-bottom:18px}.input-group[_ngcontent-%COMP%]{padding:8px;margin-bottom:12px}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.85rem}.register-btn[_ngcontent-%COMP%]{height:42px;font-size:.9rem;padding:.7rem}.error-message[_ngcontent-%COMP%]{font-size:.8rem;padding:.6rem .8rem;margin:.4rem 0}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:.95rem!important}}@media screen and (max-width: 320px){.Registration-card[_ngcontent-%COMP%], .register-card[_ngcontent-%COMP%]{padding:1.5rem 1rem}h2[_ngcontent-%COMP%]{font-size:18px}h3[_ngcontent-%COMP%]{font-size:14px;margin:16px 0 10px}p[_ngcontent-%COMP%]{font-size:11px;margin-bottom:16px}.input-group[_ngcontent-%COMP%]{padding:7px;margin-bottom:10px}.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .input-group[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]{font-size:.8rem}.register-btn[_ngcontent-%COMP%]{height:40px;font-size:.85rem;padding:.65rem}.bi-lock-fill[_ngcontent-%COMP%], .bi-person-fill[_ngcontent-%COMP%], .bi-envelope-fill[_ngcontent-%COMP%], .bi-telephone-fill[_ngcontent-%COMP%], .bi-file-earmark-medical-fill[_ngcontent-%COMP%], .bi-hospital-fill[_ngcontent-%COMP%]{font-size:.9rem!important}}`]})};var Ks=[{path:"",component:vt},{path:"splash",component:je},{path:"info",component:Ct},{path:"login",component:Et},{path:"register",component:Nt},{path:"upcoming",component:Pt},{path:"schedule",component:Ot},{path:"cancel",component:Ye},{path:"patient-profile",component:St},{path:"patient-update-profile",component:It},{path:"patient-dashboard",component:kt},{path:"cancel-booking",component:Ye},{path:"medical-record",component:At},{path:"appointment-details",component:Dt},{path:"appointment-booking",component:Tt}];export{Ks as routes};
