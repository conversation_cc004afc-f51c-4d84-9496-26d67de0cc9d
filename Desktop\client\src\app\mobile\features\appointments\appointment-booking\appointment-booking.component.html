<div class="appointment-container">
  <app-back-button routerLink="/patient-dashboard" />
  <h1 class="header-title">Book Appointment</h1>

  <app-navbar />

  <div class="availableTimeSlots">
    <h1>My Doctor(s)</h1>
  </div>

  <!-- Doctor cards display instead of table -->
  <div class="doctor-cards">
    @for (doctor of myDoctors(); track doctor.id) {
      <div class="doctor-card" [class.selected]="selectedDoctorId === doctor.id">
        <div class="doctor-card-header">
          <i class="bi bi-person-badge"></i> Doctor
        </div>
        <div class="doctor-card-body">
          <div class="doctor-name">Dr. {{doctor.firstname}} {{doctor.lastname || ''}}</div>
          <div class="doctor-email">{{doctor.email}}</div>
          <button class="view-availability-btn" (click)="toggleDoctorAvailability(doctor.id)">
            <i class="bi" [class.bi-calendar-check]="selectedDoctorId !== doctor.id" [class.bi-calendar-x]="selectedDoctorId === doctor.id"></i>
            {{ selectedDoctorId === doctor.id ? 'Close Availability' : 'View Availability' }}
          </button>
        </div>
      </div>
    }

    <!-- Empty state for no doctors -->
    @if (myDoctors().length === 0) {
      <div class="empty-state">
        <div class="empty-state-icon">
          <i class="bi bi-clipboard-x"></i>
        </div>
        <h3>No Doctors Found</h3>
        <p>You don't have any doctors assigned to your account yet.</p>
      </div>
    }
  </div>

  <!-- Success/Error Messages -->
  <div class="message-container" *ngIf="bookingSuccess || bookingError">
    <div class="message success" *ngIf="bookingSuccess">
      <i class="bi bi-check-circle-fill"></i>
      Appointment booked successfully!
    </div>
    <div class="message error" *ngIf="bookingError">
      <i class="bi bi-exclamation-circle-fill"></i>
      {{bookingError}}
    </div>
  </div>

  <!-- Date-grouped availability slots -->
  <div class="availability-section" *ngIf="groupedAvailability && groupedAvailability.length > 0 && selectedDoctorId">
    <h1>Doctor Availability</h1>

    <div class="date-group-container" *ngFor="let dateGroup of groupedAvailability; let i = index">
      <div class="date-dropdown-header" [class.active]="openDateGroups[i]" (click)="toggleDateGroup(i)">
        <div class="date-title">
          <i class="bi bi-calendar-event"></i>
          {{formatDateHeader(dateGroup.date)}}
          <span class="slot-count">{{dateGroup.slots.length}} slots</span>
        </div>
        <i class="bi bi-chevron-down chevron-icon" [class.active]="openDateGroups[i]"></i>
      </div>
      <div class="date-dropdown-content" [class.active]="openDateGroups[i]">
        <div class="time-slots-grid">
          @for (slot of dateGroup.slots; track slot.id) {
            <div class="time-slot-item">
              <div class="time-slot-info">
                <div class="time-slot-range">
                  {{formatTime(slot.start_time)}} - {{formatTime(slot.end_time)}}
                </div>
                <div class="time-slot-status"
                     [class.available]="isTimeSlotAvailable(slot.doctor_id, slot.date, slot.start_time)"
                     [class.booked]="!isTimeSlotAvailable(slot.doctor_id, slot.date, slot.start_time)">
                  {{isTimeSlotAvailable(slot.doctor_id, slot.date, slot.start_time) ? 'Available' : 'Booked'}}
                </div>
              </div>
              <button class="book-slot-btn"
                      [disabled]="!isTimeSlotAvailable(slot.doctor_id, slot.date, slot.start_time)"
                      (click)="bookAppointment(slot)">
                Book
              </button>
            </div>
          }
        </div>
      </div>
    </div>
  </div>

  <!-- Original table view as fallback -->
  <div class="availability-table-container" *ngIf="showTableView && doctorAvailability().length > 0 && selectedDoctorId">
    <div class="table-container">
      <table class="availability-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>From</th>
            <th>To</th>
            <th>Status</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          @for (item of doctorAvailability(); track item.id) {
          <tr>
            <td>{{formatDate(item.date)}}</td>
            <td>{{formatTime(item.start_time)}}</td>
            <td>{{formatTime(item.end_time)}}</td>
            <td>
              <span class="status" [class.available]="isTimeSlotAvailable(item.doctor_id, item.date, item.start_time)"
                    [class.booked]="!isTimeSlotAvailable(item.doctor_id, item.date, item.start_time)">
                {{isTimeSlotAvailable(item.doctor_id, item.date, item.start_time) ? 'Available' : 'Booked'}}
              </span>
            </td>
            <td>
              <button
                [disabled]="!isTimeSlotAvailable(item.doctor_id, item.date, item.start_time)"
                [class.disabled]="!isTimeSlotAvailable(item.doctor_id, item.date, item.start_time)"
                (click)="bookAppointment(item)">
                Book
              </button>
            </td>
          </tr>
          }
        </tbody>
      </table>
    </div>
  </div>

  <!-- No availability message -->
  <div class="no-availability" *ngIf="doctorAvailability().length === 0 && selectedDoctorId">
    <div class="loading-indicator" *ngIf="isLoading"></div>
    <ng-container *ngIf="!isLoading">
      <div class="empty-state-icon">
        <i class="bi bi-calendar-x"></i>
      </div>
      <h3>No Availability Found</h3>
      <p>This doctor doesn't have any available slots at the moment. Please select another doctor or check back later.</p>
    </ng-container>
  </div>

  <!-- Empty state when no doctor is selected -->
  <div class="empty-state" *ngIf="!selectedDoctorId && myDoctors().length > 0 && !isFirstLoad">
    <div class="empty-state-icon">
      <i class="bi bi-person-lines-fill"></i>
    </div>
    <h3>Select a Doctor</h3>
    <p>Please select a doctor to view their availability.</p>
  </div>

  <!-- Add a modal for capturing reason for visit -->
  <div class="booking-modal" *ngIf="showModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Appointment Details</h2>
        <button class="close-btn" (click)="showModal = false">&times;</button>
      </div>
      <div class="modal-body">
        <p>Please provide a reason for your visit:</p>
        <textarea
          [(ngModel)]="medicalConcern"
          placeholder="Describe your medical concerns..."
          class="reason-textarea"
          rows="5"></textarea>
      </div>
      <div class="modal-footer">
        <button class="cancel-btn" (click)="showModal = false">Cancel</button>
        <button class="confirm-btn" (click)="confirmBooking()">Book Appointment</button>
      </div>
    </div>
  </div>
</div>

<!-- Add this CSS at the end of your file -->
<style>
.booking-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

.reason-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  resize: vertical;
  font-family: inherit;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancel-btn, .confirm-btn {
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
}

.cancel-btn {
  background: none;
  border: 1px solid #ddd;
}

.confirm-btn {
  background-color: #4285F4;
  color: white;
  border: none;
}
</style>
