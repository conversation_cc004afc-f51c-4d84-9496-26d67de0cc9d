import { Component, HostListener, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { BenefitsComponent } from "../benefits/benefits.component";
import { AboutComponent } from "../about/about.component";
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SimpleResetButtonComponent } from '../../../shared/components/simple-reset-button/simple-reset-button.component';

@Component({
  selector: 'app-landing',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    BenefitsComponent,
    AboutComponent,
    FormsModule,
    ReactiveFormsModule,
    SimpleResetButtonComponent
  ],
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css'],
})
export class LandingComponent implements OnInit {
  showScroll = false;

  // screenSize: number = window.innerWidth

  constructor(private router: Router) {}

  ngOnInit(): void {
  }

  @HostListener('window:scroll')
  onWindowScroll() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;
    this.showScroll = scrollPosition > 400;
  }

  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

}
