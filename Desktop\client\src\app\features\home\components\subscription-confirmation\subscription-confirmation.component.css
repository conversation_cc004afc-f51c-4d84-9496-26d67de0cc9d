.subscription-confirmation {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-light);
  padding: 2rem;
  transition: all 0.3s ease;
}

.confirmation-container {
  width: 100%;
  max-width: 600px;
  background: var(--bg-white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transform: translateY(0);
  opacity: 1;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.confirmation-content {
  padding: 2.5rem;
  text-align: center;
}

.confirmation-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background-color: var(--bg-icon-light);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn 0.5s ease-out 0.3s both;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.confirmation-title {
  color: var(--text-primary);
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  animation: fadeIn 0.5s ease-out 0.4s both;
}

.confirmation-message {
  color: var(--text-secondary);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  animation: fadeIn 0.5s ease-out 0.5s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  animation: fadeIn 0.5s ease-out 0.6s both;
}

.primary-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.primary-button:hover {
  background-color: var(--primary-color-80);
  transform: translateY(-1px);
}

.primary-button:active {
  transform: translateY(0);
}

.secondary-button {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  padding: 0.875rem 1.5rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-button:hover {
  background-color: var(--bg-light);
  color: var(--text-primary);
}

.loading-spinner {
  text-align: center;
  padding: 2.5rem;
}

.spinner {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: 3px solid rgba(25, 154, 142, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  color: #dc3545;
  padding: 1rem;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5px); }
  40%, 80% { transform: translateX(5px); }
}

.copy-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.copy-button:hover {
  background-color: var(--primary-color-80);
  transform: translateY(-1px);
}

.copy-button:active {
  transform: translateY(0);
}

.copy-button i {
  font-size: 1rem;
}

.copy-button.copied {
  background-color: var(--primary-color-80);
  animation: pulse 0.3s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.key-display {
  background: linear-gradient(145deg, var(--bg-white) 0%, rgba(249, 250, 251, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  margin: 2rem 0;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: cardFloat 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  position: relative;
  overflow: hidden;
}

.key-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg,
    var(--primary-color) 0%,
    var(--primary-color-80) 50%,
    var(--primary-color) 100%);
  animation: shimmer 2s infinite linear;
  background-size: 200% 100%;
}

.key-display::after {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  background: radial-gradient(circle at top right,
    rgba(25, 154, 142, 0.1) 0%,
    transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.key-display:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow:
    0 20px 35px -10px rgba(0, 0, 0, 0.08),
    0 10px 20px -8px rgba(0, 0, 0, 0.03);
  border-color: rgba(25, 154, 142, 0.2);
}

.key-display:hover::after {
  opacity: 1;
}

@keyframes cardFloat {
  0% {
    opacity: 0;
    transform: translateY(40px) scale(0.98);
  }
  60% {
    transform: translateY(-10px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.key-display pre {
  background: rgba(249, 250, 251, 0.6);
  backdrop-filter: blur(4px);
  padding: 1.5rem;
  border-radius: var(--border-radius-md);
  margin: 1rem 0;
  overflow-x: auto;
  position: relative;
  font-family: 'Fira Code', monospace;
  font-size: 0.95rem;
  line-height: 1.6;
  border: 1px solid rgba(25, 154, 142, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  animation: fadeScale 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.key-display pre::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
  transform: translateX(-100%);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%); }
  20% { transform: translateX(100%); }
  100% { transform: translateX(100%); }
}

.key-display h3 {
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) both;
  letter-spacing: -0.02em;
}

.key-display h3 i {
  color: var(--primary-color);
  font-size: 1.5rem;
  animation: iconPop 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

@keyframes iconPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.key-display .copy-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.key-display .label {
  font-size: 0.95rem;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  font-weight: 500;
  animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
  letter-spacing: -0.01em;
}

.next-steps {
  margin-top: 2.5rem;
  padding: 2rem;
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  border: 1px solid rgba(25, 154, 142, 0.1);
  animation: fadeScale 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
  position: relative;
  overflow: hidden;
}

.next-steps::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center,
    rgba(25, 154, 142, 0.05) 0%,
    transparent 50%);
  animation: rotate 15s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.next-steps h3 {
  color: var(--text-primary);
  margin: 0 0 1.5rem 0;
  font-size: 1.4rem;
  font-weight: 700;
  position: relative;
  letter-spacing: -0.02em;
}

.next-steps ul {
  list-style: none;
  padding: 0;
  margin: 0;
  position: relative;
}

.next-steps li {
  margin: 1rem 0;
  padding-left: 2rem;
  position: relative;
  color: var(--text-secondary);
  font-size: 1.05rem;
  line-height: 1.6;
  animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1) calc(0.5s + var(--li-index, 0) * 0.1s) both;
}

.next-steps li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: var(--primary-color);
  border-radius: 50%;
  opacity: 0.1;
  transition: all 0.3s ease;
}

.next-steps li::after {
  content: '→';
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-weight: bold;
  font-size: 0.9rem;
}

.next-steps li:hover::before {
  transform: translateY(-50%) scale(1.2);
  opacity: 0.2;
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeScale {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .key-display {
    padding: 1.5rem;
    margin: 1.5rem 0;
  }

  .key-display pre {
    font-size: 0.85rem;
    padding: 1.25rem;
  }

  .key-display h3 {
    font-size: 1.3rem;
  }

  .next-steps {
    padding: 1.5rem;
    margin-top: 2rem;
  }

  .next-steps h3 {
    font-size: 1.25rem;
  }

  .next-steps li {
    font-size: 1rem;
    padding-left: 1.75rem;
  }
}

@media (max-width: 480px) {
  .key-display {
    padding: 1.25rem;
    margin: 1.25rem 0;
  }

  .key-display pre {
    font-size: 0.8rem;
    padding: 1rem;
  }

  .key-display h3 {
    font-size: 1.2rem;
  }

  .next-steps {
    padding: 1.25rem;
  }

  .next-steps li {
    font-size: 0.95rem;
    padding-left: 1.5rem;
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .key-display {
    background: linear-gradient(145deg, rgba(17, 24, 39, 0.8) 0%, rgba(17, 24, 39, 0.6) 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .key-display pre {
    background: rgba(17, 24, 39, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .next-steps {
    background: rgba(17, 24, 39, 0.5);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .key-display::after {
    background: radial-gradient(circle at top right,
      rgba(25, 154, 142, 0.2) 0%,
      transparent 70%);
  }
}

.license-details {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.license-info {
  margin-top: 1rem;
}

.license-key-container {
  margin-top: 1.5rem;
}

code {
  background: #e9ecef;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 1.1rem;
  letter-spacing: 1px;
}

.key-display button {
  background-color: #199A8E;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.key-display button.copied {
  background-color: #199A8E;
}

.key-display button:hover {
  background-color:  var(--primary-color-80);
}

h1 {
  color: #343a40;
  margin-bottom: 1.5rem;
}

h2 {
  color: #495057;
  margin-bottom: 1rem;
}

p {
  color: #6c757d;
  line-height: 1.5;
}

strong {
  color: #495057;
}
