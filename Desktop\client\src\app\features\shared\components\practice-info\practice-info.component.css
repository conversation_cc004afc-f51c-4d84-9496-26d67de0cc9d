.payment-container {
  width: 100%;
  min-height: 832px;
  background: #F9FAFB;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.payment-card {
  width: 736px;
  background: white;
  box-shadow: 0px 4px 6px -4px rgba(0, 0, 0, 0.10);
  border-radius: 8px;
  padding: 32px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.header h2 {
  color: #111827;
  font-size: 22.69px;
  font-weight: 700;
  line-height: 32px;
  margin: 0;
}

.progress-dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #D1D5DB;
}

.dot.active {
  background: #199A8E;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: flex;
  gap: 24px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
}

label {
  display: block;
  color: #374151;
  font-size: 13.78px;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 4px;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

input, textarea {
  width: 100%;
  padding: 11.5px 12px 11.5px 41px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  color: #111827;
  font-size: 15.25px;
  line-height: 19px;
}

textarea {
  min-height: 90px;
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  color: #9CA3AF;
}

.input-with-icon i {
  position: absolute;
  left: 12px;
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

.submit-button {
  width: 100%;
  height: 40px;
  background: #199A8E;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 20px;
  font-weight: 700;
  line-height: 24px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.submit-button:hover {
  background: #158276;
}

.submit-button:disabled {
  background: #A7A7A7;
  cursor: not-allowed;
}

/* Icon styles */
.practice-icon, .doctor-icon, .email-icon, .phone-icon, .address-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.6;
}

/* Add responsive styles */
@media (max-width: 768px) {
  .payment-card {
    width: 100%;
    max-width: 736px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }
}
