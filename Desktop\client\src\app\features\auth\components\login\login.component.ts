import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterLink } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { HttpClientModule } from '@angular/common/http';
import { Db } from '../../../../db';
import { Storage } from '../../../../storage';
import { UserType } from '../../../../type';
import { CURRENT_DOCTOR } from '../../../../constant';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterLink, HttpClientModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css'],
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  loading = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private db: Db,
    private storage: Storage
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }

  ngOnInit(): void {
    
  }

  onSubmit() {
    if (this.loginForm.valid) {
      this.loading = true;
      const { email, password } = this.loginForm.value;

      this.authService.login(email, password).subscribe({
        next: (response) => {
          if (response.success) {
            const user = this.db.current_doctor();
            if (!user) {
              this.errorMessage = 'Error getting user information';
              this.loading = false;
              return;
            }

            if (user.role === 'DOCTOR') {
              this.router.navigate(['/dashboard']);
            } else {
              this.errorMessage = 'Invalid user role';
              this.loading = false;
            }
          }
        },
        error: (error) => {
          console.error('Login error:', error);
          this.errorMessage = error.message || 'Invalid email or password';
          this.loading = false;
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields correctly.';
      if (this.loginForm.get('email')?.errors) {
        this.errorMessage = this.getEmailErrorMessage();
      } else if (this.loginForm.get('password')?.errors) {
        this.errorMessage = this.getPasswordErrorMessage();
      }
    }
  }

  private redirectBasedOnRole(role: string): void {
    if (role.toLowerCase() === 'doctor') {
      this.router.navigate(['/dashboard']);
    }
  }

  getEmailErrorMessage(): string {
    const control = this.loginForm.get('email');
    if (control?.hasError('required')) {
      return 'Email is required';
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    return '';
  }

  getPasswordErrorMessage(): string {
    const control = this.loginForm.get('password');
    if (control?.hasError('required')) {
      return 'Password is required';
    }
    if (control?.hasError('minlength')) {
      return 'Password must be at least 6 characters long';
    }
    return '';
  }
}
