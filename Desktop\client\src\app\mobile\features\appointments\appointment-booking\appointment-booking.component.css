/* Message container and notifications */
.message-container {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 100;
  max-width: 350px;
  animation: slideIn 0.3s ease-out;
}

.message {
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.message i {
  font-size: 1.2rem;
}

.message.success {
  background-color: #e2f8f5;
  border-left: 4px solid #199a8e;
  color: #0f766e;
}

.message.error {
  background-color: #fee2e2;
  border-left: 4px solid #dc2626;
  color: #b91c1c;
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateX(30px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 576px) {
  .message-container {
    top: 70px;
    right: 10px;
    max-width: calc(100% - 20px);
  }
}

/* Base styles for the appointment container */
.appointment-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  font-family: system-ui, -apple-system, sans-serif;
  width: 100%;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 80px;
  min-height: 100vh;
  background-color: #f8f9fa;
  color: #334155;
}

/* Header styling */
.header-title {
  font-size: 1.6rem;
  font-weight: 700;
  margin: 16px 0 30px;
  color: #199a8e; /* Updated to requested teal color */
  text-align: center;
}

/* Sections styling */
.availableTimeSlots h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #199a8e;
  margin: 25px 0 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #199a8e;
}

/* Doctor cards section */
.doctor-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.doctor-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  border: 1px solid #e2e8f0;
}

.doctor-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.doctor-card.selected {
  border: 2px solid #199a8e;
}

.doctor-card-header {
  padding: 15px;
  background-color: #199a8e;
  color: white;
}

.doctor-card-body {
  padding: 15px;
}

.doctor-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.doctor-email {
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 15px;
}

.view-availability-btn {
  display: block;
  width: 100%;
  padding: 10px;
  background-color: #199a8e;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  text-align: center;
}

.view-availability-btn:hover {
  background-color: #0f766e;
}

/* Table styling with improved responsive design */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 30px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* Table head styling */
table thead {
  background-color: #199a8e; /* Updated to requested teal color */
}

table th,
table td {
  padding: 14px;
  text-align: left;
}

table thead tr td {
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

/* Table body styling */
table tbody tr {
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.2s ease;
}

table tbody tr:last-child {
  border-bottom: none;
}

table tbody tr:hover {
  background-color: #f8fafc;
}

/* Button styling */
table button {
  padding: 8px 16px;
  background-color: #199a8e; /* Updated to requested teal color */
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(25, 154, 142, 0.3);
}

table button:hover {
  background-color: #0f766e;
  transform: translateY(-1px);
}

table button:active {
  transform: translateY(0);
}

/* Availability section styling */
.availability-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.availability-section h1 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #199a8e;
  font-weight: 600;
  border-bottom: 2px solid #199a8e;
  padding-bottom: 10px;
}

/* Availability table */
.availability-table {
  width: 100%;
  overflow: visible;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
}

.availability-table th {
  background-color: #199a8e;
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  text-align: left;
  padding: 14px;
}

.availability-table td {
  padding: 14px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.95rem;
}

.availability-table tr:last-child td {
  border-bottom: none;
}

.availability-table tr:hover {
  background-color: #f8fafc;
}

/* Date grouped slots styles */
.date-group-container {
  margin-bottom: 15px;
}

.date-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f1f5f9;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 1px;
  transition: background-color 0.2s;
  border-left: 4px solid #199a8e;
}

.date-dropdown-header:hover {
  background-color: #e2e8f0;
}

.date-dropdown-header.active {
  background-color: #e2f8f5;
}

.date-title {
  font-weight: 600;
  color: #334155;
  display: flex;
  align-items: center;
  gap: 10px;
}

.slot-count {
  font-size: 0.8rem;
  background-color: #199a8e;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
}

.chevron-icon {
  transition: transform 0.3s ease;
}

.chevron-icon.active {
  transform: rotate(180deg);
}

.date-dropdown-content {
  background-color: white;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.date-dropdown-content.active {
  max-height: 800px;
  opacity: 1;
  padding: 15px;
  border: 1px solid #e2e8f0;
  border-top: none;
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
}

.time-slot-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.time-slot-item:hover {
  background-color: #e2f8f5;
  border-color: #199a8e;
}

.time-slot-info {
  display: flex;
  flex-direction: column;
}

.time-slot-range {
  font-weight: 500;
  color: #334155;
}

.time-slot-status {
  font-size: 0.75rem;
}

.time-slot-status.available {
  color: #16a34a;
}

.time-slot-status.booked {
  color: #dc2626;
}

.book-slot-btn {
  padding: 6px 12px;
  background-color: #199a8e;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.8rem;
}

.book-slot-btn:hover {
  background-color: #0f766e;
}

.book-slot-btn:disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
}

/* Status indicators */
.status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 50px;
  font-weight: 500;
  font-size: 0.85rem;
  text-align: center;
}

.status.available {
  background-color: #dcfce7;
  color: #16a34a;
}

.status.booked {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Button styling in availability table */
.availability-table button {
  padding: 8px 16px;
  background-color: #199a8e;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 120px;
}

.availability-table button:hover {
  background-color: #0f766e;
  box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
}

.availability-table button:disabled,
.availability-table button.disabled {
  background-color: #94a3b8;
  cursor: not-allowed;
  box-shadow: none;
  opacity: 0.7;
}

/* No availability message */
.no-availability {
  padding: 25px;
  text-align: center;
  background-color: #ffffff;
  border-radius: 10px;
  margin: 30px 0;
  color: #64748b;
  font-style: italic;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Table container for horizontal scrolling on small screens */
.table-container {
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Animation for new elements */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.appointment-container > div {
  animation: fadeIn 0.4s ease-out;
}

/* Loading indicator */
.loading-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
}

.loading-indicator::after {
  content: "";
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #199a8e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .appointment-container {
    padding: 15px;
    padding-bottom: 70px;
  }

  .header-title {
    font-size: 1.4rem;
    margin: 15px 0 25px;
  }

  table th,
  table td,
  .availability-table th,
  .availability-table td {
    padding: 12px 10px;
    font-size: 0.9rem;
  }

  .status {
    font-size: 0.75rem;
    padding: 5px 10px;
  }

  .availability-section {
    padding: 15px;
  }

  .availability-section h1 {
    font-size: 1.3rem;
    margin-bottom: 15px;
  }
}

@media (max-width: 576px) {
  .appointment-container {
    padding: 10px;
    padding-bottom: 70px;
  }

  .header-title {
    font-size: 1.3rem;
  }

  .doctor-cards {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  }

  table, .availability-table {
    font-size: 0.85rem;
  }

  table th,
  table td,
  .availability-table th,
  .availability-table td {
    padding: 10px 8px;
  }

  table button, 
  .availability-table button {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .status {
    font-size: 0.7rem;
    padding: 4px 8px;
  }

  .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  /* Fixed the invalid CSS selector */
  .time-selection .time-slots-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

@media (max-width: 576px) {
  .appointment-container {
    padding: 10px;
    padding-bottom: 70px;
  }

  .doctor-cards {
    grid-template-columns: 1fr;
  }
}

/* Ensure scrollable areas don't get hidden under navbar */
.table-container::-webkit-scrollbar {
  height: 6px;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: #b4b4b4;
  border-radius: 20px;
}

.table-container::-webkit-scrollbar-track {
  background-color: #f1f5f9;
}

/* Time selection section */
.time-selection {
  margin: 24px 0;
  padding: 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.time-slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
}

.time-slot {
  padding: 12px 8px;
  background: #e2f8f5;
  border: 1px solid #199a8e;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #199a8e;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-slot:hover {
  background: #199a8e;
  color: white;
  transform: translateY(-2px);
}

/* Animation for hover effects */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

table button:hover,
.availability-table button:hover:not(:disabled) {
  animation: pulse 0.5s ease-in-out;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 30px;
  background-color: white;
  border-radius: 10px;
  margin-top: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.empty-state-icon {
  font-size: 3rem;
  color: #199a8e;
  margin-bottom: 15px;
}

.empty-state h3 {
  color: #334155;
  margin-bottom: 10px;
}

.empty-state p {
  color: #64748b;
  max-width: 400px;
  margin: 0 auto;
}
