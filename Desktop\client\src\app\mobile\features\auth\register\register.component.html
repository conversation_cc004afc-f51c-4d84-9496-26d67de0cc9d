<div class="register-container">
  <div class="register-card">
    <div class="register-header">
      <i class="bi bi-person"></i>
      <h2>Create Account</h2>


    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <div class="input-container">
          <i class="bi bi-person-circle"></i>



          <input type="text" formControlName="fullname" placeholder="Enter your fullname" />
        </div>
        <span class="validation-error"
          *ngIf="registerForm.get('fullname')?.errors?.['required'] && registerForm.get('fullname')?.touched">
          Full name is required
        </span>
        <span class="validation-error"
          *ngIf="registerForm.get('fullname')?.errors?.['pattern'] && registerForm.get('fullname')?.touched">
          Please enter both your first and last name
        </span>
      </div>

      <div class="form-group">
        <div class="input-container">
          <i class="bi bi-envelope"></i>
          <input type="email" formControlName="email" placeholder="Enter your email" (blur)="onEmailBlur()" />
        </div>
        <span class="validation-error" *ngIf="emailErrorMessage">
          {{ emailErrorMessage }}
        </span>
      </div>

      <div class="form-group">
        <div class="input-container">
          <i class="bi bi-shield-lock"></i>
          <input type="password" formControlName="password" placeholder="Enter your password" />
        </div>
        <span class="validation-error"
          *ngIf="registerForm.get('password')?.errors && registerForm.get('password')?.touched">
          Password must be at least 6 characters long
        </span>
      </div>

      <div class="form-group">
        <div class="input-container">
          <i class="bi bi-shield-lock"></i>
          <input type="password" formControlName="confirmPassword" placeholder="Confirm your password" />
        </div>
        <span class="validation-error"
          *ngIf="registerForm.get('confirmPassword')?.errors?.['required'] && registerForm.get('confirmPassword')?.touched">
          Please confirm your password
        </span>
        <span class="validation-error"
          *ngIf="registerForm.get('confirmPassword')?.errors?.['passwordMismatch'] && registerForm.get('confirmPassword')?.touched">
          Passwords do not match
        </span>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <app-error-modal [show]="showErrorModal" (showChange)="showErrorModal = $event" [title]="errorModalTitle"
        [message]="errorModalMessage" [actionText]="errorModalAction" [onAction]="navigateToLogin.bind(this)"
        (close)="onModalClose()"></app-error-modal>

      <button type="submit" [disabled]="loading" class="submit-button">
        {{ loading ? 'Registering...' : 'Register' }}
        <app-loading-spinner *ngIf="loading"></app-loading-spinner>
      </button>
    </form>

    <p class="login-link">
      Already have an account? <a routerLink="/mobile/patient-dashboard">Login</a>
    </p>
  </div>
</div>
