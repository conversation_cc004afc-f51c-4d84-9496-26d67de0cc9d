import { Injectable, NgZone } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, of, throwError, from, switchMap, map, catchError, tap, delay } from 'rxjs';
import { doc, setDoc, serverTimestamp, getDoc } from '@angular/fire/firestore';
import { getFirestore } from '@angular/fire/firestore';

import { AuthService } from '../../services/auth.service';
import { ApiUrlService } from '../../../core/services/api-url.service';
import { LocalStorageService } from '../../services/local-storage.service';

export interface PatientProfile {
  id?: string;
  fullName: string;
  firstName?: string;
  lastName?: string;
  phoneNumber: string;
  email: string;
  profilePicture?: string | null;
  address?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: any; // Firebase timestamp can be different types
}

interface ApiResponse {
  success: boolean;
  message: string;
  data?: PatientProfile;
}

@Injectable({
  providedIn: 'root'
})
export class PatientProfileService {
  private profileSubject = new BehaviorSubject<PatientProfile | null>(null);
  currentProfile$ = this.profileSubject.asObservable();

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private apiUrlService: ApiUrlService,
    private storage: LocalStorageService,
    private ngZone: NgZone
  ) {
    this.initializeProfile();
  }

  private initializeProfile(): void {
    // Fetch directly from Firebase instead of local storage
    this.fetchProfileFromFirebase().subscribe(
      profile => {
        if (profile) {
          console.log('Successfully loaded profile from Firebase:', profile);
        } else {
          console.warn('No profile found in Firebase during initialization');
        }
      },
      error => {
        console.error('Error loading profile from Firebase:', error);
      }
    );
  }

  private loadProfileFromStorage(): void {
    // This method is kept for backward compatibility but won't be called during initialization
    const storedProfile = localStorage.getItem('patient_profile');
    if (storedProfile) {
      try {
        const profile = JSON.parse(storedProfile);
        this.profileSubject.next(profile);
      } catch (e) {
        console.error('Error parsing stored profile', e);
      }
    }
  }

  private verifyProfileWithServer(localProfile: PatientProfile): void {
    const token = this.authService.getAuthToken();
    if (!token) return;

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // Use the ApiUrlService to construct the URL
    const profileEndpoint = this.apiUrlService.getUrl('patients/profile/info');
    console.log('Verifying profile with server at:', profileEndpoint);

    this.http.get<any>(profileEndpoint, { headers })
      .subscribe({
        next: (response) => {
          if (response.success && response.data) {
            // Update local storage if server data is different
            if (JSON.stringify(response.data) !== JSON.stringify(localProfile)) {
              this.saveProfileToStorage(response.data);
              this.profileSubject.next(response.data);
            }
          }
        },
        error: (error) => console.error('Error verifying profile with server:', error)
      });
  }

  private saveProfileToStorage(profile: PatientProfile): void {
    localStorage.setItem('patient_profile', JSON.stringify(profile));
    localStorage.setItem('user_info', JSON.stringify({
      full_name: profile.fullName,
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: profile.email,
      phoneNumber: profile.phoneNumber,
      profilePicture: profile.profilePicture,
      address: profile.address,
      role: profile.role,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt
    }));
  }

  getCurrentProfile(): PatientProfile | null {
    // Get the current profile from the subject
    const currentProfile = this.profileSubject.value;

    // If we don't have a profile in memory, try to fetch from Firebase
    // This will happen asynchronously, so the initial return might be null
    if (!currentProfile) {
      this.fetchProfileFromFirebase().subscribe();
    }

    return currentProfile;
  }

  fetchLatestProfile(): Observable<PatientProfile> {
    const token = this.authService.getAuthToken();
    if (!token) {
      console.error('No auth token found');
      return of(this.getCurrentProfile() || {} as PatientProfile);
    }

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${token}`
    });

    // Use the ApiUrlService to construct the URL
    const profileEndpoint = this.apiUrlService.getUrl('patients/profile/info');
    console.log('Fetching profile from server at:', profileEndpoint);

    return this.http.get<any>(profileEndpoint, { headers })
      .pipe(
        map(response => {
          if (response.success && response.data) {
            const profile = response.data;
            this.profileSubject.next(profile);
            this.saveProfileToStorage(profile);
            return profile;
          }
          throw new Error('Failed to fetch latest profile');
        }),
        catchError(error => {
          console.error('Error fetching latest profile:', error);
          return of(this.getCurrentProfile() || {} as PatientProfile);
        })
      );
  }

  updateProfile(profile: PatientProfile): Observable<PatientProfile> {
    console.log('Updating profile with data:', profile);

    // First try to update via the API endpoint
    return this.updateProfileViaApi(profile).pipe(
      // Whether the API call succeeds or fails, also save to Firebase
      switchMap(updatedProfile => {
        // Save to Firestore regardless of API result
        return this.uploadProfileToFirestore(updatedProfile || profile).pipe(
          // Return the profile data that came from the API if available
          map(() => updatedProfile || profile),
          catchError(firestoreError => {
            console.error('Failed to save profile to Firestore:', firestoreError);
            // If Firestore fails but API succeeded, just return the profile from API
            return of(updatedProfile || profile);
          })
        );
      })
    );
  }

  // Update profile via API endpoint
  private updateProfileViaApi(profile: PatientProfile): Observable<PatientProfile | null> {
    const token = this.authService.getAuthToken();

    if (!token) {
      console.error('No auth token found for API call');
      return of(null);
    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    const userData = {
      firstName: profile.fullName.split(' ')[0],
      lastName: profile.fullName.split(' ').slice(1).join(' '),
      email: profile.email,
      phoneNumber: profile.phoneNumber,
      profilePicture: profile.profilePicture
    };

    console.log('Updating profile via API with data:', userData);

    // Use the ApiUrlService to construct the URL
    const updateEndpoint = this.apiUrlService.getUrl('patients/profile/update');
    console.log('Update profile endpoint:', updateEndpoint);

    return this.http.post<ApiResponse>(updateEndpoint, userData, { headers })
      .pipe(
        tap(response => console.log('Server response:', response)),
        map((response: ApiResponse) => {
          if (response.success && response.data) {
            const updatedProfile = response.data;
            this.profileSubject.next(updatedProfile);
            this.saveProfileToStorage(updatedProfile);
            return updatedProfile;
          } else {
            throw new Error('Server did not return success response');
          }
        }),
        catchError(error => {
          console.error('Error updating profile via API:', error);
          return of(null);
        })
      );
  }

  updateProfilePicture(profilePicture: string): Observable<PatientProfile> {
    console.log('Updating profile picture with base64 data');

    const currentProfile = this.getCurrentProfile();

    // If no profile exists, create one from auth service data
    if (!currentProfile) {
      console.log('No existing profile found, creating one from auth data');
      return this.createProfileFromAuth(profilePicture);
    }

    const token = this.authService.getAuthToken();
    if (!token) {
      console.error('No auth token found');
      // Instead of returning an error, fallback to Firebase
      console.log('No auth token found, falling back to Firebase');
      return this.uploadProfileToFirestore({
        ...currentProfile,
        profilePicture: profilePicture
      });
    }

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    });

    // Prepare the data with just the profile picture
    const updateData = {
      profilePicture: profilePicture
    };

    console.log('Sending profile picture update request');

    // Use the ApiUrlService to construct the URL
    const updatePictureEndpoint = this.apiUrlService.getUrl('patients/profile/update');
    console.log('Update picture endpoint:', updatePictureEndpoint);

    return this.http.post<ApiResponse>(updatePictureEndpoint, updateData, { headers })
      .pipe(
        tap(response => console.log('Profile picture update response:', response)),
        map((response: ApiResponse) => {
          if (response.success && response.data) {
            const updatedProfile = response.data;

            // Update the profile in memory and storage
            this.profileSubject.next(updatedProfile);
            this.saveProfileToStorage(updatedProfile);

            // Also update Firebase for consistency
            console.log('API updated successfully, also updating Firebase for consistency');
            this.uploadProfileToFirestore(updatedProfile).subscribe();

            // Also update the user info in auth service
            this.authService.updateUserInfoFromProfile(updatedProfile);

            return updatedProfile;
          } else {
            throw new Error('Server did not return success response');
          }
        }),
        catchError(error => {
          console.error('Error updating profile picture:', error);

          // Instead of propagating the error, fall back to Firebase
          console.log('API failed, falling back to Firebase for profile picture update');

          // Update the current profile with the new picture
          const updatedProfile = {
            ...currentProfile,
            profilePicture: profilePicture,
            updatedAt: new Date().toISOString()
          };

          // Upload to Firebase and return the result
          return this.uploadProfileToFirestore(updatedProfile).pipe(
            // After Firebase update, refresh from Firebase to ensure we have latest data
            switchMap(uploadedProfile => {
              // Wait a moment for Firebase to process the update
              return of(uploadedProfile).pipe(
                delay(500),
                switchMap(() => this.fetchProfileFromFirebase().pipe(
                  // Filter out null results, use uploaded profile instead
                  map(fetchedProfile => fetchedProfile || uploadedProfile),
                  // If fetch fails, just use the uploaded profile
                  catchError(() => of(uploadedProfile))
                ))
              );
            })
          );
        })
      );
  }

  // Create a new profile using auth data and add the profile picture
  private createProfileFromAuth(profilePicture: string): Observable<PatientProfile> {
    console.log('Creating new profile with profile picture');

    // Get user info from auth service
    return from(this.authService.getCurrentUser()).pipe(
      switchMap(user => {
        if (!user) {
          return throwError(() => new Error('No authenticated user found'));
        }

        const userInfo = this.authService.getUserInfo();
        if (!userInfo) {
          console.warn('No user info available, creating minimal profile');
        }

        // Split name into first and last name
        const fullName = user.displayName || (userInfo?.full_name || '');
        const nameParts = fullName.split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

        // Create a complete profile with auth data
        const newProfile: PatientProfile = {
          id: user.id,
          fullName: fullName,
          firstName: firstName,
          lastName: lastName,
          email: user.email || (userInfo?.email || ''),
          phoneNumber: userInfo?.phoneNumber || '',
          profilePicture: profilePicture,
          address: userInfo?.address || '',
          role: userInfo?.role || 'PATIENT',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        console.log('Created new profile from auth data:', newProfile);

        // Update to Firebase
        return this.uploadProfileToFirestore(newProfile);
      })
    );
  }

  // Fetch profile directly from Firebase
  fetchProfileFromFirebase(): Observable<PatientProfile | null> {
    console.log('Fetching profile directly from Firebase');

    return from(this.authService.getCurrentUser()).pipe(
      switchMap(user => {
        if (!user || !user.id) {
          console.warn('No authenticated user found for Firebase fetch');
          return of(null);
        }

        // Get Firestore
        const db = this.getFirestore();
        if (!db) {
          console.error('Firestore not available');
          return of(null);
        }

        // Run Firebase operations outside Angular zone
        return from(this.ngZone.runOutsideAngular(async () => {
          try {
            // Create reference to patient document with type assertion
            const userId = user.id as string;
            const patientDocRef = doc(db, 'patients', userId);
            const docSnap = await getDoc(patientDocRef);

            if (docSnap.exists()) {
              console.log('Firebase profile data found:', docSnap.data());

              // Convert Firestore data to PatientProfile
              const data = docSnap.data();
              const profile: PatientProfile = {
                id: userId,
                fullName: data['fullName'] || '',
                firstName: data['firstName'] || '',
                lastName: data['lastName'] || '',
                email: data['email'] || '',
                phoneNumber: data['phoneNumber'] || '',
                profilePicture: data['profilePicture'] || null,
                address: data['address'] || '',
                role: data['role'] || 'PATIENT',
                createdAt: data['createdAt'] ?
                  (typeof data['createdAt'] === 'string' ?
                    data['createdAt'] :
                    new Date(data['createdAt'].seconds * 1000).toISOString()) :
                  undefined,
                updatedAt: data['updatedAt'] || null
              };

              return profile;
            } else {
              console.log('No profile found in Firebase for user:', userId);
              return null;
            }
          } catch (error) {
            console.error('Error fetching profile from Firebase:', error);
            return null;
          }
        })).pipe(
          tap(profile => {
            // Update local storage and subject inside Angular zone
            this.ngZone.run(() => {
              if (profile) {
                this.profileSubject.next(profile);
                this.saveProfileToStorage(profile);
              }
            });
          })
        );
      }),
      catchError(error => {
        console.error('Error in fetchProfileFromFirebase:', error);
        return of(null);
      })
    );
  }

  // Upload profile to Firestore
  private uploadProfileToFirestore(profile: PatientProfile): Observable<PatientProfile> {
    return from(this.authService.getCurrentUser()).pipe(
      switchMap(user => {
        if (!user || !user.id) {
          return throwError(() => new Error('No authenticated user found'));
        }

        // Get Firestore from auth service
        const db = this.getFirestore();
        if (!db) {
          return throwError(() => new Error('Firestore not available'));
        }

        // Run Firebase operations outside Angular zone
        return from(this.ngZone.runOutsideAngular(async () => {
          try {
            // Get user info for additional fields
            const userInfo = this.authService.getUserInfo();
            const userId = user.id as string;

            // Create or update the patient document
            const patientDocRef = doc(db, 'patients', userId);

            // Split the full name into first and last name
            const nameParts = profile.fullName.split(' ');
            const firstName = nameParts[0] || '';
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

            // Get the current timestamp for createdAt if it's a new record
            const now = new Date().toISOString();

            // Prepare data for Firestore
            const patientData = {
              fullName: profile.fullName,
              firstName: firstName,
              lastName: lastName,
              email: profile.email,
              phoneNumber: profile.phoneNumber || '',
              profilePicture: profile.profilePicture || null,
              address: profile.address || userInfo?.address || '',
              role: profile.role || userInfo?.role || 'PATIENT',
              updatedAt: serverTimestamp(),
              // Only set createdAt if it's a new document or it's already provided in the profile
              createdAt: profile.createdAt || (profile.id ? undefined : now)
            };

            // Save to Firestore
            await setDoc(patientDocRef, patientData, { merge: true });
            return { ...profile, ...patientData };
          } catch (error) {
            console.error('Error uploading profile to Firestore:', error);
            throw error;
          }
        })).pipe(
          tap(updatedProfile => {
            // Update local storage and subject inside Angular zone
            this.ngZone.run(() => {
              this.profileSubject.next(updatedProfile);
              this.saveProfileToStorage(updatedProfile);
            });
          })
        );
      })
    );
  }

  // Helper method to get Firestore
  private getFirestore(): any {
    try {
      // Import Firestore dynamically
      return getFirestore();
    } catch (error) {
      console.error('Error getting Firestore instance:', error);
      return null;
    }
  }

  // Helper to refresh the profile from Firebase
  refreshProfileFromFirebase(): Observable<PatientProfile | null> {
    // Clear any existing profile first
    this.profileSubject.next(null);

    // Fetch fresh data from Firebase
    return this.fetchProfileFromFirebase();
  }
}
