import { Injectable, NgZone } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, from, of, throwError } from 'rxjs';
import { catchError, map, tap, switchMap } from 'rxjs/operators';
import {
  Firestore,
  collection,
  doc,
  addDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  collectionData,
  Timestamp
} from '@angular/fire/firestore';
import { environment } from '../../../../environments/environment';
import { AuthService, User } from '../../../core/services/auth.service';

export interface Appointment {
  id?: string;
  doctorId: string;
  patientId: string;
  appointmentDate: Date;
  status: 'scheduled' | 'completed' | 'cancelled';
  reason: string;
  notes?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppointmentWithDetails extends Appointment {
  doctorName?: string;
  patientName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AppointmentService {
  private apiUrl = environment.apiUrl;
  private appointmentsCollection = 'appointments';
  private selectedDoctorSubject = new BehaviorSubject<{doctorId: string, doctorName: string} | null>(null);
  private availableSlotsSubject = new BehaviorSubject<Appointment[]>([]);
  public selectedDoctor$ = this.selectedDoctorSubject.asObservable();
  public availableSlots$ = this.availableSlotsSubject.asObservable();

  constructor(
    private http: HttpClient,
    private firestore: Firestore,
    private authService: AuthService,
    private ngZone: NgZone
  ) {}

  setSelectedDoctor(doctorId: string, doctorName: string): void {
    this.selectedDoctorSubject.next({ doctorId, doctorName });
    this.loadAvailableSlots();
  }

  getCurrentDoctor() {
    return this.selectedDoctorSubject.getValue();
  }

  public loadAvailableSlots(): Observable<Appointment[]> {
    return this.ngZone.runOutsideAngular(() => {
      const doctorInfo = this.getCurrentDoctor();
      if (!doctorInfo || !doctorInfo.doctorId) {
        return this.ngZone.run(() => {
          console.debug('No doctor selected yet, skipping available slots loading');
          this.availableSlotsSubject.next([]);
          return of([]);
        });
      }

      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
      const availabilityQuery = query(
        appointmentsRef,
        where('doctorId', '==', doctorInfo.doctorId),
        where('status', '==', 'scheduled'),
        where('appointmentDate', '>=', new Date()),
        orderBy('appointmentDate', 'asc')
      );

      return collectionData(availabilityQuery, { idField: 'id' }).pipe(
        map(slots => {
          return this.ngZone.run(() => {
            const mappedSlots = slots.map(slot => ({
              ...slot,
              appointmentDate: (slot['appointmentDate'] as Timestamp).toDate()
            })) as Appointment[];

            this.availableSlotsSubject.next(mappedSlots);
            return mappedSlots;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error loading available slots:', error);
            this.availableSlotsSubject.next([]);
            return of([]);
          });
        })
      );
    });
  }

  createAppointment(appointment: Omit<Appointment, 'id'>): Observable<string> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
      const newAppointment = {
        ...appointment,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return from(addDoc(appointmentsRef, newAppointment)).pipe(
        map(docRef => {
          return this.ngZone.run(() => {
            // Reload available slots inside the Angular zone
            this.loadAvailableSlots().subscribe();
            return docRef.id;
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error creating appointment:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getAppointmentById(id: string): Observable<Appointment> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentDocRef = doc(this.firestore, `${this.appointmentsCollection}/${id}`);
      return from(getDoc(appointmentDocRef)).pipe(
        map(docSnap => {
          return this.ngZone.run(() => {
            if (docSnap.exists()) {
              const data = docSnap.data();
              return {
                id: docSnap.id,
                ...data,
                appointmentDate: (data['appointmentDate'] as Timestamp).toDate()
              } as Appointment;
            } else {
              throw new Error('Appointment not found');
            }
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching appointment:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  updateAppointment(id: string, appointment: Partial<Appointment>): Observable<void> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentDocRef = doc(this.firestore, `${this.appointmentsCollection}/${id}`);
      const updatedAppointment = {
        ...appointment,
        updatedAt: new Date()
      };

      return from(updateDoc(appointmentDocRef, updatedAppointment)).pipe(
        tap(() => {
          this.ngZone.run(() => {
            // Reload available slots inside the Angular zone
            this.loadAvailableSlots().subscribe();
          });
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error updating appointment:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  cancelAppointment(id: string): Observable<void> {
    return this.updateAppointment(id, { status: 'cancelled' });
  }

  getPatientAppointments(patientId: string): Observable<Appointment[]> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
      const patientQuery = query(
        appointmentsRef,
        where('patientId', '==', patientId),
        orderBy('appointmentDate', 'desc')
      );

      return collectionData(patientQuery, { idField: 'id' }).pipe(
        map(appointments => {
          return this.ngZone.run(() => appointments.map(appt => ({
            ...appt,
            appointmentDate: (appt['appointmentDate'] as Timestamp).toDate()
          })) as Appointment[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching patient appointments:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getDoctorAppointments(doctorId: string): Observable<Appointment[]> {
    return this.ngZone.runOutsideAngular(() => {
      const appointmentsRef = collection(this.firestore, this.appointmentsCollection);
      const doctorQuery = query(
        appointmentsRef,
        where('doctorId', '==', doctorId),
        orderBy('appointmentDate', 'desc')
      );

      return collectionData(doctorQuery, { idField: 'id' }).pipe(
        map(appointments => {
          return this.ngZone.run(() => appointments.map(appt => ({
            ...appt,
            appointmentDate: (appt['appointmentDate'] as Timestamp).toDate()
          })) as Appointment[]);
        }),
        catchError(error => {
          return this.ngZone.run(() => {
            console.error('Error fetching doctor appointments:', error);
            return throwError(() => error);
          });
        })
      );
    });
  }

  getAppointmentsWithUserDetails(appointments: Appointment[]): Observable<AppointmentWithDetails[]> {
    return this.ngZone.runOutsideAngular(() => {
      if (!appointments.length) return of([]);

      // Get unique user IDs
      const doctorIds = [...new Set(appointments.map(a => a.doctorId))];
      const patientIds = [...new Set(appointments.map(a => a.patientId))];

      // Create a map of user IDs to user data
      const usersMap = new Map<string, User>();

      // Function to fetch user data and add to map
      const fetchUsers = (userIds: string[]): Observable<void> => {
        if (!userIds.length) return of(void 0);

        const promises = userIds.map(uid => {
          return this.ngZone.runOutsideAngular(() => {
            const userDocRef = doc(this.firestore, `users/${uid}`);
            return getDoc(userDocRef).then(docSnap => {
              if (docSnap.exists()) {
                usersMap.set(uid, { uid, ...docSnap.data() } as User);
              }
            });
          });
        });

        return from(Promise.all(promises)).pipe(map(() => void 0));
      };

      // Fetch all users then transform appointments
      return from(fetchUsers(doctorIds)).pipe(
        switchMap(() => fetchUsers(patientIds)),
        map(() => {
          return this.ngZone.run(() => {
            return appointments.map(appointment => {
              const doctor = usersMap.get(appointment.doctorId);
              const patient = usersMap.get(appointment.patientId);

              return {
                ...appointment,
                doctorName: doctor ? `${doctor.firstName} ${doctor.lastName}` : 'Unknown Doctor',
                patientName: patient ? `${patient.firstName} ${patient.lastName}` : 'Unknown Patient'
              } as AppointmentWithDetails;
            });
          });
        })
      );
    });
  }
}
