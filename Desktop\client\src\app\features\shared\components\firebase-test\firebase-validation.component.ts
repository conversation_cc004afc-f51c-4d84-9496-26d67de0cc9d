import { Component, OnInit, NgZone, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import {
  Firestore,
  doc,
  getDoc,
  setDoc,
  collection,
  addDoc,
  deleteDoc,
  DocumentReference,
  collectionData,
  query,
  limit
} from '@angular/fire/firestore';
import { Auth, onAuthStateChanged, User } from '@angular/fire/auth';
import { Subscription } from 'rxjs';

interface TestResult {
  operation: string;
  path: string;
  success: boolean;
  error?: string;
  data?: any;
}

@Component({
  selector: 'app-firebase-validation',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './firebase-validation.component.html',
  styles: []
})
export class FirebaseValidationComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  private authSubscription: Subscription | null = null;

  testOperation = 'read';
  testCollection = 'public_test';
  testDocId = '';

  results: TestResult[] = [];

  constructor(
    private firestore: Firestore,
    private auth: Auth,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    // Use NgZone properly to avoid Firebase Zone.js warnings
    this.ngZone.runOutsideAngular(() => {
      this.authSubscription = new Subscription();
      const unsubscribe = onAuthStateChanged(this.auth, (user) => {
        this.ngZone.run(() => {
          this.currentUser = user;
        });
      });

      // Store the unsubscribe function in our subscription for cleanup
      this.authSubscription.add(() => unsubscribe());
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions when component is destroyed
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
  }

  createPublicTestDoc(): void {
    if (!this.currentUser) {
      alert('Please log in first');
      return;
    }

    this.ngZone.runOutsideAngular(() => {
      const testCollection = collection(this.firestore, 'public_test');

      addDoc(testCollection, {
        message: 'Public test document created by validation component',
        createdBy: this.currentUser?.uid,
        createdAt: new Date().toISOString(),
        email: this.currentUser?.email
      })
        .then(docRef => {
          this.ngZone.run(() => {
            this.results.unshift({
              operation: 'Create Public Document',
              path: `public_test/${docRef.id}`,
              success: true,
              data: {
                id: docRef.id,
                message: 'Public test document created successfully'
              }
            });
          });
        })
        .catch(error => {
          this.ngZone.run(() => {
            this.results.unshift({
              operation: 'Create Public Document',
              path: 'public_test/new_doc',
              success: false,
              error: error.message
            });
          });
        });
    });
  }

  runValidationTest(): void {
    if (!this.currentUser) {
      alert('Please log in first');
      return;
    }

    const path = `${this.testCollection}/${this.testDocId || 'new_doc_' + Date.now()}`;

    switch (this.testOperation) {
      case 'read':
        this.testRead(path);
        break;
      case 'write':
        this.testWrite(path);
        break;
      case 'update':
        this.testUpdate(path);
        break;
      case 'delete':
        this.testDelete(path);
        break;
    }
  }

  testRead(path: string): void {
    this.ngZone.runOutsideAngular(() => {
      const docRef = doc(this.firestore, path);

      getDoc(docRef).then(docSnap => {
        this.ngZone.run(() => {
          if (docSnap.exists()) {
            this.results.unshift({
              operation: 'Read',
              path,
              success: true,
              data: docSnap.data()
            });
          } else {
            this.results.unshift({
              operation: 'Read',
              path,
              success: true,
              data: 'Document does not exist'
            });
          }
        });
      }).catch(error => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Read',
            path,
            success: false,
            error: error.message
          });
        });
      });
    });
  }

  testWrite(path: string): void {
    this.ngZone.runOutsideAngular(() => {
      const docRef = doc(this.firestore, path);
      const testData = {
        createdBy: this.currentUser?.uid,
        createdAt: new Date().toISOString(),
        testValue: 'Test document ' + Date.now(),
        userId: this.currentUser?.uid
      };

      setDoc(docRef, testData).then(() => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Write',
            path,
            success: true,
            data: testData
          });
        });
      }).catch(error => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Write',
            path,
            success: false,
            error: error.message
          });
        });
      });
    });
  }

  testUpdate(path: string): void {
    this.ngZone.runOutsideAngular(() => {
      const docRef = doc(this.firestore, path);
      const updateData = {
        updatedBy: this.currentUser?.uid,
        updatedAt: new Date().toISOString(),
        lastUpdated: Date.now()
      };

      setDoc(docRef, updateData, { merge: true }).then(() => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Update',
            path,
            success: true,
            data: updateData
          });
        });
      }).catch(error => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Update',
            path,
            success: false,
            error: error.message
          });
        });
      });
    });
  }

  testDelete(path: string): void {
    this.ngZone.runOutsideAngular(() => {
      const docRef = doc(this.firestore, path);

      deleteDoc(docRef).then(() => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Delete',
            path,
            success: true
          });
        });
      }).catch(error => {
        this.ngZone.run(() => {
          this.results.unshift({
            operation: 'Delete',
            path,
            success: false,
            error: error.message
          });
        });
      });
    });
  }
}
