.payment-container {
  width: 100%;
  min-height: 832px;
  background: #F3F3F3;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.payment-card {
  width: 736px;
  background: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  border-radius: 12px;
  padding: 32px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Payment Processing Overlay */
.payment-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  backdrop-filter: blur(3px);
  transition: opacity 0.3s ease;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.payment-processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80%;
  max-width: 400px;
  padding: 20px;
}

/* Loader Animation */
.loader {
  position: relative;
  width: 70px;
  height: 70px;
  margin-bottom: 32px;
  border-radius: 50%;
  background: rgba(25, 154, 142, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 0 0 8px rgba(25, 154, 142, 0.03);
}

.loader::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #199A8E;
  animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
}

.loader::after {
  content: '';
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: rgba(25, 154, 142, 0.6);
  animation: spin 1.8s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite reverse;
}

.loader.verifying::before {
  border-top-color: #3B82F6;
}

.loader.verifying::after {
  border-top-color: rgba(59, 130, 246, 0.6);
  animation-duration: 1.5s;
}

.loader.success {
  background: rgba(16, 185, 129, 0.08);
  box-shadow: 0 0 0 8px rgba(16, 185, 129, 0.05);
}

.loader.success::before,
.loader.success::after {
  display: none;
}

.loader.success::before {
  content: '✓';
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0;
  animation: checkmark 0.8s cubic-bezier(0.19, 1, 0.22, 1) forwards;
  border: none;
}

@keyframes checkmark {
  0% {
    font-size: 0;
    background: rgba(16, 185, 129, 0);
  }
  50% {
    font-size: 40px;
    background: #10B981;
    width: 70px;
    height: 70px;
    border-radius: 50%;
  }
  100% {
    font-size: 36px;
    background: #10B981;
    width: 70px;
    height: 70px;
    border-radius: 50%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-message {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 40px;
  text-align: center;
  min-height: 27px;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Processing Steps */
.processing-steps {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  position: relative;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  z-index: 2;
}

.step-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #F3F4F6;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.5s ease;
  position: relative;
  box-shadow: 0 0 0 4px rgba(243, 244, 246, 0.5);
}

.step.active .step-indicator {
  background: #199A8E;
  box-shadow: 0 0 0 4px rgba(25, 154, 142, 0.15);
  transform: scale(1.1);
}

.step.completed .step-indicator {
  background: #10B981;
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.15);
}

.step.completed .step-indicator::after {
  content: '✓';
  color: white;
  font-size: 14px;
  font-weight: bold;
  opacity: 0;
  animation: fadeIn 0.3s ease 0.2s forwards;
}

.step-connector {
  height: 3px;
  background: #E5E7EB;
  flex: 1;
  margin: 0 4px;
  margin-bottom: 38px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.step-connector::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 0;
  background: #10B981;
  transition: width 0.5s ease;
}

.step:nth-child(3) ~ .step-connector::after {
  width: 100%;
}

.step:nth-child(5) ~ .step-connector::after {
  width: 100%;
}

.step-label {
  font-size: 14px;
  color: #6B7280;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.step.active .step-label {
  color: #111827;
  font-weight: 600;
  transform: translateY(-2px);
}

.step.completed .step-label {
  color: #10B981;
  font-weight: 600;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.header h2 {
  color: #111827;
  font-size: 22.69px;
  font-weight: 700;
  line-height: 32px;
  margin: 0;
}

.progress-dots {
  display: flex;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #D1D5DB;
  transition: background-color 0.3s ease;
}

.dot.active {
  background: #199A8E;
}

/* Form Styles */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: flex;
  gap: 24px;
}

.form-group {
  flex: 1;
}

.form-group.full-width {
  width: 100%;
}

label {
  display: block;
  color: #374151;
  font-size: 13.78px;
  font-weight: 500;
  line-height: 20px;
  margin-bottom: 6px;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

input {
  width: 100%;
  padding: 12px 14px 12px 42px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  color: #111827;
  font-size: 15.25px;
  line-height: 19px;
  transition: all 0.2s ease;
}

input:focus {
  outline: none;
  border-color: #199A8E;
  box-shadow: 0 0 0 2px rgba(25, 154, 142, 0.1);
}

input.ng-invalid.ng-touched {
  border-color: #EF4444;
}

input::placeholder {
  color: #9CA3AF;
}

.card-icon {
  position: absolute;
  left: 12px;
  width: 20px;
  height: 20px;
  opacity: 0.6;
}

.error-message {
  color: #EF4444;
  font-size: 12px;
  margin-top: 6px;
  min-height: 16px;
  transition: all 0.2s ease;
}

/* Summary Box Styles */
.summary-box {
  background: #F9FAFB;
  border-radius: 10px;
  padding: 28px 20px;
  margin-top: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 28px;
}

.description {
  color: #4B5563;
  font-size: 15.38px;
  line-height: 24px;
}

.user-details {
  font-size: 14px;
  color: #6B7280;
  margin-top: 4px;
}

.amount {
  color: #111827;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #E5E7EB;
}

.total-label {
  color: #111827;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
}

.total-amount {
  color: #111827;
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
}

.submit-button {
  width: 100%;
  height: 44px;
  background: #199A8E;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-button:hover {
  background: #158276;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-button:active {
  transform: translateY(0);
  box-shadow: none;
}

.submit-button:disabled {
  background: #A7A7A7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

@media (max-width: 768px) {
  .payment-card {
    width: 100%;
    max-width: 736px;
    padding: 24px;
  }

  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .processing-steps {
    flex-direction: column;
    gap: 20px;
    margin-top: 0;
  }

  .step {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    gap: 16px;
  }

  .step-indicator {
    margin-bottom: 0;
  }

  .step-label {
    text-align: left;
  }

  .step-connector {
    width: 3px;
    height: 20px;
    margin: 0;
    margin-left: 12px;
  }
}
