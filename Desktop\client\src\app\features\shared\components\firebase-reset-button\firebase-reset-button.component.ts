import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FirebaseDbService } from '../../../../core/services/firebase-db.service';

@Component({
  selector: 'app-firebase-reset-button',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <!-- Prominent Red Reset Button -->
    <div class="firebase-reset-button-container" style="position: fixed; top: 10px; right: 10px; z-index: 9999;">
      <div class="bg-red-600 text-white p-4 rounded-lg shadow-lg border-2 border-red-700" style="min-width: 300px;">
        
        <!-- Simple Button State -->
        <div *ngIf="!showConfirmation && !loading && !resetResult">
          <div class="flex items-center justify-between mb-3">
            <h3 class="font-bold text-lg">🔥 DEV TOOL</h3>
            <button 
              (click)="toggleVisibility()"
              class="text-red-200 hover:text-white text-sm">
              {{ isMinimized ? '▼' : '▲' }}
            </button>
          </div>
          
          <div [hidden]="isMinimized">
            <p class="text-sm mb-3 text-red-100">
              Reset Firebase Database (Delete ALL data)
            </p>
            <button 
              (click)="showConfirmation = true"
              class="w-full bg-red-800 hover:bg-red-900 text-white font-bold py-2 px-4 rounded transition duration-200">
              🗑️ RESET FIREBASE
            </button>
          </div>
        </div>

        <!-- Confirmation State -->
        <div *ngIf="showConfirmation && !loading">
          <h3 class="font-bold text-lg mb-3">⚠️ CONFIRM RESET</h3>
          <p class="text-sm mb-3 text-red-100">
            This will DELETE ALL data permanently!
          </p>
          
          <div class="space-y-2 mb-4">
            <label class="flex items-center text-sm">
              <input 
                type="checkbox" 
                [(ngModel)]="confirmDelete"
                class="mr-2 h-4 w-4">
              <span class="text-red-100">I understand data will be deleted</span>
            </label>
            <label class="flex items-center text-sm">
              <input 
                type="checkbox" 
                [(ngModel)]="confirmDevelopment"
                class="mr-2 h-4 w-4">
              <span class="text-red-100">This is for development only</span>
            </label>
          </div>

          <div class="flex space-x-2">
            <button 
              (click)="resetFirebase()"
              [disabled]="!confirmDelete || !confirmDevelopment"
              class="flex-1 bg-red-900 hover:bg-red-950 disabled:bg-gray-600 text-white font-bold py-2 px-3 rounded text-sm transition duration-200">
              DELETE ALL
            </button>
            <button 
              (click)="cancelReset()"
              class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-3 rounded text-sm transition duration-200">
              Cancel
            </button>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="loading">
          <h3 class="font-bold text-lg mb-3">🔄 RESETTING...</h3>
          <div class="w-full bg-red-800 rounded-full h-2 mb-2">
            <div class="bg-white h-2 rounded-full transition-all duration-300" 
                 [style.width.%]="progress"></div>
          </div>
          <p class="text-sm text-red-100">{{ progressMessage }}</p>
        </div>

        <!-- Result State -->
        <div *ngIf="resetResult">
          <h3 class="font-bold text-lg mb-3" 
              [class]="resetResult.success ? 'text-green-300' : 'text-red-300'">
            {{ resetResult.success ? '✅ SUCCESS' : '❌ FAILED' }}
          </h3>
          <p class="text-sm mb-3" 
             [class]="resetResult.success ? 'text-green-200' : 'text-red-200'">
            {{ resetResult.message }}
          </p>
          
          <div *ngIf="resetResult.success && resetResult.deletedCounts" class="mb-3">
            <p class="text-xs text-green-200">Deleted:</p>
            <div class="text-xs text-green-100 grid grid-cols-2 gap-1">
              <span>Users: {{ resetResult.deletedCounts.users }}</span>
              <span>Doctors: {{ resetResult.deletedCounts.doctors }}</span>
              <span>Patients: {{ resetResult.deletedCounts.patients }}</span>
              <span>Appointments: {{ resetResult.deletedCounts.appointments }}</span>
            </div>
          </div>

          <div class="flex space-x-2">
            <button 
              (click)="refreshPage()"
              class="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-3 rounded text-sm transition duration-200">
              🔄 Refresh
            </button>
            <button 
              (click)="resetState()"
              class="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-3 rounded text-sm transition duration-200">
              Reset Tool
            </button>
          </div>
        </div>

        <!-- Quick Actions (when minimized) -->
        <div *ngIf="isMinimized && !showConfirmation && !loading && !resetResult" class="mt-2">
          <button 
            (click)="clearUserState()"
            class="w-full bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-1 px-2 rounded text-xs transition duration-200">
            Clear User State
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .firebase-reset-button-container {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    input[type="checkbox"] {
      accent-color: #dc2626;
    }
    
    button:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
    
    @media (max-width: 768px) {
      .firebase-reset-button-container {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        margin: 1rem;
      }
    }
  `]
})
export class FirebaseResetButtonComponent {
  private firebaseDbService = inject(FirebaseDbService);

  isMinimized = false;
  showConfirmation = false;
  confirmDelete = false;
  confirmDevelopment = false;
  loading = false;
  progress = 0;
  progressMessage = '';
  resetResult: { success: boolean; message: string; deletedCounts?: any } | null = null;

  toggleVisibility(): void {
    this.isMinimized = !this.isMinimized;
  }

  resetFirebase(): void {
    if (!this.confirmDelete || !this.confirmDevelopment) {
      return;
    }

    this.loading = true;
    this.progress = 0;
    this.progressMessage = 'Initializing reset...';
    this.resetResult = null;

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      if (this.progress < 90) {
        this.progress += 15;
        this.updateProgressMessage();
      }
    }, 500);

    this.firebaseDbService.resetAllFirebaseData().subscribe({
      next: (result) => {
        clearInterval(progressInterval);
        this.progress = 100;
        this.progressMessage = 'Reset complete!';
        this.resetResult = result;
        this.loading = false;
        this.showConfirmation = false;
        this.confirmDelete = false;
        this.confirmDevelopment = false;
      },
      error: (error) => {
        clearInterval(progressInterval);
        this.loading = false;
        this.resetResult = {
          success: false,
          message: `Reset failed: ${error.message}`
        };
        this.showConfirmation = false;
        this.confirmDelete = false;
        this.confirmDevelopment = false;
      }
    });
  }

  private updateProgressMessage(): void {
    const messages = [
      'Connecting to Firebase...',
      'Fetching collections...',
      'Preparing batch deletion...',
      'Deleting user data...',
      'Deleting appointments...',
      'Deleting medical records...',
      'Finalizing reset...'
    ];
    const index = Math.floor(this.progress / 15);
    this.progressMessage = messages[index] || 'Processing...';
  }

  cancelReset(): void {
    this.showConfirmation = false;
    this.confirmDelete = false;
    this.confirmDevelopment = false;
  }

  resetState(): void {
    this.showConfirmation = false;
    this.confirmDelete = false;
    this.confirmDevelopment = false;
    this.loading = false;
    this.progress = 0;
    this.progressMessage = '';
    this.resetResult = null;
  }

  clearUserState(): void {
    this.firebaseDbService.clearCurrentUserState();
    alert('Current user state cleared! You may need to log in again.');
  }

  refreshPage(): void {
    window.location.reload();
  }
}
