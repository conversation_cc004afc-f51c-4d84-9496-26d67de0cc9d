import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FirebaseAppointmentService } from '../../services/firebase-appointment.service';
import { AvailabilityType } from '../../../../type';

@Component({
  selector: 'app-firebase-availability-debug',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="debug-container">
      <h2>Firebase Availability Debug Tool</h2>

      <div class="test-section">
        <h3>Add Test Availability</h3>
        <div class="form-group">
          <label>Doctor ID</label>
          <input type="text" [(ngModel)]="doctorId" class="form-control">
        </div>
        <div class="form-group">
          <label>Date (YYYY-MM-DD)</label>
          <input type="text" [(ngModel)]="date" class="form-control">
        </div>
        <div class="form-group">
          <label>Start Time (HH:MM)</label>
          <input type="text" [(ngModel)]="startTime" class="form-control">
        </div>
        <div class="form-group">
          <label>End Time (HH:MM)</label>
          <input type="text" [(ngModel)]="endTime" class="form-control">
        </div>
        <button (click)="addTestAvailability()">Add Availability</button>
        <div *ngIf="addResult" [class]="addResult.success ? 'success' : 'error'">
          {{ addResult.message }}
        </div>
      </div>

      <div class="test-section">
        <h3>View Availability for Doctor</h3>
        <div class="form-group">
          <label>Doctor ID</label>
          <input type="text" [(ngModel)]="viewDoctorId" class="form-control">
        </div>
        <button (click)="viewAvailability()">View Availability</button>
        <div *ngIf="viewResult" [class]="viewResult.success ? 'success' : 'error'">
          {{ viewResult.message }}
        </div>
        <div *ngIf="availabilitySlots.length > 0">
          <h4>Found {{ availabilitySlots.length }} availability slots:</h4>
          <div *ngFor="let slot of availabilitySlots" class="availability-slot">
            <div>Date: {{ slot.date }}</div>
            <div>Time: {{ slot.start_time }} to {{ slot.end_time }}</div>
            <div>ID: {{ slot.id }}</div>
            <button (click)="deleteAvailability(slot.id)" class="delete-btn">Delete</button>
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>Check Collections</h3>
        <div class="form-group">
          <label>Collection Name</label>
          <input type="text" [(ngModel)]="collectionName" class="form-control">
        </div>
        <button (click)="checkCollection()">Check Collection</button>
        <div *ngIf="collectionResult" [class]="collectionResult.success ? 'success' : 'error'">
          {{ collectionResult.message }}
        </div>
        <div *ngIf="collectionDocs.length > 0">
          <h4>Found {{ collectionDocs.length }} documents in collection:</h4>
          <pre>{{ collectionDocsSummary }}</pre>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .debug-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .test-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    label {
      display: block;
      margin-bottom: 5px;
    }

    .form-control {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    button {
      background-color: #4285F4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-bottom: 10px;
    }

    .delete-btn {
      background-color: #F44336;
      margin-top: 5px;
    }

    .success {
      color: #4CAF50;
      margin-top: 10px;
      padding: 10px;
      background-color: #E8F5E9;
      border-radius: 4px;
    }

    .error {
      color: #F44336;
      margin-top: 10px;
      padding: 10px;
      background-color: #FFEBEE;
      border-radius: 4px;
    }

    .availability-slot {
      padding: 10px;
      margin: 10px 0;
      background-color: #f5f5f5;
      border-radius: 4px;
    }

    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      max-height: 300px;
      overflow: auto;
    }
  `]
})
export class FirebaseAvailabilityDebugComponent implements OnInit {
  // Firebase Appointment Service
  private firebaseService = inject(FirebaseAppointmentService);

  // Add availability form fields
  doctorId = '';
  date = '';
  startTime = '';
  endTime = '';
  addResult: { success: boolean; message: string } | null = null;

  // View availability fields
  viewDoctorId = '';
  availabilitySlots: AvailabilityType[] = [];
  viewResult: { success: boolean; message: string } | null = null;

  // Collection check fields
  collectionName = 'doctorAvailability';
  collectionDocs: any[] = [];
  collectionDocsSummary = '';
  collectionResult: { success: boolean; message: string } | null = null;

  ngOnInit() {
    console.log('Firebase Availability Debug Component initialized');

    // Set default date to today
    const today = new Date();
    this.date = today.toISOString().split('T')[0];

    // Set default times
    this.startTime = '09:00';
    this.endTime = '10:00';
  }

  // Add a test availability slot
  addTestAvailability() {
    if (!this.doctorId || !this.date || !this.startTime || !this.endTime) {
      this.addResult = {
        success: false,
        message: 'Please fill out all fields'
      };
      return;
    }

    const availability = {
      doctor_id: this.doctorId,
      date: this.date,
      start_time: this.startTime,
      end_time: this.endTime
    };

    this.firebaseService.addAvailability(availability).subscribe({
      next: (id) => {
        this.addResult = {
          success: true,
          message: `Successfully added availability with ID: ${id}`
        };
      },
      error: (error) => {
        this.addResult = {
          success: false,
          message: `Error adding availability: ${error.message || error}`
        };
      }
    });
  }

  // View availability for a doctor
  viewAvailability() {
    if (!this.viewDoctorId) {
      this.viewResult = {
        success: false,
        message: 'Please enter a doctor ID'
      };
      return;
    }

    this.firebaseService.loadDoctorAvailability(this.viewDoctorId).subscribe({
      next: (slots) => {
        this.availabilitySlots = slots;
        this.viewResult = {
          success: true,
          message: `Loaded ${slots.length} availability slots for doctor ${this.viewDoctorId}`
        };
      },
      error: (error) => {
        this.viewResult = {
          success: false,
          message: `Error loading availability: ${error.message || error}`
        };
      }
    });
  }

  // Delete an availability slot
  deleteAvailability(id: string) {
    this.firebaseService.removeAvailability(id).subscribe({
      next: () => {
        this.availabilitySlots = this.availabilitySlots.filter(slot => slot.id !== id);
        this.viewResult = {
          success: true,
          message: `Successfully deleted availability slot with ID: ${id}`
        };
      },
      error: (error) => {
        this.viewResult = {
          success: false,
          message: `Error deleting availability: ${error.message || error}`
        };
      }
    });
  }

  // Check a collection in Firestore
  checkCollection() {
    if (!this.collectionName) {
      this.collectionResult = {
        success: false,
        message: 'Please enter a collection name'
      };
      return;
    }

    // This would be implemented using Firestore directly since it's a debug tool
    // For now, we'll just leverage the existing loadDoctorAvailability method
    if (this.collectionName === 'doctorAvailability') {
      this.firebaseService.loadDoctorAvailability('').subscribe({
        next: (slots) => {
          this.collectionDocs = slots;
          this.collectionDocsSummary = JSON.stringify(slots, null, 2);
          this.collectionResult = {
            success: true,
            message: `Found ${slots.length} documents in collection ${this.collectionName}`
          };
        },
        error: (error) => {
          this.collectionResult = {
            success: false,
            message: `Error checking collection: ${error.message || error}`
          };
        }
      });
    } else {
      this.collectionResult = {
        success: false,
        message: `Debug tool can only check the 'doctorAvailability' collection for now`
      };
    }
  }
}
